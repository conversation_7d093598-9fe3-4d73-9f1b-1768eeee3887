const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Verify Supabase Realtime Setup
 * This script checks if realtime is properly configured for user deletion detection
 */
async function verifyRealtimeSetup() {
  try {
    logger.info('🔍 Verifying Supabase Realtime setup...');
    
    const supabase = getSupabase();
    
    // Test 1: Basic realtime connection
    logger.info('📡 Testing basic realtime connection...');
    const testResult = await testRealtimeConnection(supabase);
    
    if (!testResult.success) {
      logger.error('❌ Basic realtime connection failed:', testResult.error);
      printRealtimeSetupGuide();
      return false;
    }
    
    logger.info('✅ Basic realtime connection successful');
    
    // Test 2: Check if users table exists
    logger.info('🗄️  Checking users table...');
    const tableResult = await checkUsersTable(supabase);
    
    if (!tableResult.success) {
      logger.error('❌ Users table check failed:', tableResult.error);
      return false;
    }
    
    logger.info('✅ Users table exists');
    
    // Test 3: Test postgres_changes subscription
    logger.info('🔄 Testing postgres_changes subscription...');
    const subscriptionResult = await testPostgresChangesSubscription(supabase);
    
    if (!subscriptionResult.success) {
      logger.error('❌ Postgres changes subscription failed:', subscriptionResult.error);
      printRealtimeSetupGuide();
      return false;
    }
    
    logger.info('✅ Postgres changes subscription successful');
    
    logger.info('🎉 All realtime setup checks passed!');
    return true;
    
  } catch (error) {
    logger.error('❌ Realtime setup verification failed:', error);
    printRealtimeSetupGuide();
    return false;
  }
}

async function testRealtimeConnection(supabase) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve({ success: false, error: 'Connection timeout' });
    }, 10000);

    const testChannel = supabase.channel('setup_test');
    
    testChannel.subscribe((status) => {
      clearTimeout(timeout);
      testChannel.unsubscribe();
      
      if (status === 'SUBSCRIBED') {
        resolve({ success: true });
      } else {
        resolve({ success: false, error: `Connection failed with status: ${status}` });
      }
    });
  });
}

async function checkUsersTable(supabase) {
  try {
    const { error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });

    if (error && error.code === 'PGRST116') {
      return { success: false, error: 'Users table does not exist' };
    }

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testPostgresChangesSubscription(supabase) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve({ success: false, error: 'Subscription timeout' });
    }, 15000);

    const testChannel = supabase
      .channel('postgres_test')
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'users'
        },
        () => {
          // This won't be called during test, but subscription should succeed
        }
      )
      .subscribe((status, err) => {
        clearTimeout(timeout);
        testChannel.unsubscribe();
        
        if (status === 'SUBSCRIBED') {
          resolve({ success: true });
        } else {
          resolve({ 
            success: false, 
            error: `Subscription failed with status: ${status}${err ? `, error: ${err}` : ''}` 
          });
        }
      });
  });
}

function printRealtimeSetupGuide() {
  logger.info('');
  logger.info('📋 SUPABASE REALTIME SETUP GUIDE');
  logger.info('================================');
  logger.info('');
  logger.info('To fix realtime issues, please check the following:');
  logger.info('');
  logger.info('1. 🔧 Enable Realtime for the users table:');
  logger.info('   - Go to your Supabase Dashboard');
  logger.info('   - Navigate to Database > Replication');
  logger.info('   - Find the "users" table');
  logger.info('   - Enable realtime replication');
  logger.info('');
  logger.info('2. 🔐 Check Row Level Security (RLS):');
  logger.info('   - Go to Database > Tables > users');
  logger.info('   - If RLS is enabled, ensure service role has proper access');
  logger.info('   - Or temporarily disable RLS for testing');
  logger.info('');
  logger.info('3. 🔑 Verify Service Role Key:');
  logger.info('   - Ensure SUPABASE_SERVICE_ROLE_KEY is set correctly');
  logger.info('   - Service role should have bypass RLS permissions');
  logger.info('');
  logger.info('4. 📊 Check Realtime Quotas:');
  logger.info('   - Go to Settings > Usage');
  logger.info('   - Verify you haven\'t exceeded realtime limits');
  logger.info('');
  logger.info('5. 🌐 Network/Firewall:');
  logger.info('   - Ensure WebSocket connections are allowed');
  logger.info('   - Check if corporate firewall blocks realtime');
  logger.info('');
  logger.info('After making changes, restart the server and run this verification again.');
  logger.info('');
}

// Run verification if called directly
if (require.main === module) {
  const { connectDB } = require('../config/database');
  
  connectDB().then(() => {
    verifyRealtimeSetup().then((success) => {
      process.exit(success ? 0 : 1);
    });
  }).catch((error) => {
    logger.error('Failed to connect to database:', error);
    process.exit(1);
  });
}

module.exports = { verifyRealtimeSetup };