/**
 * Switch Primary Provider to PluginNG
 * 
 * This script switches the primary VTU provider from VTpass to PluginNG
 * and updates the database accordingly.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { createClient } = require('@supabase/supabase-js');
const logger = require('../utils/logger');

async function switchToPluginNG() {
  try {
    console.log('🔄 Switching primary provider to PluginNG...');

    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase environment variables');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Check if provider_status table exists
    const { data: tables, error: tableError } = await supabase
      .from('provider_status')
      .select('provider_id, is_primary, is_enabled')
      .limit(1);

    if (tableError && tableError.code === 'PGRST116') {
      console.log('⚠️ Provider status table not found. Creating initial provider records...');
      
      // Insert initial provider records
      const { error: insertError } = await supabase
        .from('provider_status')
        .insert([
          {
            provider_id: 'pluginng',
            provider_name: 'PluginNG',
            is_primary: true,
            is_enabled: true,
            status: 'active',
            priority: 1,
            config: {
              baseUrl: process.env.PLUGINNG_BASE_URL || 'https://pluginng.com/api',
              timeout: 30000,
              retryDelay: 5000,
              costPerTransaction: 0.7,
              supportedServices: ['airtime'],
              maxConcurrentRequests: 50
            },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            provider_id: 'vtpass',
            provider_name: 'VTpass',
            is_primary: false,
            is_enabled: true,
            status: 'active',
            priority: 2,
            config: {
              baseUrl: process.env.VTPASS_BASE_URL || 'https://vtpass.com/api',
              timeout: 30000,
              retryDelay: 5000,
              costPerTransaction: 0.5,
              supportedServices: ['airtime', 'data'],
              maxConcurrentRequests: 100
            },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ]);

      if (insertError) {
        throw new Error(`Failed to create provider records: ${insertError.message}`);
      }

      console.log('✅ Initial provider records created successfully');
    } else {
      // Update existing records
      console.log('📊 Updating existing provider records...');

      // Set all providers to not primary
      await supabase
        .from('provider_status')
        .update({ is_primary: false, updated_at: new Date().toISOString() })
        .neq('provider_id', 'dummy'); // Update all records

      // Set PluginNG as primary
      const { error: pluginngError } = await supabase
        .from('provider_status')
        .upsert({
          provider_id: 'pluginng',
          provider_name: 'PluginNG',
          is_primary: true,
          is_enabled: true,
          status: 'active',
          priority: 1,
          config: {
            baseUrl: process.env.PLUGINNG_BASE_URL || 'https://pluginng.com/api',
            timeout: 30000,
            retryDelay: 5000,
            costPerTransaction: 0.7,
            supportedServices: ['airtime'],
            maxConcurrentRequests: 50
          },
          updated_at: new Date().toISOString()
        });

      if (pluginngError) {
        throw new Error(`Failed to set PluginNG as primary: ${pluginngError.message}`);
      }

      // Set VTpass as backup
      const { error: vtpassError } = await supabase
        .from('provider_status')
        .upsert({
          provider_id: 'vtpass',
          provider_name: 'VTpass',
          is_primary: false,
          is_enabled: true,
          status: 'active',
          priority: 2,
          config: {
            baseUrl: process.env.VTPASS_BASE_URL || 'https://vtpass.com/api',
            timeout: 30000,
            retryDelay: 5000,
            costPerTransaction: 0.5,
            supportedServices: ['airtime', 'data'],
            maxConcurrentRequests: 100
          },
          updated_at: new Date().toISOString()
        });

      if (vtpassError) {
        throw new Error(`Failed to set VTpass as backup: ${vtpassError.message}`);
      }

      console.log('✅ Provider records updated successfully');
    }

    // Verify the changes
    const { data: finalStatus, error: verifyError } = await supabase
      .from('provider_status')
      .select('provider_id, provider_name, is_primary, is_enabled, status, priority')
      .order('priority');

    if (verifyError) {
      throw new Error(`Failed to verify changes: ${verifyError.message}`);
    }

    console.log('\n📋 Final Provider Status:');
    finalStatus.forEach(provider => {
      const status = provider.is_primary ? '🥇 PRIMARY' : '🥈 BACKUP';
      const enabled = provider.is_enabled ? '✅ ENABLED' : '❌ DISABLED';
      console.log(`  ${provider.provider_name}: ${status} | ${enabled} | Priority: ${provider.priority}`);
    });

    console.log('\n🎉 Successfully switched to PluginNG as primary provider!');
    console.log('\n📝 Next Steps:');
    console.log('1. Restart the backend server to apply changes');
    console.log('2. Test airtime purchase through WhatsApp bot');
    console.log('3. Monitor logs for PluginNG transactions');
    console.log('4. VTpass is now available as backup provider');

  } catch (error) {
    console.error('❌ Failed to switch provider:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  // Load environment variables
  require('dotenv').config();
  switchToPluginNG();
}

module.exports = { switchToPluginNG };
