require('dotenv').config();
const { getSupabase } = require('../config/database');

/**
 * Check email delivery status from our logs
 */

async function checkEmailDelivery(email = null) {
  try {
    console.log('📊 Checking email delivery status...');
    
    const supabase = getSupabase();
    
    let query = supabase
      .from('email_logs')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (email) {
      query = query.eq('recipient', email);
      console.log(`🔍 Filtering for email: ${email}`);
    }
    
    const { data, error } = await query.limit(10);
    
    if (error) {
      console.error('❌ Error fetching email logs:', error);
      return;
    }
    
    if (!data || data.length === 0) {
      console.log('📭 No email logs found');
      return;
    }
    
    console.log(`📧 Found ${data.length} email log(s):`);
    console.log('='.repeat(80));
    
    data.forEach((log, index) => {
      console.log(`\n📧 Email ${index + 1}:`);
      console.log(`📧 To: ${log.recipient}`);
      console.log(`📧 Subject: ${log.subject}`);
      console.log(`📧 Status: ${log.status}`);
      console.log(`📧 Message ID: ${log.message_id || 'N/A'}`);
      console.log(`📧 Sent At: ${new Date(log.created_at).toLocaleString()}`);
      
      if (log.error_message) {
        console.log(`❌ Error: ${log.error_message}`);
      }
      
      if (log.message_id) {
        console.log(`🔗 SendGrid Activity: https://app.sendgrid.com/email_activity`);
        console.log(`   Search for Message ID: ${log.message_id}`);
      }
    });
    
    console.log('\n' + '='.repeat(80));
    console.log('💡 Troubleshooting Tips:');
    console.log('1. Check spam/junk folder');
    console.log('2. Search Gmail for "Vendy" or the OTP code');
    console.log('3. Check SendGrid Activity dashboard');
    console.log('4. Try a different email provider (Yahoo, Outlook)');
    console.log('5. Wait 1-5 minutes for delivery');
    
  } catch (error) {
    console.error('💥 Error checking email delivery:', error);
  }
}

// Get email from command line argument
const email = process.argv[2];

if (require.main === module) {
  checkEmailDelivery(email)
    .then(() => {
      console.log('\n✅ Email delivery check completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Check failed:', error);
      process.exit(1);
    });
}

module.exports = { checkEmailDelivery };
