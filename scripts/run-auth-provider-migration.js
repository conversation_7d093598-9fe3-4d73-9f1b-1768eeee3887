/**
 * Run Auth Provider Migration
 * Adds auth_provider and Google authentication columns to users table
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

async function runAuthProviderMigration() {
  try {
    console.log('🚀 Starting auth provider migration...');
    
    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Read migration SQL
    const migrationPath = path.join(__dirname, '../migrations/fix_auth_provider_column.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Executing migration SQL...');
    
    // Try to execute the migration using rpc
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });
    
    if (error) {
      console.log('⚠️  RPC execution failed, trying manual approach...');
      
      // Manual approach - execute individual statements
      const statements = [
        // Add auth_provider column
        `DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                AND column_name = 'auth_provider'
            ) THEN
                ALTER TABLE users ADD COLUMN auth_provider VARCHAR(50) DEFAULT 'phone';
            END IF;
        END $$;`,
        
        // Add google_uid column
        `DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                AND column_name = 'google_uid'
            ) THEN
                ALTER TABLE users ADD COLUMN google_uid VARCHAR(255) UNIQUE;
            END IF;
        END $$;`,
        
        // Add provider_id column
        `DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                AND column_name = 'provider_id'
            ) THEN
                ALTER TABLE users ADD COLUMN provider_id VARCHAR(255);
            END IF;
        END $$;`,
        
        // Add picture column
        `DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                AND column_name = 'picture'
            ) THEN
                ALTER TABLE users ADD COLUMN picture TEXT;
            END IF;
        END $$;`,
        
        // Update existing users
        `UPDATE users SET auth_provider = 'phone' WHERE auth_provider IS NULL;`,
        
        // Make phone_number nullable
        `ALTER TABLE users ALTER COLUMN phone_number DROP NOT NULL;`
      ];
      
      for (const statement of statements) {
        try {
          console.log(`📝 Executing: ${statement.substring(0, 50)}...`);
          const { error: stmtError } = await supabase.rpc('exec_sql', {
            sql: statement
          });
          
          if (stmtError) {
            console.log('⚠️  Statement failed:', stmtError.message);
          } else {
            console.log('✅ Statement executed successfully');
          }
        } catch (err) {
          console.log('⚠️  Statement execution failed:', err.message);
        }
      }
    } else {
      console.log('✅ Migration executed successfully');
      console.log('📊 Result:', data);
    }
    
    // Verify the columns were added
    console.log('🔍 Verifying migration...');
    
    const { data: tableInfo, error: infoError } = await supabase
      .from('users')
      .select('*')
      .limit(1);
    
    if (infoError) {
      console.log('⚠️  Could not verify migration:', infoError.message);
    } else {
      console.log('✅ Migration verification completed');
    }
    
    console.log('\n🎉 Auth provider migration completed!');
    console.log('✅ Your backend is now ready for Google authentication');
    console.log('📝 Changes made:');
    console.log('   - Added auth_provider column (default: phone)');
    console.log('   - Added google_uid column for Google users');
    console.log('   - Added provider_id column');
    console.log('   - Added picture column for profile photos');
    console.log('   - Made phone_number nullable for Google users');
    
    return true;
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    console.log('\n💡 Manual Setup Instructions:');
    console.log('=============================');
    console.log('Please run this SQL manually in your Supabase dashboard:');
    console.log('1. Go to your Supabase project dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the contents of:');
    console.log('   backend/migrations/fix_auth_provider_column.sql');
    console.log('4. Click Run to execute the script');
    console.log('5. Restart your backend server');
    
    return false;
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runAuthProviderMigration()
    .then((success) => {
      if (success) {
        console.log('\n🚀 Ready to test Google authentication!');
        process.exit(0);
      } else {
        console.log('\n⚠️  Migration needs to be completed manually');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { runAuthProviderMigration };
