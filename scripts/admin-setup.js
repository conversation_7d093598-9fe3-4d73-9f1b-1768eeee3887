#!/usr/bin/env node

/**
 * Admin User Setup Script for Vendy
 * Creates admin users for the admin dashboard
 */

const { connectDB, getSupabase } = require('../config/database');
const bcrypt = require('bcryptjs');

async function checkAdminUsers() {
  try {
    console.log('🔍 Connecting to database...');
    await connectDB();
    
    const supabase = getSupabase();
    
    console.log('👥 Checking for admin users...');
    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, phone_number, role, is_active, created_at')
      .eq('role', 'admin');
    
    if (error) {
      console.log('❌ Error:', error.message);
      return;
    }
    
    console.log(`\n📊 Found ${users.length} admin user(s):\n`);
    
    if (users.length === 0) {
      console.log('❌ No admin users found in the database.');
      console.log('🔧 You need to create an admin user first.');
      console.log('\n💡 To create an admin user, run:');
      console.log('   node scripts/admin-setup.<NAME_EMAIL> 123456');
    } else {
      users.forEach((user, index) => {
        console.log(`${index + 1}. Admin User:`);
        console.log(`   📧 Email: ${user.email || 'Not set'}`);
        console.log(`   📱 Phone: ${user.phone_number || 'Not set'}`);
        console.log(`   ✅ Active: ${user.is_active ? 'Yes' : 'No'}`);
        console.log(`   🆔 ID: ${user.id}`);
        console.log(`   📅 Created: ${new Date(user.created_at).toLocaleString()}`);
        console.log('');
      });
      
      console.log('🔐 Login Instructions:');
      console.log('   1. Go to: https://admin.payvendy.name.ng');
      console.log('   2. Enter your email address');
      console.log('   3. Enter your PIN (the PIN you set when creating the admin user)');
      console.log('   4. Click "Sign In"');
    }
    
  } catch (error) {
    console.log('❌ Error checking admin users:', error.message);
  }
  
  process.exit(0);
}

async function createAdminUser(email, pin, phoneNumber = null) {
  try {
    console.log('🔍 Connecting to database...');
    await connectDB();
    
    const supabase = getSupabase();
    
    // Validate PIN
    if (!/^\d{4,6}$/.test(pin)) {
      console.log('❌ PIN must be 4-6 digits');
      return;
    }
    
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id, email')
      .eq('email', email)
      .single();
    
    if (existingUser) {
      console.log('❌ User with this email already exists');
      return;
    }
    
    // Hash PIN
    const hashedPin = await bcrypt.hash(pin, 12);
    
    // Generate a phone number if not provided (required field)
    const adminPhone = phoneNumber || `0901${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`;
    
    // Create admin user
    const { data: newUser, error } = await supabase
      .from('users')
      .insert([{
        email: email,
        phone_number: adminPhone,
        pin: hashedPin,
        role: 'admin',
        is_active: true,
        is_phone_verified: true,
        is_email_verified: true,
        first_name: 'Admin',
        last_name: 'User',
        balance: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();
    
    if (error) {
      console.log('❌ Error creating admin user:', error.message);
      return;
    }
    
    console.log('✅ Admin user created successfully!');
    console.log(`📧 Email: ${newUser.email}`);
    console.log(`📱 Phone: ${newUser.phone_number}`);
    console.log(`🆔 ID: ${newUser.id}`);
    console.log('\n🔐 Login Details:');
    console.log(`   Dashboard: https://admin.payvendy.name.ng`);
    console.log(`   Email: ${email}`);
    console.log(`   PIN: ${pin}`);
    console.log('\n📝 Next Steps:');
    console.log('   1. Visit https://admin.payvendy.name.ng');
    console.log('   2. Login with the credentials above');
    console.log('   3. Start managing your OTA updates!');
    
  } catch (error) {
    console.log('❌ Error creating admin user:', error.message);
  }
  
  process.exit(0);
}

async function updateAdminPin(email, newPin) {
  try {
    console.log('🔍 Connecting to database...');
    await connectDB();
    
    const supabase = getSupabase();
    
    // Validate PIN
    if (!/^\d{4,6}$/.test(newPin)) {
      console.log('❌ PIN must be 4-6 digits');
      return;
    }
    
    // Find admin user
    const { data: user, error: findError } = await supabase
      .from('users')
      .select('id, email, role')
      .eq('email', email)
      .eq('role', 'admin')
      .single();
    
    if (findError || !user) {
      console.log('❌ Admin user not found');
      return;
    }
    
    // Hash new PIN
    const hashedPin = await bcrypt.hash(newPin, 12);
    
    // Update PIN
    const { error: updateError } = await supabase
      .from('users')
      .update({
        pin: hashedPin,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);
    
    if (updateError) {
      console.log('❌ Error updating PIN:', updateError.message);
      return;
    }
    
    console.log('✅ Admin PIN updated successfully!');
    console.log(`📧 Email: ${email}`);
    console.log(`🔐 New PIN: ${newPin}`);
    
  } catch (error) {
    console.log('❌ Error updating admin PIN:', error.message);
  }
  
  process.exit(0);
}

// Command line interface
const args = process.argv.slice(2);
const command = args[0];

if (command === 'create') {
  const email = args[1];
  const pin = args[2];
  const phone = args[3];
  
  if (!email || !pin) {
    console.log('Usage: node scripts/admin-setup.js create <email> <pin> [phone]');
    console.log('Example: node scripts/admin-setup.<NAME_EMAIL> 123456');
    console.log('Note: PIN must be 4-6 digits');
    process.exit(1);
  }
  
  createAdminUser(email, pin, phone);
} else if (command === 'update-pin') {
  const email = args[1];
  const newPin = args[2];
  
  if (!email || !newPin) {
    console.log('Usage: node scripts/admin-setup.js update-pin <email> <new-pin>');
    console.log('Example: node scripts/admin-setup.js update-pin <EMAIL> 654321');
    process.exit(1);
  }
  
  updateAdminPin(email, newPin);
} else {
  console.log('🔍 Checking existing admin users...\n');
  checkAdminUsers();
}

module.exports = {
  checkAdminUsers,
  createAdminUser,
  updateAdminPin
};
