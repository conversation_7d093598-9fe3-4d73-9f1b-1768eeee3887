const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function runPinMigration() {
  try {
    console.log('🔄 Running PIN migration...');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Supabase URL and Service Role Key are required in environment variables');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    console.log('📝 Making PIN column nullable...');
    
    // Execute the migration directly
    const { data, error } = await supabase.rpc('exec', {
      sql: 'ALTER TABLE users ALTER COLUMN pin DROP NOT NULL;'
    });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      console.log('💡 Please run this SQL manually in your Supabase dashboard:');
      console.log('   ALTER TABLE users ALTER COLUMN pin DROP NOT NULL;');
      process.exit(1);
    }
    
    console.log('✅ PIN migration completed successfully!');
    console.log('📝 Changes made:');
    console.log('   - PIN column is now nullable');
    console.log('   - New users can be created without a PIN');
    
  } catch (error) {
    console.error('💥 Error running migration:', error);
    console.log('💡 Please run this SQL manually in your Supabase dashboard:');
    console.log('   ALTER TABLE users ALTER COLUMN pin DROP NOT NULL;');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  runPinMigration();
}

module.exports = { runPinMigration };