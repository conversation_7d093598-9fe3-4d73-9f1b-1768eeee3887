/**
 * PluginNG Setup Verification Script
 * 
 * Quick verification script to check if PluginNG integration is properly set up
 * and ready for production use.
 * 
 * Usage:
 * node scripts/verify-pluginng-setup.js
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

require('dotenv').config();

async function verifySetup() {
  console.log('🔍 Verifying PluginNG Integration Setup...\n');

  let allGood = true;

  // 1. Check environment variables
  console.log('1. Checking environment variables...');
  const requiredEnvVars = [
    'PLUGINNG_EMAIL',
    'PLUGINNG_PASSWORD'
  ];

  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      console.log(`   ✅ ${envVar} is set`);
    } else {
      console.log(`   ❌ ${envVar} is missing`);
      allGood = false;
    }
  }

  // 2. Check configuration loading
  console.log('\n2. Checking configuration loading...');
  try {
    const pluginNGConfig = require('../config/pluginng');
    console.log('   ✅ PluginNG configuration loaded successfully');
    
    const networks = pluginNGConfig.getSupportedNetworks();
    console.log(`   ✅ ${networks.length} networks configured`);
  } catch (error) {
    console.log(`   ❌ Configuration loading failed: ${error.message}`);
    allGood = false;
  }

  // 3. Check service loading
  console.log('\n3. Checking service loading...');
  try {
    const pluginNGService = require('../services/pluginngService');
    console.log('   ✅ PluginNG service loaded successfully');
  } catch (error) {
    console.log(`   ❌ Service loading failed: ${error.message}`);
    allGood = false;
  }

  // 4. Check provider manager integration
  console.log('\n4. Checking provider manager integration...');
  try {
    const vtuProviderManager = require('../services/vtuProviderManager');
    const providers = vtuProviderManager.getActiveProviders();
    const pluginngProvider = providers.find(p => p.id === 'pluginng');
    
    if (pluginngProvider) {
      console.log('   ✅ PluginNG provider registered');
      console.log(`   ✅ Priority: ${pluginngProvider.priority}`);
      console.log(`   ✅ Status: ${pluginngProvider.status}`);
    } else {
      console.log('   ❌ PluginNG provider not found');
      allGood = false;
    }
  } catch (error) {
    console.log(`   ❌ Provider manager check failed: ${error.message}`);
    allGood = false;
  }

  // 5. Check error handler
  console.log('\n5. Checking error handler...');
  try {
    const { PluginNGErrorHandler } = require('../utils/pluginngErrorHandler');
    console.log('   ✅ PluginNG error handler loaded successfully');
  } catch (error) {
    console.log(`   ❌ Error handler loading failed: ${error.message}`);
    allGood = false;
  }

  // 6. Check database schema files
  console.log('\n6. Checking database schema files...');
  try {
    const fs = require('fs');
    const schemaFile = './database/pluginng-schema-extensions.sql';
    
    if (fs.existsSync(schemaFile)) {
      console.log('   ✅ PluginNG schema extensions file exists');
    } else {
      console.log('   ❌ PluginNG schema extensions file missing');
      allGood = false;
    }
  } catch (error) {
    console.log(`   ❌ Schema file check failed: ${error.message}`);
    allGood = false;
  }

  // 7. Check documentation
  console.log('\n7. Checking documentation...');
  try {
    const fs = require('fs');
    const setupGuide = './docs/PLUGINNG_SETUP_GUIDE.md';
    const apiDocs = './docs/PLUGINNG_API_DOCUMENTATION.md';
    
    if (fs.existsSync(setupGuide)) {
      console.log('   ✅ Setup guide exists');
    } else {
      console.log('   ❌ Setup guide missing');
    }
    
    if (fs.existsSync(apiDocs)) {
      console.log('   ✅ API documentation exists');
    } else {
      console.log('   ❌ API documentation missing');
    }
  } catch (error) {
    console.log(`   ❌ Documentation check failed: ${error.message}`);
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  if (allGood) {
    console.log('🎉 PluginNG integration setup is COMPLETE!');
    console.log('\nNext steps:');
    console.log('1. Run database schema extensions in Supabase');
    console.log('2. Test with actual API calls');
    console.log('3. Monitor logs for any issues');
    console.log('4. Set up monitoring and alerts');
  } else {
    console.log('❌ Setup is INCOMPLETE. Please fix the issues above.');
  }
  console.log('='.repeat(50));

  return allGood;
}

// Run verification if this file is executed directly
if (require.main === module) {
  verifySetup().catch(error => {
    console.error('Verification error:', error);
    process.exit(1);
  });
}

module.exports = { verifySetup };
