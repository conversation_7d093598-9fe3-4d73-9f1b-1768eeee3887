const fs = require('fs');
const path = require('path');
const { getSupabase } = require('../config/database');

async function runCrashReportsMigration() {
  try {
    console.log('🚀 Starting crash reports table migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../migrations/create_crash_reports_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    const supabase = getSupabase();
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          // Try direct query if RPC fails
          const { error: directError } = await supabase
            .from('_temp_migration')
            .select('*')
            .limit(0);
          
          if (directError && directError.message.includes('does not exist')) {
            // Table doesn't exist, which is expected for some statements
            console.log(`⚠️  Statement ${i + 1} skipped (expected for new installation)`);
          } else {
            throw error;
          }
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (statementError) {
        // Some statements might fail if objects already exist
        if (statementError.message.includes('already exists') || 
            statementError.message.includes('does not exist')) {
          console.log(`⚠️  Statement ${i + 1} skipped (object already exists or doesn't exist)`);
        } else {
          console.error(`❌ Error in statement ${i + 1}:`, statementError.message);
          throw statementError;
        }
      }
    }
    
    // Verify the table was created
    console.log('🔍 Verifying crash_reports table...');
    const { data: tableInfo, error: tableError } = await supabase
      .from('crash_reports')
      .select('*')
      .limit(1);
    
    if (tableError) {
      throw new Error(`Table verification failed: ${tableError.message}`);
    }
    
    console.log('✅ crash_reports table verified successfully');
    
    // Test the views
    console.log('🔍 Verifying views...');
    try {
      const { error: statsError } = await supabase
        .from('crash_report_stats')
        .select('*')
        .limit(1);
      
      if (!statsError) {
        console.log('✅ crash_report_stats view verified');
      }
      
      const { error: criticalError } = await supabase
        .from('recent_critical_crashes')
        .select('*')
        .limit(1);
      
      if (!criticalError) {
        console.log('✅ recent_critical_crashes view verified');
      }
    } catch (viewError) {
      console.log('⚠️  Views verification skipped (may not be supported)');
    }
    
    console.log('🎉 Crash reports migration completed successfully!');
    console.log('');
    console.log('📊 Available endpoints:');
    console.log('  POST /api/v1/crash-reports - Submit crash report');
    console.log('  GET  /api/v1/crash-reports - List crash reports (admin)');
    console.log('  GET  /api/v1/crash-reports/:id - Get crash report details (admin)');
    console.log('  DELETE /api/v1/crash-reports/:id - Delete crash report (admin)');
    console.log('');
    console.log('📈 Available views:');
    console.log('  crash_report_stats - Aggregated statistics');
    console.log('  recent_critical_crashes - Recent critical crashes');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Alternative method using raw SQL execution
async function runMigrationDirect() {
  try {
    console.log('🚀 Running migration with direct SQL execution...');
    
    const migrationPath = path.join(__dirname, '../migrations/create_crash_reports_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    const supabase = getSupabase();
    
    // Execute the entire migration as one block
    const { error } = await supabase.rpc('exec_migration', { 
      migration_sql: migrationSQL 
    });
    
    if (error) {
      throw error;
    }
    
    console.log('✅ Migration executed successfully');
    
  } catch (error) {
    console.error('❌ Direct migration failed:', error.message);
    // Fall back to statement-by-statement execution
    console.log('🔄 Falling back to statement-by-statement execution...');
    await runCrashReportsMigration();
  }
}

// Run the migration
if (require.main === module) {
  console.log('🗄️  Crash Reports Migration Tool');
  console.log('=====================================');
  
  // Try direct method first, fall back to statement-by-statement
  runMigrationDirect().catch(() => {
    console.log('🔄 Trying alternative migration method...');
    runCrashReportsMigration();
  });
}

module.exports = { runCrashReportsMigration, runMigrationDirect };
