#!/usr/bin/env node

/**
 * Simple Admin User Creation Script
 * Creates an admin user directly via SQL
 */

const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

async function createAdminUser() {
  const email = '<EMAIL>';
  const pin = '123456';
  const phoneNumber = '09012345678';
  
  try {
    // Hash the PIN
    const hashedPin = await bcrypt.hash(pin, 12);
    const userId = uuidv4();
    const now = new Date().toISOString();
    
    // Generate SQL insert statement
    const sqlStatement = `
INSERT INTO users (
  id, 
  email, 
  phone_number, 
  pin, 
  role, 
  is_active, 
  is_phone_verified, 
  is_email_verified,
  first_name,
  last_name,
  balance,
  created_at, 
  updated_at
) VALUES (
  '${userId}',
  '${email}',
  '${phoneNumber}',
  '${hashedPin}',
  'admin',
  true,
  true,
  true,
  'Admin',
  'User',
  0,
  '${now}',
  '${now}'
) ON CONFLICT (email) DO NOTHING;
`;

    console.log('🔐 Admin User Creation SQL:');
    console.log('Copy and paste this SQL into your database:');
    console.log('');
    console.log(sqlStatement);
    console.log('');
    console.log('🔑 Login Credentials:');
    console.log(`   Dashboard: https://admin.payvendy.name.ng`);
    console.log(`   Email: ${email}`);
    console.log(`   PIN: ${pin}`);
    console.log('');
    console.log('📝 Instructions:');
    console.log('1. Run the SQL statement above in your database');
    console.log('2. Visit https://admin.payvendy.name.ng');
    console.log('3. Login with the credentials above');
    
  } catch (error) {
    console.log('❌ Error generating admin user:', error.message);
  }
}

createAdminUser();
