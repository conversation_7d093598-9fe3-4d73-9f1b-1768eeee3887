/**
 * PluginNG Integration Test Script
 * 
 * Comprehensive test suite for PluginNG backup provider integration
 * Tests configuration, service functionality, failover logic, and error handling
 * 
 * Usage:
 * node scripts/test-pluginng-integration.js
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

require('dotenv').config();
const pluginNGConfig = require('../config/pluginng');
const pluginNGService = require('../services/pluginngService');
const vtuProviderManager = require('../services/vtuProviderManager');
const { PluginNGErrorHandler } = require('../utils/pluginngErrorHandler');
const logger = require('../utils/logger');

/**
 * Test Results Tracker
 */
class TestTracker {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
  }

  addTest(name, passed, error = null) {
    this.tests.push({ name, passed, error });
    if (passed) {
      this.passed++;
      console.log(`✅ ${name}`);
    } else {
      this.failed++;
      console.log(`❌ ${name}: ${error?.message || 'Unknown error'}`);
    }
  }

  summary() {
    console.log('\n' + '='.repeat(60));
    console.log('TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.tests.length}`);
    console.log(`Passed: ${this.passed}`);
    console.log(`Failed: ${this.failed}`);
    console.log(`Success Rate: ${((this.passed / this.tests.length) * 100).toFixed(1)}%`);
    
    if (this.failed > 0) {
      console.log('\nFailed Tests:');
      this.tests.filter(t => !t.passed).forEach(t => {
        console.log(`- ${t.name}: ${t.error?.message || 'Unknown error'}`);
      });
    }
    
    console.log('='.repeat(60));
    return this.failed === 0;
  }
}

/**
 * Test Configuration
 */
async function testConfiguration() {
  const tracker = new TestTracker();
  
  console.log('\n🔧 Testing PluginNG Configuration...');
  
  // Test 1: Environment variables
  try {
    const config = pluginNGConfig.getConfig();
    tracker.addTest('Environment variables loaded', !!config.baseUrl);
  } catch (error) {
    tracker.addTest('Environment variables loaded', false, error);
  }

  // Test 2: Supported networks
  try {
    const networks = pluginNGConfig.getSupportedNetworks();
    tracker.addTest('Supported networks configured', networks.length === 4);
  } catch (error) {
    tracker.addTest('Supported networks configured', false, error);
  }

  // Test 3: Phone validation - MTN
  try {
    const result = pluginNGConfig.validatePhone('08012345678');
    tracker.addTest('MTN phone validation', result.isValid && result.network === 'mtn');
  } catch (error) {
    tracker.addTest('MTN phone validation', false, error);
  }

  // Test 4: Phone validation - GLO
  try {
    const result = pluginNGConfig.validatePhone('08051234567');
    tracker.addTest('GLO phone validation', result.isValid && result.network === 'glo');
  } catch (error) {
    tracker.addTest('GLO phone validation', false, error);
  }

  // Test 5: Phone validation - Airtel
  try {
    const result = pluginNGConfig.validatePhone('08021234567');
    tracker.addTest('Airtel phone validation', result.isValid && result.network === 'airtel');
  } catch (error) {
    tracker.addTest('Airtel phone validation', false, error);
  }

  // Test 6: Phone validation - 9mobile
  try {
    const result = pluginNGConfig.validatePhone('08091234567');
    tracker.addTest('9mobile phone validation', result.isValid && result.network === 'etisalat');
  } catch (error) {
    tracker.addTest('9mobile phone validation', false, error);
  }

  // Test 7: Invalid phone validation
  try {
    const result = pluginNGConfig.validatePhone('1234567890');
    tracker.addTest('Invalid phone rejection', !result.isValid);
  } catch (error) {
    tracker.addTest('Invalid phone rejection', false, error);
  }

  // Test 8: Amount validation - valid
  try {
    const result = pluginNGConfig.validateAmount(100);
    tracker.addTest('Valid amount acceptance', result.isValid);
  } catch (error) {
    tracker.addTest('Valid amount acceptance', false, error);
  }

  // Test 9: Amount validation - too low
  try {
    const result = pluginNGConfig.validateAmount(10);
    tracker.addTest('Low amount rejection', !result.isValid);
  } catch (error) {
    tracker.addTest('Low amount rejection', false, error);
  }

  // Test 10: Amount validation - too high
  try {
    const result = pluginNGConfig.validateAmount(100000);
    tracker.addTest('High amount rejection', !result.isValid);
  } catch (error) {
    tracker.addTest('High amount rejection', false, error);
  }

  return tracker.summary();
}

/**
 * Test Service Functionality
 */
async function testServiceFunctionality() {
  const tracker = new TestTracker();
  
  console.log('\n🔧 Testing PluginNG Service...');

  // Test 1: Service initialization
  try {
    const service = pluginNGService;
    tracker.addTest('Service initialization', !!service);
  } catch (error) {
    tracker.addTest('Service initialization', false, error);
  }

  // Test 2: Error categorization
  try {
    const error = new Error('Network timeout');
    const category = pluginNGService.categorizeError(error);
    tracker.addTest('Error categorization', category === 'network_error');
  } catch (error) {
    tracker.addTest('Error categorization', false, error);
  }

  // Test 3: Authentication error categorization
  try {
    const error = { response: { status: 401 } };
    const category = pluginNGService.categorizeError(error);
    tracker.addTest('Auth error categorization', category === 'authentication_error');
  } catch (error) {
    tracker.addTest('Auth error categorization', false, error);
  }

  return tracker.summary();
}

/**
 * Test Error Handler
 */
async function testErrorHandler() {
  const tracker = new TestTracker();
  
  console.log('\n🔧 Testing PluginNG Error Handler...');

  // Test 1: HTTP error handling
  try {
    const error = {
      response: { status: 400, data: { message: 'Invalid request' } },
      message: 'Bad Request'
    };
    const result = PluginNGErrorHandler.handleError(error);
    tracker.addTest('HTTP error handling', result.category === 'validation_error');
  } catch (error) {
    tracker.addTest('HTTP error handling', false, error);
  }

  // Test 2: Generic error handling
  try {
    const error = new Error('Phone number is invalid');
    const result = PluginNGErrorHandler.handleError(error);
    tracker.addTest('Generic error handling', result.category === 'invalid_phone');
  } catch (error) {
    tracker.addTest('Generic error handling', false, error);
  }

  // Test 3: Retry logic
  try {
    const error = { response: { status: 500 } };
    const isRetryable = PluginNGErrorHandler.isRetryable(error);
    tracker.addTest('Retry logic for server errors', isRetryable);
  } catch (error) {
    tracker.addTest('Retry logic for server errors', false, error);
  }

  // Test 4: Non-retryable errors
  try {
    const error = { response: { status: 401 } };
    const isRetryable = PluginNGErrorHandler.isRetryable(error);
    tracker.addTest('Non-retryable auth errors', !isRetryable);
  } catch (error) {
    tracker.addTest('Non-retryable auth errors', false, error);
  }

  // Test 5: Retry delay calculation
  try {
    const error = { response: { status: 500 } };
    const delay = PluginNGErrorHandler.getRetryDelay(error, 1);
    tracker.addTest('Retry delay calculation', delay > 0 && delay <= 30000);
  } catch (error) {
    tracker.addTest('Retry delay calculation', false, error);
  }

  return tracker.summary();
}

/**
 * Test Provider Manager Integration
 */
async function testProviderManager() {
  const tracker = new TestTracker();
  
  console.log('\n🔧 Testing VTU Provider Manager Integration...');

  // Test 1: PluginNG provider registration
  try {
    const providers = vtuProviderManager.getActiveProviders();
    const pluginngProvider = providers.find(p => p.id === 'pluginng');
    tracker.addTest('PluginNG provider registered', !!pluginngProvider);
  } catch (error) {
    tracker.addTest('PluginNG provider registered', false, error);
  }

  // Test 2: Provider priority
  try {
    const providers = vtuProviderManager.getActiveProviders();
    const pluginngProvider = providers.find(p => p.id === 'pluginng');
    tracker.addTest('PluginNG backup priority', pluginngProvider?.priority === 'backup');
  } catch (error) {
    tracker.addTest('PluginNG backup priority', false, error);
  }

  // Test 3: Provider capabilities
  try {
    const providers = vtuProviderManager.getActiveProviders();
    const pluginngProvider = providers.find(p => p.id === 'pluginng');
    tracker.addTest('PluginNG airtime capability', pluginngProvider?.capabilities?.airtime === true);
  } catch (error) {
    tracker.addTest('PluginNG airtime capability', false, error);
  }

  // Test 4: Supported networks
  try {
    const providers = vtuProviderManager.getActiveProviders();
    const pluginngProvider = providers.find(p => p.id === 'pluginng');
    const supportedNetworks = pluginngProvider?.capabilities?.supportedNetworks || [];
    tracker.addTest('PluginNG network support', supportedNetworks.length === 4);
  } catch (error) {
    tracker.addTest('PluginNG network support', false, error);
  }

  return tracker.summary();
}

/**
 * Test Database Schema
 */
async function testDatabaseSchema() {
  const tracker = new TestTracker();
  
  console.log('\n🔧 Testing Database Schema...');

  // Note: These tests would require actual database connection
  // For now, we'll test the schema files exist and are valid

  // Test 1: Schema file exists
  try {
    const fs = require('fs');
    const schemaExists = fs.existsSync('./database/pluginng-schema-extensions.sql');
    tracker.addTest('PluginNG schema file exists', schemaExists);
  } catch (error) {
    tracker.addTest('PluginNG schema file exists', false, error);
  }

  return tracker.summary();
}

/**
 * Test Failover Simulation
 */
async function testFailoverSimulation() {
  const tracker = new TestTracker();
  
  console.log('\n🔧 Testing Failover Logic...');

  // Test 1: Provider selection logic
  try {
    const providers = vtuProviderManager.getActiveProviders();
    const primaryProvider = providers.find(p => p.priority === 'primary');
    const backupProvider = providers.find(p => p.priority === 'backup');
    
    tracker.addTest('Primary provider exists', !!primaryProvider);
    tracker.addTest('Backup provider exists', !!backupProvider);
    tracker.addTest('PluginNG is backup', backupProvider?.id === 'pluginng');
  } catch (error) {
    tracker.addTest('Provider selection logic', false, error);
  }

  return tracker.summary();
}

/**
 * Main Test Runner
 */
async function runTests() {
  console.log('🚀 Starting PluginNG Integration Tests...');
  console.log('='.repeat(60));

  const results = [];

  // Run all test suites
  results.push(await testConfiguration());
  results.push(await testServiceFunctionality());
  results.push(await testErrorHandler());
  results.push(await testProviderManager());
  results.push(await testDatabaseSchema());
  results.push(await testFailoverSimulation());

  // Overall summary
  const allPassed = results.every(result => result === true);
  
  console.log('\n' + '='.repeat(60));
  console.log('OVERALL TEST RESULTS');
  console.log('='.repeat(60));
  
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! PluginNG integration is ready.');
    console.log('\nNext steps:');
    console.log('1. Add your PluginNG API token to .env file');
    console.log('2. Run database schema extensions');
    console.log('3. Test with actual API calls');
    console.log('4. Monitor logs for any issues');
  } else {
    console.log('❌ Some tests failed. Please review and fix issues before deployment.');
  }
  
  console.log('='.repeat(60));
  
  process.exit(allPassed ? 0 : 1);
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testConfiguration,
  testServiceFunctionality,
  testErrorHandler,
  testProviderManager,
  testDatabaseSchema,
  testFailoverSimulation
};
