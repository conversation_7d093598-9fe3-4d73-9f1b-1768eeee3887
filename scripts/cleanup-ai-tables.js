/**
 * PayVendy AI Tables Cleanup Script
 * 
 * Removes all AI-related database tables and functions from the database.
 * This script should be run after removing AI functionality from the backend.
 */

const fs = require('fs').promises;
const path = require('path');
const pool = require('../config/database');
const logger = require('../utils/logger');

async function cleanupAITables() {
    console.log('🧹 Starting AI tables cleanup...');
    
    try {
        // List of AI-related tables to drop
        const aiTables = [
            'ai_logs',
            'behavior_analytics', 
            'user_segments',
            'reward_queue',
            'realtime_events',
            'fraud_alerts',
            'ai_configuration'
        ];

        // List of AI-related functions to drop
        const aiFunctions = [
            'apply_reward',
            'cleanup_old_events',
            'cleanup_old_ai_data'
        ];

        console.log('📋 Checking which AI tables exist...');
        
        // Check which tables exist
        const existingTablesResult = await pool.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = ANY($1)
            ORDER BY table_name
        `, [aiTables]);

        const existingTables = existingTablesResult.rows.map(row => row.table_name);
        
        if (existingTables.length === 0) {
            console.log('✅ No AI tables found in database');
        } else {
            console.log(`📊 Found ${existingTables.length} AI tables to remove:`);
            existingTables.forEach(table => {
                console.log(`  • ${table}`);
            });

            // Drop tables (with CASCADE to handle dependencies)
            for (const table of existingTables) {
                console.log(`🗑️  Dropping table: ${table}`);
                await pool.query(`DROP TABLE IF EXISTS ${table} CASCADE`);
            }
            
            console.log('✅ All AI tables removed successfully');
        }

        console.log('📋 Checking which AI functions exist...');
        
        // Check which functions exist
        const existingFunctionsResult = await pool.query(`
            SELECT routine_name 
            FROM information_schema.routines 
            WHERE routine_schema = 'public' 
            AND routine_name = ANY($1)
            ORDER BY routine_name
        `, [aiFunctions]);

        const existingFunctions = existingFunctionsResult.rows.map(row => row.routine_name);
        
        if (existingFunctions.length === 0) {
            console.log('✅ No AI functions found in database');
        } else {
            console.log(`🔧 Found ${existingFunctions.length} AI functions to remove:`);
            existingFunctions.forEach(func => {
                console.log(`  • ${func}()`);
            });

            // Drop functions
            for (const func of existingFunctions) {
                console.log(`🗑️  Dropping function: ${func}()`);
                await pool.query(`DROP FUNCTION IF EXISTS ${func} CASCADE`);
            }
            
            console.log('✅ All AI functions removed successfully');
        }

        // Check for any remaining AI-related columns in existing tables
        console.log('📋 Checking for AI-related columns in existing tables...');
        
        const aiColumnPatterns = [
            'ai_%',
            '%_ai_%',
            'behavior_%',
            'fraud_%',
            'reward_%',
            'analysis_%',
            'segment_%'
        ];

        for (const pattern of aiColumnPatterns) {
            const columnsResult = await pool.query(`
                SELECT table_name, column_name 
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND column_name LIKE $1
                ORDER BY table_name, column_name
            `, [pattern]);

            if (columnsResult.rows.length > 0) {
                console.log(`⚠️  Found AI-related columns matching pattern '${pattern}':`);
                columnsResult.rows.forEach(row => {
                    console.log(`  • ${row.table_name}.${row.column_name}`);
                });
                console.log('   Note: These columns were not automatically removed. Review manually if needed.');
            }
        }

        console.log('\n🎉 AI database cleanup completed successfully!');
        console.log('💡 The backend is now free of AI-related database components.');
        
    } catch (error) {
        console.error('❌ Cleanup failed:', error);
        logger.error('AI database cleanup error:', error);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// Run if called directly
if (require.main === module) {
    cleanupAITables().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { cleanupAITables };
