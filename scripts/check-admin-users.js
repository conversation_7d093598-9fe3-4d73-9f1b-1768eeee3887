#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to check and manage admin users
 */

const { connectDB, getSupabase } = require('../config/database');
const bcrypt = require('bcryptjs');

async function checkAdminUsers() {
  try {
    console.log('🔍 Connecting to database...');
    await connectDB();
    
    const supabase = getSupabase();
    
    console.log('👥 Checking for admin users...');
    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, phone_number, role, is_active, created_at')
      .eq('role', 'admin');
    
    if (error) {
      console.log('❌ Error:', error.message);
      return;
    }
    
    console.log(`\n📊 Found ${users.length} admin user(s):\n`);
    
    if (users.length === 0) {
      console.log('❌ No admin users found in the database.');
      console.log('🔧 You need to create an admin user first.');
      console.log('\n💡 To create an admin user, you can:');
      console.log('1. Use the registration endpoint with role "admin"');
      console.log('2. Or run: node scripts/create-admin-user.js');
    } else {
      users.forEach((user, index) => {
        console.log(`${index + 1}. Admin User:`);
        console.log(`   📧 Email: ${user.email || 'Not set'}`);
        console.log(`   📱 Phone: ${user.phone_number || 'Not set'}`);
        console.log(`   ✅ Active: ${user.is_active ? 'Yes' : 'No'}`);
        console.log(`   🆔 ID: ${user.id}`);
        console.log(`   📅 Created: ${new Date(user.created_at).toLocaleString()}`);
        console.log('');
      });
      
      console.log('🔐 Login Credentials:');
      console.log('   Dashboard: https://admin.payvendy.name.ng');
      console.log('   Use the email and password you set when creating the admin user');
    }
    
  } catch (error) {
    console.log('❌ Error checking admin users:', error.message);
  }
  
  process.exit(0);
}

async function createAdminUser(email, password, phoneNumber = null) {
  try {
    console.log('🔍 Connecting to database...');
    await connectDB();
    
    const supabase = getSupabase();
    
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id, email')
      .eq('email', email)
      .single();
    
    if (existingUser) {
      console.log('❌ User with this email already exists');
      return;
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create admin user
    const { data: newUser, error } = await supabase
      .from('users')
      .insert([{
        email: email,
        phone_number: phoneNumber,
        password_hash: hashedPassword,
        role: 'admin',
        is_active: true,
        email_verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();
    
    if (error) {
      console.log('❌ Error creating admin user:', error.message);
      return;
    }
    
    console.log('✅ Admin user created successfully!');
    console.log(`📧 Email: ${newUser.email}`);
    console.log(`🆔 ID: ${newUser.id}`);
    console.log('\n🔐 Login Details:');
    console.log(`   Dashboard: https://admin.payvendy.name.ng`);
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${password}`);
    
  } catch (error) {
    console.log('❌ Error creating admin user:', error.message);
  }
  
  process.exit(0);
}

// Command line interface
const args = process.argv.slice(2);
const command = args[0];

if (command === 'create') {
  const email = args[1];
  const password = args[2];
  const phone = args[3];
  
  if (!email || !password) {
    console.log('Usage: node scripts/check-admin-users.js create <email> <password> [phone]');
    console.log('Example: node scripts/check-admin-users.<NAME_EMAIL> mypassword123');
    process.exit(1);
  }
  
  createAdminUser(email, password, phone);
} else {
  checkAdminUsers();
}

module.exports = {
  checkAdminUsers,
  createAdminUser
};
