require('dotenv').config();
const logger = require('../utils/logger');

/**
 * Configuration checker script
 * Validates all environment variables and service configurations
 */

function checkConfiguration() {
  console.log('🔍 Checking Vendy Backend Configuration...\n');

  let allGood = true;
  const issues = [];
  const warnings = [];

  // Check required environment variables
  const requiredVars = [
    'NODE_ENV',
    'PORT',
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'TERMII_API_KEY',
    'BCRYPT_ROUNDS',
    'OTP_EXPIRY_TIME'
  ];

  console.log('📋 Required Environment Variables:');
  console.log('==================================');
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value) {
      console.log(`❌ ${varName}: MISSING`);
      issues.push(`${varName} is required but not set`);
      allGood = false;
    } else {
      console.log(`✅ ${varName}: SET`);
    }
  });

  console.log('\n📧 Email Configuration:');
  console.log('========================');
  
  // Check SendGrid configuration
  const sendgridKey = process.env.SENDGRID_API_KEY;
  const sendgridEmail = process.env.SENDGRID_FROM_EMAIL;
  const sendgridName = process.env.SENDGRID_FROM_NAME;

  if (!sendgridKey || sendgridKey === 'SG.your-sendgrid-api-key-here') {
    console.log('🔴 SENDGRID_API_KEY: NOT CONFIGURED');
    console.log('   ⚠️  You need to get a SendGrid API key');
    console.log('   📝 Go to: https://app.sendgrid.com/settings/api_keys');
    issues.push('SendGrid API key is required for email OTP functionality');
    allGood = false;
  } else if (sendgridKey.startsWith('SG.')) {
    console.log('✅ SENDGRID_API_KEY: CONFIGURED');
  } else {
    console.log('⚠️  SENDGRID_API_KEY: INVALID FORMAT (should start with SG.)');
    warnings.push('SendGrid API key should start with "SG."');
  }

  if (sendgridEmail) {
    console.log(`✅ SENDGRID_FROM_EMAIL: ${sendgridEmail}`);
  } else {
    console.log('❌ SENDGRID_FROM_EMAIL: NOT SET');
    issues.push('SendGrid from email is required');
  }

  if (sendgridName) {
    console.log(`✅ SENDGRID_FROM_NAME: ${sendgridName}`);
  } else {
    console.log('❌ SENDGRID_FROM_NAME: NOT SET');
    issues.push('SendGrid from name is required');
  }

  console.log('\n📱 SMS Configuration:');
  console.log('=====================');
  
  // Check Termii configuration
  const termiiKey = process.env.TERMII_API_KEY;
  const termiiSender = process.env.TERMII_SENDER_ID;
  const termiiUrl = process.env.TERMII_BASE_URL;

  if (termiiKey && termiiKey !== 'your-termii-api-key-here') {
    console.log('✅ TERMII_API_KEY: CONFIGURED');
  } else {
    console.log('❌ TERMII_API_KEY: NOT CONFIGURED');
    issues.push('Termii API key is required for SMS functionality');
  }

  if (termiiSender) {
    console.log(`✅ TERMII_SENDER_ID: ${termiiSender}`);
  } else {
    console.log('❌ TERMII_SENDER_ID: NOT SET');
    issues.push('Termii sender ID is required');
  }

  if (termiiUrl) {
    console.log(`✅ TERMII_BASE_URL: ${termiiUrl}`);
  } else {
    console.log('❌ TERMII_BASE_URL: NOT SET');
    issues.push('Termii base URL is required');
  }

  console.log('\n🗄️  Database Configuration:');
  console.log('============================');
  
  // Check Supabase configuration
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (supabaseUrl && supabaseUrl !== 'https://your-project-ref.supabase.co') {
    console.log(`✅ SUPABASE_URL: ${supabaseUrl}`);
  } else {
    console.log('❌ SUPABASE_URL: NOT CONFIGURED');
    issues.push('Supabase URL is required');
  }

  if (supabaseKey && supabaseKey !== 'your-supabase-service-role-key-here') {
    console.log('✅ SUPABASE_SERVICE_ROLE_KEY: CONFIGURED');
  } else {
    console.log('❌ SUPABASE_SERVICE_ROLE_KEY: NOT CONFIGURED');
    issues.push('Supabase service role key is required');
  }

  console.log('\n🔐 Security Configuration:');
  console.log('===========================');
  
  // Check security settings
  const jwtSecret = process.env.JWT_SECRET;
  const jwtRefreshSecret = process.env.JWT_REFRESH_SECRET;
  const bcryptRounds = process.env.BCRYPT_ROUNDS;

  if (jwtSecret && jwtSecret.length >= 32) {
    console.log('✅ JWT_SECRET: STRONG');
  } else {
    console.log('⚠️  JWT_SECRET: WEAK (should be at least 32 characters)');
    warnings.push('JWT secret should be at least 32 characters long');
  }

  if (jwtRefreshSecret && jwtRefreshSecret.length >= 32) {
    console.log('✅ JWT_REFRESH_SECRET: STRONG');
  } else {
    console.log('⚠️  JWT_REFRESH_SECRET: WEAK (should be at least 32 characters)');
    warnings.push('JWT refresh secret should be at least 32 characters long');
  }

  const rounds = parseInt(bcryptRounds);
  if (rounds >= 12) {
    console.log(`✅ BCRYPT_ROUNDS: ${rounds} (SECURE)`);
  } else {
    console.log(`⚠️  BCRYPT_ROUNDS: ${rounds} (should be at least 12)`);
    warnings.push('BCRYPT_ROUNDS should be at least 12 for security');
  }

  console.log('\n⚡ Rate Limiting:');
  console.log('=================');
  
  const emailRateLimit = process.env.MAX_EMAIL_OTP_PER_5MIN || '3';
  const smsRateLimit = process.env.MAX_SMS_OTP_PER_5MIN || '3';
  
  console.log(`✅ Email OTP Rate Limit: ${emailRateLimit} per 5 minutes`);
  console.log(`✅ SMS OTP Rate Limit: ${smsRateLimit} per 5 minutes`);

  // Summary
  console.log('\n📊 Configuration Summary:');
  console.log('==========================');
  
  if (allGood && issues.length === 0) {
    console.log('🎉 ALL CONFIGURATIONS ARE READY!');
    console.log('✅ Your backend is ready for production use');
  } else {
    console.log(`❌ Found ${issues.length} critical issue(s):`);
    issues.forEach(issue => console.log(`   • ${issue}`));
  }

  if (warnings.length > 0) {
    console.log(`\n⚠️  Found ${warnings.length} warning(s):`);
    warnings.forEach(warning => console.log(`   • ${warning}`));
  }

  console.log('\n🚀 Next Steps:');
  console.log('===============');
  
  if (issues.length > 0) {
    console.log('1. Fix the critical issues listed above');
    console.log('2. Re-run this script to verify: node scripts/check-config.js');
    console.log('3. Test email functionality: node scripts/test-email.js');
    console.log('4. Start the server: npm run dev');
  } else {
    console.log('1. Test email functionality: node scripts/test-email.js <EMAIL>');
    console.log('2. Start the server: npm run dev');
    console.log('3. Test API endpoints with Postman or curl');
    console.log('4. Deploy to production when ready');
  }

  console.log('\n📚 Documentation:');
  console.log('==================');
  console.log('• API Keys Guide: API_KEYS_REQUIRED.md');
  console.log('• Email Setup: SETUP_EMAIL.md');
  console.log('• API Documentation: README.md');

  return allGood && issues.length === 0;
}

// Run the configuration check
if (require.main === module) {
  const isReady = checkConfiguration();
  process.exit(isReady ? 0 : 1);
}

module.exports = { checkConfiguration };
