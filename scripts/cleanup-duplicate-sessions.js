const { getSupabase } = require('../config/database');

async function cleanupDuplicateSessions() {
  try {
    console.log('🧹 Starting cleanup of duplicate sessions...');
    
    const supabase = getSupabase();
    
    // First, let's see what we have
    const { data: allSessions, error: fetchError } = await supabase
      .from('sessions')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (fetchError) {
      throw fetchError;
    }
    
    console.log(`📊 Found ${allSessions.length} total sessions`);
    
    // Group by user_id and keep only the most recent session per user
    const userSessions = {};
    const sessionsToDeactivate = [];
    
    allSessions.forEach(session => {
      if (!userSessions[session.user_id]) {
        userSessions[session.user_id] = session;
      } else {
        // If this session is newer, deactivate the old one
        if (new Date(session.created_at) > new Date(userSessions[session.user_id].created_at)) {
          sessionsToDeactivate.push(userSessions[session.user_id].id);
          userSessions[session.user_id] = session;
        } else {
          // This session is older, deactivate it
          sessionsToDeactivate.push(session.id);
        }
      }
    });
    
    console.log(`🔄 Will deactivate ${sessionsToDeactivate.length} duplicate sessions`);
    
    if (sessionsToDeactivate.length > 0) {
      // Deactivate duplicate sessions
      const { error: updateError } = await supabase
        .from('sessions')
        .update({ 
          is_active: false, 
          updated_at: new Date().toISOString() 
        })
        .in('id', sessionsToDeactivate);
      
      if (updateError) {
        throw updateError;
      }
      
      console.log(`✅ Deactivated ${sessionsToDeactivate.length} duplicate sessions`);
    }
    
    // Show final stats
    const { data: activeSessions, error: activeError } = await supabase
      .from('sessions')
      .select('user_id')
      .eq('is_active', true);
    
    if (activeError) {
      throw activeError;
    }
    
    console.log(`📊 Final result: ${activeSessions.length} active sessions remaining`);
    console.log('✅ Cleanup completed successfully!');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// Run cleanup if this file is executed directly
if (require.main === module) {
  cleanupDuplicateSessions()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { cleanupDuplicateSessions };