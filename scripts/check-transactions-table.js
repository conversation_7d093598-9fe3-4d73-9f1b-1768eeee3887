/**
 * Check Transactions Table Structure
 * 
 * This script checks what columns exist in the transactions table
 * and identifies any missing columns.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function checkTransactionsTable() {
  try {
    console.log('🔍 Checking transactions table structure...');
    
    const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY, {
      auth: { autoRefreshToken: false, persistSession: false }
    });
    
    // Try to select all possible columns one by one
    const possibleColumns = [
      'id', 'user_id', 'type', 'amount', 'recipient', 'provider', 
      'status', 'reference', 'external_reference', 'description', 
      'metadata', 'created_at', 'updated_at'
    ];
    
    const existingColumns = [];
    
    for (const column of possibleColumns) {
      try {
        const { error } = await supabase
          .from('transactions')
          .select(column)
          .limit(1);
          
        if (!error) {
          existingColumns.push(column);
        }
      } catch (e) {
        // Column doesn't exist, continue
      }
    }
    
    console.log('✅ Existing columns in transactions table:');
    console.log(existingColumns.join(', '));
    
    const missingColumns = possibleColumns.filter(col => !existingColumns.includes(col));
    if (missingColumns.length > 0) {
      console.log('❌ Missing columns:');
      console.log(missingColumns.join(', '));
    } else {
      console.log('✅ All expected columns exist');
    }
    
    // Test creating a transaction without description
    if (!existingColumns.includes('description')) {
      console.log('\n🔧 Testing transaction creation without description column...');
      
      const testTransaction = {
        id: 'test-' + Date.now(),
        user_id: '00000000-0000-0000-0000-000000000000',
        type: 'airtime',
        amount: 100,
        recipient: '08012345678',
        provider: 'test',
        status: 'pending',
        reference: 'test-ref-' + Date.now(),
        metadata: {}
      };
      
      // Remove description from transaction object
      delete testTransaction.description;
      
      const { data, error } = await supabase
        .from('transactions')
        .insert([testTransaction])
        .select()
        .single();
      
      if (error) {
        console.error('❌ Test transaction creation failed:', error);
      } else {
        console.log('✅ Test transaction created successfully without description');
        
        // Clean up test transaction
        await supabase
          .from('transactions')
          .delete()
          .eq('id', testTransaction.id);
        console.log('✅ Test transaction cleaned up');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  checkTransactionsTable();
}

module.exports = { checkTransactionsTable };
