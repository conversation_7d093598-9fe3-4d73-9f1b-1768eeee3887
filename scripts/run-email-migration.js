require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { getSupabase } = require('../config/database');

/**
 * Run email verification migration
 * This script adds the missing email columns to the users table
 */

async function runEmailMigration() {
  try {
    console.log('🔄 Starting email verification migration...');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../migrations/add_email_columns.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📄 Migration SQL loaded');
    console.log('🗄️  Connecting to Supabase...');

    // Get Supabase client
    const supabase = getSupabase();

    console.log('📡 Executing migration...');

    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });

    if (error) {
      // If exec_sql doesn't exist, try direct SQL execution
      console.log('⚠️  exec_sql function not available, trying direct execution...');
      
      // Split the SQL into individual statements and execute them
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.includes('SELECT ') && statement.includes('message')) {
          // Skip the final SELECT message statement
          continue;
        }

        try {
          console.log(`📝 Executing: ${statement.substring(0, 50)}...`);
          const { error: stmtError } = await supabase.from('_').select('*').limit(0);
          
          // Since we can't execute raw SQL directly through the client,
          // we'll need to use the SQL editor in Supabase dashboard
          console.log('⚠️  Direct SQL execution not available through client');
          break;
        } catch (err) {
          console.log('⚠️  Statement execution failed:', err.message);
        }
      }
    } else {
      console.log('✅ Migration executed successfully');
      console.log('📊 Result:', data);
    }

    // Test if the columns exist now by trying to query them
    console.log('🧪 Testing email columns...');
    
    try {
      const { data: testData, error: testError } = await supabase
        .from('users')
        .select('id, email, is_email_verified, email_verification_token, email_verification_expires')
        .limit(1);

      if (testError) {
        console.log('❌ Email columns test failed:', testError.message);
        console.log('');
        console.log('📋 MANUAL MIGRATION REQUIRED:');
        console.log('===============================');
        console.log('1. Go to your Supabase dashboard');
        console.log('2. Navigate to SQL Editor');
        console.log('3. Copy and paste the following SQL:');
        console.log('');
        console.log(migrationSQL);
        console.log('');
        console.log('4. Execute the SQL');
        console.log('5. Restart your backend server');
        
        return false;
      } else {
        console.log('✅ Email columns are working correctly!');
        console.log('📊 Test query result:', testData);
        return true;
      }
    } catch (error) {
      console.log('❌ Column test failed:', error.message);
      return false;
    }

  } catch (error) {
    console.error('💥 Migration failed:', error);
    return false;
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runEmailMigration()
    .then((success) => {
      if (success) {
        console.log('\n🎉 Email migration completed successfully!');
        console.log('✅ Your backend is now ready for email verification');
        process.exit(0);
      } else {
        console.log('\n⚠️  Migration needs to be completed manually');
        console.log('📖 See instructions above');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { runEmailMigration };
