const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = process.env.BASE_URL || 'https://funny-poems-slide.loca.lt';
const API_VERSION = process.env.API_VERSION || 'v1';

// Test data
const testCrashReport = {
  errorId: `test_error_${Date.now()}`,
  message: 'Test error from crash reporting test script',
  stack: `Error: Test error from crash reporting test script
    at TestComponent.render (TestComponent.tsx:45:12)
    at ReactCompositeComponent._renderValidatedComponentWithoutOwnerOrContext (ReactCompositeComponent.js:587:34)
    at ReactCompositeComponent._renderValidatedComponent (ReactCompositeComponent.js:614:32)`,
  componentStack: `
    in TestComponent (at App.tsx:123:45)
    in ErrorBoundary (at App.tsx:120:12)
    in App (at index.tsx:67:89)`,
  timestamp: new Date().toISOString(),
  appVersion: '1.0.0',
  platform: 'android',
  deviceInfo: {
    model: 'Samsung Galaxy S21',
    brand: 'Samsung',
    systemName: 'Android',
    systemVersion: '12',
    buildNumber: '123',
    bundleId: 'com.vendy.app',
    isEmulator: false,
    totalMemory: 8589934592,
    usedMemory: 4294967296,
    freeDiskStorage: 107374182400,
    totalDiskCapacity: 128849018880
  },
  breadcrumbs: [
    {
      category: 'navigation',
      message: 'User navigated to Home screen',
      level: 'info',
      timestamp: new Date(Date.now() - 30000).toISOString(),
      data: { screen: 'Home' }
    },
    {
      category: 'user_action',
      message: 'User tapped buy airtime button',
      level: 'info',
      timestamp: new Date(Date.now() - 15000).toISOString(),
      data: { action: 'buy_airtime', amount: 1000 }
    },
    {
      category: 'api',
      message: 'API request failed',
      level: 'warning',
      timestamp: new Date(Date.now() - 5000).toISOString(),
      data: { endpoint: '/api/v1/airtime/purchase', status: 500 }
    }
  ]
};

class CrashReportingTester {
  constructor() {
    this.authToken = null;
    this.adminToken = null;
    this.testResults = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    console.log(logMessage);
    
    this.testResults.push({
      timestamp,
      type,
      message
    });
  }

  async makeRequest(method, endpoint, data = null, token = null) {
    try {
      const config = {
        method,
        url: `${BASE_URL}/api/${API_VERSION}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
        }
      };

      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return { success: true, data: response.data, status: response.status };
    } catch (error) {
      return {
        success: false,
        error: error.response?.data || error.message,
        status: error.response?.status || 500
      };
    }
  }

  async authenticateTestUser() {
    this.log('🔐 Authenticating test user...');
    
    // Try to create a test user first
    const testEmail = `test-crash-${Date.now()}@vendy.app`;
    const createResult = await this.makeRequest('POST', '/auth/register', {
      email: testEmail,
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User'
    });

    if (createResult.success) {
      this.log('✅ Test user created successfully');
    } else {
      this.log('⚠️  Test user creation failed, trying to login with existing user');
    }

    // Try to login
    const loginResult = await this.makeRequest('POST', '/auth/login', {
      email: testEmail,
      password: 'TestPassword123!'
    });

    if (loginResult.success && loginResult.data.data?.tokens?.accessToken) {
      this.authToken = loginResult.data.data.tokens.accessToken;
      this.log('✅ Authentication successful');
      return true;
    } else {
      this.log('❌ Authentication failed');
      return false;
    }
  }

  async testCrashReportSubmission() {
    this.log('📊 Testing crash report submission...');

    if (!this.authToken) {
      this.log('❌ No auth token available for crash report test');
      return false;
    }

    const result = await this.makeRequest('POST', '/crash-reports', testCrashReport, this.authToken);

    if (result.success) {
      this.log('✅ Crash report submitted successfully');
      this.log(`📝 Report ID: ${result.data.data?.reportId}`);
      return result.data.data?.reportId;
    } else {
      this.log(`❌ Crash report submission failed: ${JSON.stringify(result.error)}`);
      return false;
    }
  }

  async testAdminEndpoints(reportId) {
    this.log('👑 Testing admin endpoints...');

    // For this test, we'll assume admin credentials exist
    // In a real scenario, you'd have proper admin authentication
    this.log('⚠️  Admin endpoint testing requires admin credentials');
    this.log('📋 Available admin endpoints:');
    this.log('   GET /api/v1/admin/crash-reports/dashboard');
    this.log('   GET /api/v1/admin/crash-reports/stats');
    this.log('   GET /api/v1/admin/crash-reports');
    this.log('   GET /api/v1/admin/crash-reports/:id');
    this.log('   POST /api/v1/admin/crash-reports/:id/resolve');
    this.log('   POST /api/v1/admin/crash-reports/bulk-resolve');
    this.log('   DELETE /api/v1/admin/crash-reports/:id');

    return true;
  }

  async testErrorBoundaryIntegration() {
    this.log('🛡️  Testing Error Boundary integration...');
    
    // Test multiple error scenarios
    const errorScenarios = [
      {
        errorId: `boundary_test_${Date.now()}_1`,
        message: 'TypeError: Cannot read property of undefined',
        stack: 'TypeError: Cannot read property of undefined\n    at Component.render',
        severity: 'error'
      },
      {
        errorId: `boundary_test_${Date.now()}_2`,
        message: 'ReferenceError: variable is not defined',
        stack: 'ReferenceError: variable is not defined\n    at handleClick',
        severity: 'error'
      },
      {
        errorId: `boundary_test_${Date.now()}_3`,
        message: 'Fatal: Application crashed',
        stack: 'Fatal: Application crashed\n    at main',
        severity: 'fatal'
      }
    ];

    let successCount = 0;
    for (const scenario of errorScenarios) {
      const testReport = {
        ...testCrashReport,
        ...scenario,
        timestamp: new Date().toISOString()
      };

      const result = await this.makeRequest('POST', '/crash-reports', testReport, this.authToken);
      if (result.success) {
        successCount++;
        this.log(`✅ Error scenario ${scenario.errorId} submitted successfully`);
      } else {
        this.log(`❌ Error scenario ${scenario.errorId} failed`);
      }
    }

    this.log(`📊 Error Boundary test results: ${successCount}/${errorScenarios.length} scenarios successful`);
    return successCount === errorScenarios.length;
  }

  async testRateLimiting() {
    this.log('🚦 Testing rate limiting...');

    const requests = [];
    for (let i = 0; i < 15; i++) {
      const testReport = {
        ...testCrashReport,
        errorId: `rate_limit_test_${Date.now()}_${i}`,
        timestamp: new Date().toISOString()
      };

      requests.push(this.makeRequest('POST', '/crash-reports', testReport, this.authToken));
    }

    const results = await Promise.all(requests);
    const successCount = results.filter(r => r.success).length;
    const rateLimitedCount = results.filter(r => r.status === 429).length;

    this.log(`📊 Rate limiting test: ${successCount} successful, ${rateLimitedCount} rate limited`);
    
    if (rateLimitedCount > 0) {
      this.log('✅ Rate limiting is working correctly');
      return true;
    } else {
      this.log('⚠️  Rate limiting may not be configured properly');
      return false;
    }
  }

  async generateTestReport() {
    const reportPath = path.join(__dirname, `crash-reporting-test-report-${Date.now()}.json`);
    
    const report = {
      timestamp: new Date().toISOString(),
      testResults: this.testResults,
      summary: {
        totalTests: this.testResults.filter(r => r.type === 'info' && r.message.includes('Testing')).length,
        passed: this.testResults.filter(r => r.message.includes('✅')).length,
        failed: this.testResults.filter(r => r.message.includes('❌')).length,
        warnings: this.testResults.filter(r => r.message.includes('⚠️')).length
      },
      configuration: {
        baseUrl: BASE_URL,
        apiVersion: API_VERSION,
        testCrashReport
      }
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    this.log(`📄 Test report saved to: ${reportPath}`);
    
    return report;
  }

  async runAllTests() {
    this.log('🚀 Starting Crash Reporting System Tests');
    this.log('=====================================');

    try {
      // Test 1: Authentication
      const authSuccess = await this.authenticateTestUser();
      if (!authSuccess) {
        this.log('❌ Cannot proceed without authentication');
        return;
      }

      // Test 2: Basic crash report submission
      const reportId = await this.testCrashReportSubmission();

      // Test 3: Error boundary integration
      await this.testErrorBoundaryIntegration();

      // Test 4: Rate limiting
      await this.testRateLimiting();

      // Test 5: Admin endpoints (informational)
      await this.testAdminEndpoints(reportId);

      // Generate final report
      const finalReport = await this.generateTestReport();

      this.log('🎉 All tests completed!');
      this.log(`📊 Summary: ${finalReport.summary.passed} passed, ${finalReport.summary.failed} failed, ${finalReport.summary.warnings} warnings`);

    } catch (error) {
      this.log(`💥 Test suite failed with error: ${error.message}`);
      console.error(error);
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new CrashReportingTester();
  tester.runAllTests().catch(console.error);
}

module.exports = CrashReportingTester;
