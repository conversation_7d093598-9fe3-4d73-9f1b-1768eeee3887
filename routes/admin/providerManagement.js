/**
 * Provider Management Admin Routes
 * 
 * Admin routes for managing VTU providers (VTpass, PluginNG)
 * Includes provider switching, configuration, monitoring, and status management
 * 
 * Routes:
 * - GET /api/v1/admin/providers - Get all providers status
 * - POST /api/v1/admin/providers/switch - Switch primary provider
 * - PUT /api/v1/admin/providers/:id/toggle - Enable/disable provider
 * - PUT /api/v1/admin/providers/:id/config - Update provider configuration
 * - GET /api/v1/admin/providers/:id/stats - Get provider statistics
 * - GET /api/v1/admin/providers/actions - Get admin actions log
 * - POST /api/v1/admin/providers/:id/health-check - Manual health check
 * 
 * Security:
 * - Admin authentication required
 * - Admin permissions validation
 * - Input validation and sanitization
 * - Rate limiting for sensitive operations
 * - Comprehensive audit logging
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const express = require('express');
const { body, param, query } = require('express-validator');
const rateLimit = require('express-rate-limit');
const providerManagementController = require('../../controllers/providerManagementController');
const { authenticateToken } = require('../../middleware/auth');
const { requireAdminPermission, ADMIN_PERMISSIONS } = require('../../middleware/adminPermissions');

const router = express.Router();

// =====================================================
// RATE LIMITING
// =====================================================

// Rate limit for provider switching (more restrictive)
const providerSwitchRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // 10 switches per 5 minutes
  message: {
    success: false,
    message: 'Too many provider switch attempts. Please wait before trying again.',
    retryAfter: '5 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Rate limit for configuration updates
const configUpdateRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 20, // 20 config updates per minute
  message: {
    success: false,
    message: 'Too many configuration updates. Please wait before trying again.',
    retryAfter: '1 minute'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// General admin operations rate limit
const adminOperationsRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute
  message: {
    success: false,
    message: 'Too many requests. Please wait before trying again.',
    retryAfter: '1 minute'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// =====================================================
// VALIDATION RULES
// =====================================================

// Validation for switching primary provider
const switchProviderValidation = [
  body('provider_id')
    .isString()
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Provider ID must be a valid string with alphanumeric characters, hyphens, and underscores only'),
  body('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('Reason must be a string with maximum 500 characters')
];

// Validation for toggling provider status
const toggleProviderValidation = [
  body('provider_id')
    .isString()
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Provider ID must be a valid string'),
  body('enabled')
    .isBoolean()
    .withMessage('Enabled must be a boolean value'),
  body('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('Reason must be a string with maximum 500 characters')
];

// Validation for updating provider configuration
const updateConfigValidation = [
  param('provider_id')
    .isString()
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Provider ID must be a valid string'),
  body('config')
    .isObject()
    .withMessage('Config must be an object'),
  body('config.timeout')
    .optional()
    .isInt({ min: 1000, max: 120000 })
    .withMessage('Timeout must be between 1000 and 120000 milliseconds'),
  body('config.max_retries')
    .optional()
    .isInt({ min: 0, max: 10 })
    .withMessage('Max retries must be between 0 and 10'),
  body('config.min_amount')
    .optional()
    .isFloat({ min: 1, max: 1000 })
    .withMessage('Min amount must be between 1 and 1000'),
  body('config.max_amount')
    .optional()
    .isFloat({ min: 100, max: 1000000 })
    .withMessage('Max amount must be between 100 and 1,000,000'),
  body('reason')
    .optional()
    .isString()
    .isLength({ max: 500 })
    .withMessage('Reason must be a string with maximum 500 characters')
];

// Validation for provider stats query
const providerStatsValidation = [
  param('provider_id')
    .isString()
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Provider ID must be a valid string'),
  query('period')
    .optional()
    .isIn(['1h', '24h', '7d', '30d'])
    .withMessage('Period must be one of: 1h, 24h, 7d, 30d')
];

// Validation for admin actions log query
const adminActionsValidation = [
  query('page')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Page must be between 1 and 1000'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('provider_id')
    .optional()
    .isString()
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Provider ID must be a valid string'),
  query('action_type')
    .optional()
    .isIn(['enable', 'disable', 'switch_primary', 'update_config', 'maintenance'])
    .withMessage('Action type must be one of: enable, disable, switch_primary, update_config, maintenance')
];

// =====================================================
// ROUTE DEFINITIONS
// =====================================================

/**
 * @route   GET /api/v1/admin/providers
 * @desc    Get all providers status and configuration
 * @access  Admin only
 */
router.get('/',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  providerManagementController.getProvidersStatus
);

/**
 * @route   POST /api/v1/admin/providers/switch
 * @desc    Switch primary provider
 * @access  Admin only (write permissions)
 */
router.post('/switch',
  providerSwitchRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  switchProviderValidation,
  providerManagementController.switchPrimaryProvider
);

/**
 * @route   PUT /api/v1/admin/providers/:provider_id/toggle
 * @desc    Enable or disable a provider
 * @access  Admin only (write permissions)
 */
router.put('/:provider_id/toggle',
  providerSwitchRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  toggleProviderValidation,
  providerManagementController.toggleProviderStatus
);

/**
 * @route   PUT /api/v1/admin/providers/:provider_id/config
 * @desc    Update provider configuration
 * @access  Admin only (write permissions)
 */
router.put('/:provider_id/config',
  configUpdateRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  updateConfigValidation,
  providerManagementController.updateProviderConfig
);

/**
 * @route   GET /api/v1/admin/providers/:provider_id/stats
 * @desc    Get provider performance statistics
 * @access  Admin only
 */
router.get('/:provider_id/stats',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  providerStatsValidation,
  providerManagementController.getProviderStats
);

/**
 * @route   GET /api/v1/admin/providers/actions
 * @desc    Get admin actions log
 * @access  Admin only
 */
router.get('/actions',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.AUDIT_LOGS),
  adminActionsValidation,
  providerManagementController.getAdminActionsLog
);

/**
 * @route   GET /api/v1/admin/providers/alerts
 * @desc    Get provider alerts
 * @access  Admin only
 */
router.get('/alerts',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  providerManagementController.getProviderAlerts
);

/**
 * @route   PUT /api/v1/admin/providers/alerts/:alert_id/acknowledge
 * @desc    Acknowledge provider alert
 * @access  Admin only (write permissions)
 */
router.put('/alerts/:alert_id/acknowledge',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  providerManagementController.acknowledgeAlert
);

/**
 * @route   PUT /api/v1/admin/providers/alerts/:alert_id/resolve
 * @desc    Resolve provider alert
 * @access  Admin only (write permissions)
 */
router.put('/alerts/:alert_id/resolve',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  providerManagementController.resolveAlert
);

/**
 * @route   POST /api/v1/admin/providers/:provider_id/test-connection
 * @desc    Test provider connection
 * @access  Admin only (write permissions)
 */
router.post('/:provider_id/test-connection',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  providerManagementController.testProviderConnection
);

module.exports = router;
