/**
 * Airtime Configuration Routes
 * 
 * Admin routes for managing airtime service configurations
 * Provides comprehensive control over limits, pricing, networks, and settings
 * 
 * Security:
 * - Admin authentication required
 * - Read/write permissions enforced
 * - Rate limiting applied
 * - Input validation and sanitization
 * - Comprehensive audit logging
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();

// Middleware
const { authenticateToken } = require('../../middleware/auth');
const { requirePermission: requireAdminPermission, ADMIN_PERMISSIONS } = require('../../middleware/adminPermissions');
const { strictRateLimiter: adminOperationsRateLimit } = require('../../middleware/rateLimiter');

// Validation middleware
const {
  updateAirtimeSettingValidation,
  updateNetworkConfigValidation,
  toggleNetworkStatusValidation,
  testNetworkConfigValidation,
  adminActionsLogValidation,
  getAirtimeSettingsValidation,
  getNetworkConfigurationsValidation
} = require('../../middleware/airtimeConfigValidation');

// Controller
const airtimeConfigController = require('../../controllers/airtimeConfigController');

// =====================================================
// AIRTIME SETTINGS ROUTES
// =====================================================

/**
 * @route   GET /api/v1/admin/airtime-config/settings
 * @desc    Get all airtime settings
 * @access  Admin only (read permissions)
 */
router.get('/settings',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  getAirtimeSettingsValidation,
  airtimeConfigController.getAirtimeSettings
);

/**
 * @route   PUT /api/v1/admin/airtime-config/settings/:setting_key
 * @desc    Update airtime setting
 * @access  Admin only (write permissions)
 */
router.put('/settings/:setting_key',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  updateAirtimeSettingValidation,
  airtimeConfigController.updateAirtimeSetting
);

// =====================================================
// NETWORK CONFIGURATION ROUTES
// =====================================================

/**
 * @route   GET /api/v1/admin/airtime-config/networks
 * @desc    Get network configurations
 * @access  Admin only (read permissions)
 */
router.get('/networks',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  getNetworkConfigurationsValidation,
  airtimeConfigController.getNetworkConfigurations
);

/**
 * @route   PUT /api/v1/admin/airtime-config/networks/:network_id/:provider_id
 * @desc    Update network configuration
 * @access  Admin only (write permissions)
 */
router.put('/networks/:network_id/:provider_id',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  updateNetworkConfigValidation,
  airtimeConfigController.updateNetworkConfiguration
);

/**
 * @route   POST /api/v1/admin/airtime-config/networks/:network_id/:provider_id/toggle
 * @desc    Toggle network status (enable/disable)
 * @access  Admin only (write permissions)
 */
router.post('/networks/:network_id/:provider_id/toggle',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  toggleNetworkStatusValidation,
  airtimeConfigController.toggleNetworkStatus
);

/**
 * @route   POST /api/v1/admin/airtime-config/networks/:network_id/:provider_id/test
 * @desc    Test network configuration
 * @access  Admin only (write permissions)
 */
router.post('/networks/:network_id/:provider_id/test',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  testNetworkConfigValidation,
  airtimeConfigController.testNetworkConfiguration
);

// =====================================================
// MONITORING AND AUDIT ROUTES
// =====================================================

/**
 * @route   GET /api/v1/admin/airtime-config/actions
 * @desc    Get admin actions log
 * @access  Admin only (read permissions)
 */
router.get('/actions',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.AUDIT_LOGS),
  adminActionsLogValidation,
  airtimeConfigController.getAdminActionsLog
);

/**
 * @route   GET /api/v1/admin/airtime-config/summary
 * @desc    Get configuration summary for dashboard
 * @access  Admin only (read permissions)
 */
router.get('/summary',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.SYSTEM_SETTINGS),
  airtimeConfigController.getConfigurationSummary
);

// =====================================================
// BULK OPERATIONS ROUTES
// =====================================================

/**
 * @route   POST /api/v1/admin/airtime-config/networks/bulk-toggle
 * @desc    Bulk enable/disable networks
 * @access  Admin only (write permissions)
 */
router.post('/networks/bulk-toggle',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.BULK_OPERATIONS),
  async (req, res) => {
    try {
      const { networks, enabled, reason } = req.body;
      const adminUser = req.user;

      if (!Array.isArray(networks) || networks.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Networks array is required and must not be empty'
        });
      }

      if (typeof enabled !== 'boolean') {
        return res.status(400).json({
          success: false,
          message: 'Enabled must be a boolean value'
        });
      }

      const results = [];
      const errors = [];

      for (const network of networks) {
        try {
          // Create a mock request object for the toggle function
          const mockReq = {
            params: { network_id: network.network_id, provider_id: network.provider_id },
            body: { enabled, reason },
            user: adminUser,
            ip: req.ip,
            get: req.get.bind(req)
          };

          const mockRes = {
            status: () => mockRes,
            json: (data) => data
          };

          await airtimeConfigController.toggleNetworkStatus(mockReq, mockRes);
          results.push({
            network_id: network.network_id,
            provider_id: network.provider_id,
            success: true
          });
        } catch (error) {
          errors.push({
            network_id: network.network_id,
            provider_id: network.provider_id,
            error: error.message
          });
        }
      }

      res.status(200).json({
        success: true,
        message: `Bulk operation completed. ${results.length} successful, ${errors.length} failed.`,
        data: {
          successful: results,
          failed: errors,
          total_processed: networks.length
        }
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Bulk toggle operation failed',
        error: error.message
      });
    }
  }
);

/**
 * @route   POST /api/v1/admin/airtime-config/settings/bulk-update
 * @desc    Bulk update multiple settings
 * @access  Admin only (write permissions)
 */
router.post('/settings/bulk-update',
  adminOperationsRateLimit,
  authenticateToken,
  requireAdminPermission(ADMIN_PERMISSIONS.BULK_OPERATIONS),
  async (req, res) => {
    try {
      const { settings } = req.body;
      const adminUser = req.user;

      if (!Array.isArray(settings) || settings.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Settings array is required and must not be empty'
        });
      }

      const results = [];
      const errors = [];

      for (const setting of settings) {
        try {
          // Create a mock request object for the update function
          const mockReq = {
            params: { setting_key: setting.setting_key },
            body: {
              setting_value: setting.setting_value,
              setting_type: setting.setting_type,
              description: setting.description
            },
            user: adminUser,
            ip: req.ip,
            get: req.get.bind(req)
          };

          const mockRes = {
            status: () => mockRes,
            json: (data) => data
          };

          await airtimeConfigController.updateAirtimeSetting(mockReq, mockRes);
          results.push({
            setting_key: setting.setting_key,
            success: true
          });
        } catch (error) {
          errors.push({
            setting_key: setting.setting_key,
            error: error.message
          });
        }
      }

      res.status(200).json({
        success: true,
        message: `Bulk update completed. ${results.length} successful, ${errors.length} failed.`,
        data: {
          successful: results,
          failed: errors,
          total_processed: settings.length
        }
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Bulk update operation failed',
        error: error.message
      });
    }
  }
);

module.exports = router;
