/**
 * Admin OTA Updates Management Routes
 * 
 * Provides admin endpoints for managing Over-The-Air updates
 */

const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const multer = require('multer');
const { body, validationResult } = require('express-validator');

const logger = require('../../utils/logger');
const authService = require('../../services/authService');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../../storage/app-bundles'));
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow zip files and json files
    if (file.mimetype === 'application/zip' || 
        file.mimetype === 'application/json' ||
        file.originalname.endsWith('.zip') ||
        file.originalname.endsWith('.json')) {
      cb(null, true);
    } else {
      cb(new Error('Only ZIP and JSON files are allowed'));
    }
  }
});

/**
 * @route GET /api/v1/admin/ota/updates
 * @desc Get all available updates
 * @access Private (Admin only)
 */
router.get('/updates',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const configPath = path.join(__dirname, '../../config/app-updates.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      res.json({
        success: true,
        data: {
          updates: config.updates,
          config: config.config,
          rollout: config.rollout,
          lastUpdated: config.lastUpdated
        }
      });
    } catch (error) {
      logger.error('Failed to get OTA updates:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve updates'
      });
    }
  }
);

/**
 * @route POST /api/v1/admin/ota/updates
 * @desc Create new update
 * @access Private (Admin only)
 */
router.post('/updates',
  authService.protect,
  authService.restrictTo('admin'),
  upload.single('bundle'),
  [
    body('version').notEmpty().withMessage('Version is required'),
    body('buildNumber').notEmpty().withMessage('Build number is required'),
    body('platform').isIn(['ios', 'android', 'both']).withMessage('Invalid platform'),
    body('description').notEmpty().withMessage('Description is required'),
    body('isMandatory').isBoolean().withMessage('isMandatory must be boolean'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const {
        version,
        buildNumber,
        platform,
        description,
        releaseNotes,
        isMandatory,
        minAppVersion,
        maxAppVersion
      } = req.body;

      const file = req.file;
      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'Bundle file is required'
        });
      }

      // Generate checksum for the uploaded file
      const fileBuffer = await fs.readFile(file.path);
      const checksum = 'sha256:' + crypto.createHash('sha256').update(fileBuffer).digest('hex');

      // Create update entry
      const updateId = `vendy-v${version}-${platform}`;
      const newUpdate = {
        id: updateId,
        version,
        buildNumber,
        platform,
        filename: file.filename,
        downloadUrl: `/api/v1/app/updates/download/${updateId}`,
        checksum,
        size: file.size,
        isMandatory: isMandatory === 'true',
        description,
        releaseNotes: releaseNotes || '',
        minAppVersion: minAppVersion || null,
        maxAppVersion: maxAppVersion || null,
        createdAt: new Date().toISOString(),
        deployedAt: new Date().toISOString(),
        isActive: true
      };

      // Load current config
      const configPath = path.join(__dirname, '../../config/app-updates.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      // Add new update
      config.updates.push(newUpdate);
      config.lastUpdated = new Date().toISOString();

      // Save updated config
      await fs.writeFile(configPath, JSON.stringify(config, null, 2));

      logger.info('New OTA update created:', {
        updateId,
        version,
        platform,
        adminId: req.user.id,
        fileSize: file.size
      });

      res.json({
        success: true,
        message: 'Update created successfully',
        data: newUpdate
      });

    } catch (error) {
      logger.error('Failed to create OTA update:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create update'
      });
    }
  }
);

/**
 * @route PUT /api/v1/admin/ota/updates/:updateId
 * @desc Update existing update configuration
 * @access Private (Admin only)
 */
router.put('/updates/:updateId',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { updateId } = req.params;
      const { isActive, isMandatory, description, releaseNotes } = req.body;

      const configPath = path.join(__dirname, '../../config/app-updates.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      const updateIndex = config.updates.findIndex(u => u.id === updateId);
      if (updateIndex === -1) {
        return res.status(404).json({
          success: false,
          error: 'Update not found'
        });
      }

      // Update the configuration
      if (isActive !== undefined) config.updates[updateIndex].isActive = isActive;
      if (isMandatory !== undefined) config.updates[updateIndex].isMandatory = isMandatory;
      if (description) config.updates[updateIndex].description = description;
      if (releaseNotes) config.updates[updateIndex].releaseNotes = releaseNotes;

      config.lastUpdated = new Date().toISOString();

      // Save updated config
      await fs.writeFile(configPath, JSON.stringify(config, null, 2));

      logger.info('OTA update modified:', {
        updateId,
        adminId: req.user.id,
        changes: { isActive, isMandatory, description: !!description, releaseNotes: !!releaseNotes }
      });

      res.json({
        success: true,
        message: 'Update modified successfully',
        data: config.updates[updateIndex]
      });

    } catch (error) {
      logger.error('Failed to update OTA configuration:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update configuration'
      });
    }
  }
);

/**
 * @route DELETE /api/v1/admin/ota/updates/:updateId
 * @desc Delete an update
 * @access Private (Admin only)
 */
router.delete('/updates/:updateId',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { updateId } = req.params;

      const configPath = path.join(__dirname, '../../config/app-updates.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      const updateIndex = config.updates.findIndex(u => u.id === updateId);
      if (updateIndex === -1) {
        return res.status(404).json({
          success: false,
          error: 'Update not found'
        });
      }

      const update = config.updates[updateIndex];

      // Remove the update from config
      config.updates.splice(updateIndex, 1);
      config.lastUpdated = new Date().toISOString();

      // Save updated config
      await fs.writeFile(configPath, JSON.stringify(config, null, 2));

      // Optionally delete the file (commented out for safety)
      // const filePath = path.join(__dirname, '../../storage/app-bundles', update.filename);
      // await fs.unlink(filePath);

      logger.info('OTA update deleted:', {
        updateId,
        adminId: req.user.id
      });

      res.json({
        success: true,
        message: 'Update deleted successfully'
      });

    } catch (error) {
      logger.error('Failed to delete OTA update:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete update'
      });
    }
  }
);

module.exports = router;
