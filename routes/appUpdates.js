/**
 * App Updates API Routes
 * 
 * Handles Over-The-Air (OTA) update functionality for the Vendy mobile app.
 * Provides endpoints for checking available updates, downloading bundles,
 * and managing update deployments.
 * 
 * Key Features:
 * - Version compatibility checking
 * - Platform-specific updates (iOS/Android)
 * - Mandatory vs optional updates
 * - Update analytics and tracking
 * - Secure bundle distribution
 * - Rollback capabilities
 * 
 * Security Features:
 * - Bundle integrity verification
 * - Signed update packages
 * - Version validation
 * - Rate limiting
 * - Authentication required
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

// Import utilities and middleware
const logger = require('../utils/logger');
const otaManager = require('../utils/otaManager');
const otaService = require('../services/otaService');
const { authenticateToken } = require('../middleware/auth');

// Rate limiting for update checks
const updateCheckLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 requests per windowMs
  message: {
    error: 'Too many update check requests, please try again later',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Configuration
const UPDATE_CONFIG = {
  BUNDLES_PATH: path.join(__dirname, '../storage/app-bundles'),
  SUPPORTED_PLATFORMS: ['ios', 'android'],
  MAX_BUNDLE_SIZE: 50 * 1024 * 1024, // 50MB
  CURRENT_VERSIONS: {
    ios: '1.0.0',
    android: '1.0.0'
  }
};

/**
 * @route POST /api/v1/app/updates/check
 * @desc Check for available app updates
 * @access Public (with rate limiting)
 */
router.post('/check',
  updateCheckLimiter,
  [
    body('currentVersion').notEmpty().withMessage('Current version is required'),
    body('currentBuildNumber').notEmpty().withMessage('Current build number is required'),
    body('platform').isIn(['ios', 'android']).withMessage('Invalid platform'),
    body('deviceInfo').isObject().withMessage('Device info is required'),
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }
    try {
      const { currentVersion, currentBuildNumber, platform, deviceInfo } = req.body;

      logger.info('📱 [APP UPDATES] Update check request', {
        currentVersion,
        currentBuildNumber,
        platform,
        deviceModel: deviceInfo?.model,
        userAgent: req.get('User-Agent')
      });

      // Load available updates configuration
      const updatesConfig = await loadUpdatesConfig();
      
      // Find applicable update for this version and platform using OTA manager
      const availableUpdate = await otaManager.findApplicableUpdate(
        currentVersion,
        currentBuildNumber,
        platform
      );

      if (availableUpdate) {
        // Log update availability
        logger.info('📦 [APP UPDATES] Update available', {
          currentVersion,
          availableVersion: availableUpdate.version,
          platform,
          mandatory: availableUpdate.isMandatory
        });

        // Track update check analytics
        await trackUpdateCheck(req.body, availableUpdate, true);

        res.json({
          updateAvailable: true,
          bundle: {
            version: availableUpdate.version,
            buildNumber: availableUpdate.buildNumber,
            description: availableUpdate.description,
            size: availableUpdate.size,
            downloadUrl: `${req.protocol}://${req.get('host')}/api/v1/app/updates/download/${availableUpdate.id}`,
            checksum: availableUpdate.checksum,
            isMandatory: availableUpdate.isMandatory,
            releaseNotes: availableUpdate.releaseNotes,
            minAppVersion: availableUpdate.minAppVersion,
            maxAppVersion: availableUpdate.maxAppVersion,
            platform: availableUpdate.platform,
            createdAt: availableUpdate.createdAt,
            // Additional fields the frontend might need
            packageHash: availableUpdate.checksum,
            label: `v${availableUpdate.version}-${availableUpdate.buildNumber}`,
            packageSize: availableUpdate.size
          }
        });
      } else {
        logger.info('✅ [APP UPDATES] No update available', {
          currentVersion,
          platform
        });

        // Track update check analytics
        await trackUpdateCheck(req.body, null, false);

        res.json({
          updateAvailable: false,
          message: 'App is up to date'
        });
      }

    } catch (error) {
      logger.error('❌ [APP UPDATES] Update check failed', {
        error: error.message,
        stack: error.stack,
        body: req.body
      });

      res.status(500).json({
        success: false,
        error: 'Failed to check for updates',
        code: 'UPDATE_CHECK_FAILED'
      });
    }
  }
);

/**
 * @route GET /api/v1/app/updates/download/:updateId
 * @desc Download app update bundle
 * @access Public (with rate limiting)
 */
router.get('/download/:updateId',
  updateCheckLimiter,
  async (req, res) => {
    try {
      const { updateId } = req.params;

      logger.info('⬇️ [APP UPDATES] Bundle download request', {
        updateId,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });

      // Load updates configuration
      const updatesConfig = await loadUpdatesConfig();
      const update = updatesConfig.updates.find(u => u.id === updateId);

      if (!update) {
        logger.warn('⚠️ [APP UPDATES] Update not found', { updateId });
        return res.status(404).json({
          success: false,
          error: 'Update not found',
          code: 'UPDATE_NOT_FOUND'
        });
      }

      const bundlePath = path.join(UPDATE_CONFIG.BUNDLES_PATH, update.filename);

      // Check if bundle file exists
      try {
        await fs.access(bundlePath);
      } catch (error) {
        logger.error('❌ [APP UPDATES] Bundle file not found', {
          updateId,
          bundlePath,
          error: error.message
        });
        return res.status(404).json({
          success: false,
          error: 'Bundle file not found',
          code: 'BUNDLE_FILE_NOT_FOUND'
        });
      }

      // Set appropriate headers for file download
      res.setHeader('Content-Type', 'application/zip');
      res.setHeader('Content-Disposition', `attachment; filename="${update.filename}"`);
      res.setHeader('Content-Length', update.size);
      res.setHeader('X-Bundle-Version', update.version);
      res.setHeader('X-Bundle-Checksum', update.checksum);
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
      res.setHeader('ETag', update.checksum);
      res.setHeader('Accept-Ranges', 'bytes'); // Support resume downloads

      // Add CORS headers for mobile app access
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Range');

      // Track download start
      await trackBundleDownload(updateId, req.ip, req.get('User-Agent'));

      // Stream the file
      const fileStream = require('fs').createReadStream(bundlePath);
      
      fileStream.on('error', (error) => {
        logger.error('❌ [APP UPDATES] Bundle download stream error', {
          updateId,
          error: error.message
        });
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            error: 'Download failed',
            code: 'DOWNLOAD_STREAM_ERROR'
          });
        }
      });

      fileStream.on('end', () => {
        logger.info('✅ [APP UPDATES] Bundle download completed', {
          updateId,
          size: update.size
        });
      });

      fileStream.pipe(res);

    } catch (error) {
      logger.error('❌ [APP UPDATES] Bundle download failed', {
        error: error.message,
        stack: error.stack,
        updateId: req.params.updateId
      });

      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: 'Download failed',
          code: 'DOWNLOAD_FAILED'
        });
      }
    }
  }
);

/**
 * @route POST /api/v1/app/updates/analytics
 * @desc Track update analytics
 * @access Public
 */
router.post('/analytics',
  [
    body('event').isIn([
      'update_check', 'download_started', 'download_completed', 'download_failed',
      'install_started', 'install_completed', 'install_failed', 'rollback_started',
      'rollback_completed', 'restart_required', 'user_dismissed'
    ]).withMessage('Invalid event type'),
    body('updateId').optional().isString().withMessage('Update ID must be string'),
    body('platform').isIn(['ios', 'android']).withMessage('Invalid platform'),
    body('version').optional().isString().withMessage('Version must be string'),
    body('buildNumber').optional().isString().withMessage('Build number must be string'),
  ],
  async (req, res) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const {
        event,
        updateId,
        platform,
        version,
        buildNumber,
        metadata,
        error,
        duration,
        downloadSize,
        installSize
      } = req.body;

      const analyticsData = {
        event,
        updateId,
        platform,
        version,
        buildNumber,
        metadata,
        error,
        duration,
        downloadSize,
        installSize,
        timestamp: new Date(),
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        deviceInfo: metadata?.deviceInfo || {}
      };

      logger.info('📊 [APP UPDATES] Analytics event', analyticsData);

      // Store analytics data
      await storeAnalyticsEvent(analyticsData);

      res.json({
        success: true,
        message: 'Analytics event recorded',
        eventId: crypto.randomUUID()
      });

    } catch (error) {
      logger.error('❌ [APP UPDATES] Analytics tracking failed', {
        error: error.message,
        body: req.body
      });

      res.status(500).json({
        success: false,
        error: 'Failed to record analytics',
        code: 'ANALYTICS_FAILED'
      });
    }
  }
);

/**
 * Load updates configuration from file
 */
async function loadUpdatesConfig() {
  try {
    const configPath = path.join(__dirname, '../config/app-updates.json');
    const configData = await fs.readFile(configPath, 'utf8');
    return JSON.parse(configData);
  } catch (error) {
    logger.warn('⚠️ [APP UPDATES] Failed to load updates config, using defaults', {
      error: error.message
    });
    
    // Return default configuration
    return {
      updates: [],
      lastUpdated: new Date().toISOString()
    };
  }
}

/**
 * Find applicable update for given version and platform
 */
function findApplicableUpdate(updatesConfig, currentVersion, currentBuildNumber, platform) {
  const updates = updatesConfig.updates || [];
  
  // Filter updates for the platform
  const platformUpdates = updates.filter(update => 
    update.platform === platform || update.platform === 'both'
  );

  // Find the latest applicable update
  for (const update of platformUpdates) {
    if (isVersionNewer(update.version, currentVersion) || 
        (update.version === currentVersion && parseInt(update.buildNumber) > parseInt(currentBuildNumber))) {
      
      // Check version compatibility
      if (isVersionCompatible(currentVersion, update.minAppVersion, update.maxAppVersion)) {
        return update;
      }
    }
  }

  return null;
}

/**
 * Check if version A is newer than version B
 */
function isVersionNewer(versionA, versionB) {
  const partsA = versionA.split('.').map(Number);
  const partsB = versionB.split('.').map(Number);
  
  for (let i = 0; i < Math.max(partsA.length, partsB.length); i++) {
    const a = partsA[i] || 0;
    const b = partsB[i] || 0;
    
    if (a > b) return true;
    if (a < b) return false;
  }
  
  return false;
}

/**
 * Check version compatibility
 */
function isVersionCompatible(currentVersion, minVersion, maxVersion) {
  const isAboveMin = !minVersion || !isVersionNewer(minVersion, currentVersion);
  const isBelowMax = !maxVersion || isVersionNewer(maxVersion, currentVersion) || maxVersion === currentVersion;
  
  return isAboveMin && isBelowMax;
}

/**
 * Track update check analytics
 */
async function trackUpdateCheck(requestData, availableUpdate, updateAvailable) {
  try {
    // Implement your analytics tracking here
    // This could be stored in database, sent to analytics service, etc.
    logger.info('📊 [APP UPDATES] Update check tracked', {
      currentVersion: requestData.currentVersion,
      platform: requestData.platform,
      updateAvailable,
      availableVersion: availableUpdate?.version
    });
  } catch (error) {
    logger.error('❌ [APP UPDATES] Failed to track update check', error);
  }
}

/**
 * Track bundle download
 */
async function trackBundleDownload(updateId, ip, userAgent) {
  try {
    // Implement your download tracking here
    logger.info('📊 [APP UPDATES] Bundle download tracked', {
      updateId,
      ip,
      userAgent
    });
  } catch (error) {
    logger.error('❌ [APP UPDATES] Failed to track bundle download', error);
  }
}

/**
 * Store analytics event
 */
async function storeAnalyticsEvent(eventData) {
  try {
    // Implement your analytics storage here
    // This could be database, analytics service, etc.
    logger.info('📊 [APP UPDATES] Analytics event stored', eventData);
  } catch (error) {
    logger.error('❌ [APP UPDATES] Failed to store analytics event', error);
  }
}

module.exports = router;
