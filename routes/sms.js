const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const smsService = require('../services/smsService');
const authService = require('../services/authService');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for SMS endpoints
const smsLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 2, // limit each IP to 2 SMS requests per minute
  message: {
    error: 'Too many SMS requests, please try again later.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// All SMS routes require authentication
router.use(authService.protect);

/**
 * @route   POST /api/v1/sms/send
 * @desc    Send SMS (for admin use or special cases)
 * @access  Private (Admin only)
 */
router.post('/send',
  authService.restrictTo('admin'),
  smsLimiter,
  [
    body('phoneNumber')
      .matches(/^0[789][01]\d{8}$/)
      .withMessage('Please provide a valid Nigerian phone number'),
    body('message')
      .isLength({ min: 1, max: 160 })
      .withMessage('Message must be between 1 and 160 characters'),
    body('type')
      .optional()
      .isIn(['plain', 'numeric'])
      .withMessage('Type must be either plain or numeric')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { phoneNumber, message, type = 'plain' } = req.body;
      const adminId = req.user.id;

      logger.info(`Admin ${adminId} sending SMS to ${phoneNumber}`);

      const result = await smsService.sendSMS(phoneNumber, message, type);

      if (!result.success) {
        return res.status(500).json({
          status: 'error',
          message: result.error || 'Failed to send SMS'
        });
      }

      res.status(200).json({
        status: 'success',
        message: 'SMS sent successfully',
        data: {
          messageId: result.messageId,
          phoneNumber,
          sentAt: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Send SMS error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/v1/sms/delivery-status/:messageId
 * @desc    Check SMS delivery status
 * @access  Private (Admin only)
 */
router.get('/delivery-status/:messageId',
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { messageId } = req.params;

      if (!messageId) {
        return res.status(400).json({
          status: 'error',
          message: 'Message ID is required'
        });
      }

      const result = await smsService.checkDeliveryStatus(messageId);

      if (!result.success) {
        return res.status(500).json({
          status: 'error',
          message: result.error || 'Failed to check delivery status'
        });
      }

      res.status(200).json({
        status: 'success',
        data: {
          messageId,
          deliveryStatus: result.status,
          details: result.data
        }
      });

    } catch (error) {
      logger.error('Check delivery status error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/v1/sms/balance
 * @desc    Get SMS account balance
 * @access  Private (Admin only)
 */
router.get('/balance',
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const result = await smsService.getBalance();

      if (!result.success) {
        return res.status(500).json({
          status: 'error',
          message: result.error || 'Failed to get SMS balance'
        });
      }

      res.status(200).json({
        status: 'success',
        data: {
          balance: result.balance,
          currency: result.currency,
          checkedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Get SMS balance error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/sms/resend-otp
 * @desc    Resend OTP to user's phone number
 * @access  Private
 */
router.post('/resend-otp',
  smsLimiter,
  async (req, res) => {
    try {
      const userId = req.user.id;
      const user = req.user;

      // Check if user's phone is already verified
      if (user.isPhoneVerified) {
        return res.status(400).json({
          status: 'error',
          message: 'Phone number is already verified'
        });
      }

      // Generate new OTP
      const otp = user.createPhoneVerificationToken();
      await user.save({ validateBeforeSave: false });

      // Send OTP via SMS
      const smsResult = await smsService.sendOTP(user.phoneNumber, otp, 'verification');

      if (!smsResult.success) {
        logger.error(`Failed to resend OTP to user ${userId}: ${smsResult.error}`);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to send verification code. Please try again.'
        });
      }

      logger.info(`OTP resent successfully to user ${userId}`);

      res.status(200).json({
        status: 'success',
        message: 'Verification code sent to your phone number',
        data: {
          phoneNumber: user.phoneNumber,
          expiresIn: 300 // 5 minutes in seconds
        }
      });

    } catch (error) {
      logger.error('Resend OTP error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

module.exports = router;
