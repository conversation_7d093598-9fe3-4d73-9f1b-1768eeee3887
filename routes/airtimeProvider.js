/**
 * Airtime Provider Routes
 * 
 * Simple routes for switching between VTpass and PluginNG providers
 * Admin-only endpoints for provider management
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const rateLimit = require('express-rate-limit');

const {
  getProviderStatus,
  updateProviderSettings,
  testProvider,
  getProviderStats
} = require('../controllers/airtimeProviderController');

const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');

// Rate limiting for provider operations
const providerRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many provider requests, please try again later'
  }
});

// Apply rate limiting and authentication to all routes
router.use(providerRateLimit);
router.use(authenticateToken);
router.use(requireAdmin);

/**
 * @route   GET /api/v1/admin/airtime-providers/status
 * @desc    Get current provider status
 * @access  Admin only
 */
router.get('/status', getProviderStatus);

/**
 * @route   POST /api/v1/admin/airtime-providers/update
 * @desc    Update provider settings
 * @access  Admin only
 */
router.post('/update', 
  validateRequest([
    {
      field: 'providers',
      type: 'object',
      required: true,
      message: 'Provider settings are required'
    }
  ]),
  updateProviderSettings
);

/**
 * @route   POST /api/v1/admin/airtime-providers/:provider/test
 * @desc    Test provider connection
 * @access  Admin only
 */
router.post('/:provider/test', testProvider);

/**
 * @route   GET /api/v1/admin/airtime-providers/stats
 * @desc    Get provider statistics
 * @access  Admin only
 */
router.get('/stats', getProviderStats);

module.exports = router;
