const express = require('express');
const router = express.Router();
const {
  sendPushNotification,
  saveFcmToken,
  getUserFcmTokens,
  sendTransactionNotification,
  sendSecurityAlert,
  sendAccountNotification,
  sendPurchaseNotification
} = require('../services/notificationService');
const { body, validationResult } = require('express-validator');
const userService = require('../services/userService');
const smsService = require('../services/smsService');
const logger = require('../utils/logger');
const { authenticateToken } = require('../middleware/auth');

// Apply authentication to all notification routes
router.use(authenticateToken);

// Store FCM token for a user (authenticated)
router.post('/register-fcm-token', [
  body('userId').isUUID(),
  body('fcmToken').isString().notEmpty(),
  body('deviceInfo').optional().isObject(),
], async (req, res) => {
  try {
    console.log('🔔 [NOTIFICATION-ROUTE] FCM token registration request received');
    console.log('🔔 Request body:', JSON.stringify(req.body, null, 2));
    console.log('🔔 Authenticated user:', req.user?.id);

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('❌ [NOTIFICATION-ROUTE] Validation errors:', errors.array());
      return res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId, fcmToken, deviceInfo } = req.body;

    // Verify that the authenticated user matches the userId in the request
    if (req.user.id !== userId) {
      console.log('❌ [NOTIFICATION-ROUTE] User ID mismatch:', {
        authenticatedUser: req.user.id,
        requestedUserId: userId
      });
      return res.status(403).json({
        status: 'error',
        message: 'Cannot register FCM token for another user'
      });
    }

    console.log('🔔 [NOTIFICATION-ROUTE] Saving FCM token to database...');
    await saveFcmToken(userId, fcmToken, deviceInfo);

    console.log('✅ [NOTIFICATION-ROUTE] FCM token registered successfully', {
      deviceInfo: deviceInfo ? {
        platform: deviceInfo.platform,
        model: deviceInfo.deviceModel,
        appVersion: deviceInfo.appVersion
      } : 'No device info provided'
    });
    res.status(200).json({
      status: 'success',
      message: 'FCM token registered successfully'
    });
  } catch (err) {
    console.log('❌ [NOTIFICATION-ROUTE] Error registering FCM token:', err);
    logger.error('FCM token registration error:', err);
    res.status(500).json({
      status: 'error',
      message: 'Failed to register FCM token',
      error: err.message
    });
  }
});

// Send push notification to a user
router.post('/send-push', [
  body('userId').isUUID(),
  body('title').isString().notEmpty(),
  body('body').isString().notEmpty(),
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  const { userId, title, body: messageBody } = req.body;
  try {
    await sendPushNotification(userId, title, messageBody);
    res.status(200).json({ message: 'Push notification sent' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to send push notification' });
  }
});

// Send transaction notification (high security)
router.post('/transaction', [
  body('userId').isUUID(),
  body('amount').isNumeric(),
  body('type').isIn(['debit', 'credit']),
  body('recipient').optional().isString(),
  body('reference').isString().notEmpty(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { userId, amount, type, recipient, reference } = req.body;

    // Verify authenticated user matches userId
    if (req.user.id !== userId) {
      return res.status(403).json({
        status: 'error',
        message: 'Cannot send notification for another user'
      });
    }

    await sendTransactionNotification(userId, {
      amount,
      type,
      recipient,
      reference
    });

    res.status(200).json({
      status: 'success',
      message: 'Transaction notification sent'
    });
  } catch (err) {
    logger.error('Transaction notification error:', err);
    res.status(500).json({ error: 'Failed to send transaction notification' });
  }
});

// Send security alert (critical)
router.post('/security-alert', [
  body('userId').isUUID(),
  body('type').isString().notEmpty(),
  body('location').optional().isString(),
  body('device').optional().isString(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { userId, type, location, device } = req.body;

    // Verify authenticated user matches userId
    if (req.user.id !== userId) {
      return res.status(403).json({
        status: 'error',
        message: 'Cannot send notification for another user'
      });
    }

    await sendSecurityAlert(userId, {
      type,
      location,
      device
    });

    res.status(200).json({
      status: 'success',
      message: 'Security alert sent'
    });
  } catch (err) {
    logger.error('Security alert error:', err);
    res.status(500).json({ error: 'Failed to send security alert' });
  }
});

// Send purchase notification (airtime/data)
router.post('/purchase', [
  body('userId').isUUID(),
  body('type').isIn(['airtime', 'data']),
  body('amount').isNumeric(),
  body('recipient').isString().notEmpty(),
  body('provider').isString().notEmpty(),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { userId, type, amount, recipient, provider } = req.body;

    // Verify authenticated user matches userId
    if (req.user.id !== userId) {
      return res.status(403).json({
        status: 'error',
        message: 'Cannot send notification for another user'
      });
    }

    await sendPurchaseNotification(userId, {
      type,
      amount,
      recipient,
      provider
    });

    res.status(200).json({
      status: 'success',
      message: 'Purchase notification sent'
    });
  } catch (err) {
    logger.error('Purchase notification error:', err);
    res.status(500).json({ error: 'Failed to send purchase notification' });
  }
});

// Admin: Get all FCM tokens for a user
router.get('/user-tokens/:userId', async (req, res) => {
  // TODO: Add admin authentication/authorization here
  const { userId } = req.params;
  try {
    const tokens = await getUserFcmTokens(userId);
    res.status(200).json({ tokens });
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch FCM tokens' });
  }
});

module.exports = router;
