const express = require('express');
const router = express.Router();
const realtimeService = require('../services/realtimeService');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

/**
 * @route   GET /api/v1/health
 * @desc    Get system health status
 * @access  Public
 */
router.get('/', async (req, res) => {
  // Simple health check for AI Brain compatibility
  if (req.headers['user-agent'] && req.headers['user-agent'].includes('PayVendy-AI-Brain')) {
    return res.status(200).json({
      success: true,
      status: 'healthy',
      timestamp: new Date().toISOString(),
      message: 'AI Brain connection successful'
    });
  }
  const startTime = Date.now();
  
  try {
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {},
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    };

    // Check database connection
    try {
      const supabase = getSupabase();
      const { error } = await supabase
        .from('users')
        .select('count', { count: 'exact', head: true });
      
      healthStatus.services.database = {
        status: error && error.code !== 'PGRST116' ? 'unhealthy' : 'healthy',
        error: error && error.code !== 'PGRST116' ? error.message : null,
        responseTime: Date.now() - startTime
      };
    } catch (dbError) {
      healthStatus.services.database = {
        status: 'unhealthy',
        error: dbError.message,
        responseTime: Date.now() - startTime
      };
      healthStatus.status = 'degraded';
    }

    // Check realtime service
    const realtimeStatus = realtimeService.getStatus();
    healthStatus.services.realtime = {
      status: realtimeStatus.isInitialized ? 'healthy' : 'unhealthy',
      isInitialized: realtimeStatus.isInitialized,
      hasChannel: realtimeStatus.hasChannel,
      pollingFallbackActive: realtimeStatus.pollingFallbackActive,
      lastUserCheck: realtimeStatus.lastUserCheck,
      connectedClients: realtimeStatus.connectedClients
    };

    // If realtime is using polling fallback, mark as degraded
    if (realtimeStatus.pollingFallbackActive) {
      healthStatus.services.realtime.status = 'degraded';
      healthStatus.services.realtime.message = 'Using polling fallback due to realtime connection issues';
      if (healthStatus.status === 'healthy') {
        healthStatus.status = 'degraded';
      }
    }

    // Overall status determination
    const serviceStatuses = Object.values(healthStatus.services).map(s => s.status);
    if (serviceStatuses.includes('unhealthy')) {
      healthStatus.status = 'unhealthy';
    } else if (serviceStatuses.includes('degraded')) {
      healthStatus.status = 'degraded';
    }

    const responseTime = Date.now() - startTime;
    healthStatus.responseTime = responseTime;

    // Log health check
    logger.debug('Health check completed', {
      status: healthStatus.status,
      responseTime,
      services: Object.keys(healthStatus.services).reduce((acc, key) => {
        acc[key] = healthStatus.services[key].status;
        return acc;
      }, {})
    });

    // Set appropriate HTTP status code
    const httpStatus = healthStatus.status === 'healthy' ? 200 : 
                      healthStatus.status === 'degraded' ? 200 : 503;

    res.status(httpStatus).json({
      success: healthStatus.status !== 'unhealthy',
      data: healthStatus
    });

  } catch (error) {
    logger.error('Health check failed:', error);
    
    res.status(503).json({
      success: false,
      error: 'Health check failed',
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
        responseTime: Date.now() - startTime
      }
    });
  }
});

/**
 * @route   GET /api/v1/health/realtime
 * @desc    Get detailed realtime service status
 * @access  Public
 */
router.get('/realtime', async (req, res) => {
  const startTime = Date.now();
  
  try {
    const realtimeStatus = realtimeService.getStatus();
    
    // Test realtime connection if requested
    let connectionTest = null;
    if (req.query.test === 'true') {
      try {
        const supabase = getSupabase();
        connectionTest = await testRealtimeConnection(supabase);
      } catch (error) {
        connectionTest = {
          success: false,
          error: error.message
        };
      }
    }

    const response = {
      status: realtimeStatus.isInitialized ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime,
      details: {
        ...realtimeStatus,
        connectionTest
      }
    };

    // Provide recommendations if issues detected
    if (!realtimeStatus.hasChannel && realtimeStatus.pollingFallbackActive) {
      response.recommendations = [
        'Check if realtime is enabled for the users table in Supabase Dashboard',
        'Verify Row Level Security policies allow service role access',
        'Ensure SUPABASE_SERVICE_ROLE_KEY is correctly set',
        'Check realtime quotas in Supabase Dashboard',
        'Run: npm run verify-realtime for detailed diagnosis'
      ];
    }

    res.json({
      success: response.status !== 'unhealthy',
      data: response
    });

  } catch (error) {
    logger.error('Realtime health check failed:', error);
    
    res.status(503).json({
      success: false,
      error: 'Realtime health check failed',
      data: {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
        responseTime: Date.now() - startTime
      }
    });
  }
});

/**
 * Test realtime connection
 */
async function testRealtimeConnection(supabase) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve({ success: false, error: 'Connection timeout' });
    }, 5000);

    const testChannel = supabase.channel('health_test');
    
    testChannel.subscribe((status) => {
      clearTimeout(timeout);
      testChannel.unsubscribe();
      
      if (status === 'SUBSCRIBED') {
        resolve({ success: true, status });
      } else {
        resolve({ success: false, status, error: `Connection failed with status: ${status}` });
      }
    });
  });
}

module.exports = router;