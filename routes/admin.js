/**
 * PayVendy Admin API Routes
 *
 * Admin endpoints for basic administrative functions.
 */

const express = require('express');
const { body, param, validationResult } = require('express-validator');
const userService = require('../services/userService');
const authService = require('../services/authService');
const logger = require('../utils/logger');
const crashReportsDashboard = require('./admin/crashReportsDashboard');
const airtimeConfigRoutes = require('./admin/airtimeConfig');
// Lazy load provider management routes to avoid initialization issues
let providerManagementRoutes = null;
const bcrypt = require('bcryptjs');
const rateLimit = require('express-rate-limit');
const { getSupabase } = require('../config/database');
const { handleValidationErrors } = require('../middleware/validation');
const {
  ADMIN_PERMISSIONS,
  requirePermission,
  requireAllPermissions,
  logAdminActivity
} = require('../middleware/adminPermissions');

const router = express.Router();

// Rate limiting for admin login
const adminLoginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // limit each IP to 20 requests per windowMs (increased for development)
  message: {
    status: 'error',
    message: 'Too many admin login attempts, please try again later.',
    retryAfter: 900
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for localhost in development
    const isLocalhost = req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === '::ffff:127.0.0.1';
    return process.env.NODE_ENV === 'development' && isLocalhost;
  }
});

/**
 * @route   POST /api/v1/admin/login
 * @desc    Admin login with email and PIN
 * @access  Public
 */
router.post('/login',
  adminLoginLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address'),
    body('pin')
      .isLength({ min: 4, max: 6 })
      .isNumeric()
      .withMessage('PIN must be 4-6 digits')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, pin } = req.body;
      const clientIP = req.ip || req.connection.remoteAddress;

      logger.info(`Admin login attempt for email: ${email} from IP: ${clientIP}`);

      // Find user by email
      const user = await userService.findByEmail(email);
      if (!user) {
        logger.warn(`Admin login failed - user not found: ${email}`);
        return res.status(401).json({
          status: 'error',
          message: 'Invalid credentials'
        });
      }

      // Check if user has admin role
      if (user.role !== 'admin') {
        logger.warn(`Admin login failed - insufficient permissions: ${email}`);
        return res.status(403).json({
          status: 'error',
          message: 'Access denied. Admin privileges required.'
        });
      }

      // Check if user is active
      if (!user.isActive) {
        logger.warn(`Admin login failed - account inactive: ${email}`);
        return res.status(401).json({
          status: 'error',
          message: 'Account is inactive'
        });
      }

      // Check if account is locked
      if (user.lockUntil && new Date(user.lockUntil) > new Date()) {
        const lockTimeRemaining = Math.ceil((new Date(user.lockUntil) - new Date()) / (1000 * 60));
        logger.warn(`Admin login failed - account locked: ${email}`);
        return res.status(423).json({
          status: 'error',
          message: `Account is locked. Try again in ${lockTimeRemaining} minutes.`
        });
      }

      // Verify PIN
      if (!user.pin) {
        logger.warn(`Admin login failed - no PIN set: ${email}`);
        return res.status(401).json({
          status: 'error',
          message: 'PIN not set for this account'
        });
      }

      const isValidPin = await bcrypt.compare(pin, user.pin);
      if (!isValidPin) {
        // Increment login attempts
        await userService.incLoginAttempts(user.id);
        logger.warn(`Admin login failed - invalid PIN: ${email}`);
        return res.status(401).json({
          status: 'error',
          message: 'Invalid credentials'
        });
      }

      // Reset login attempts on successful login
      await userService.resetLoginAttempts(user.id);

      // Generate JWT tokens
      const tokens = await authService.generateTokens(user.id, {
        ipAddress: clientIP,
        userAgent: req.headers['user-agent'],
        deviceInfo: { type: 'admin-web', platform: 'web' }
      });

      // Update last login (using snake_case as expected by database)
      await userService.updateUser(user.id, {
        last_login: new Date().toISOString()
      });

      logger.info(`Admin login successful: ${email}`);

      res.status(200).json({
        status: 'success',
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            isActive: user.isActive,
            isEmailVerified: user.isEmailVerified,
            lastLogin: new Date().toISOString()
          },
          token: tokens.accessToken,
          refreshToken: tokens.refreshToken,
          expiresIn: tokens.expiresIn
        }
      });

    } catch (error) {
      logger.error('Admin login error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/dashboard/system-health
 * @desc    Get system health metrics for admin dashboard
 * @access  Private (Admin only)
 */
router.get('/dashboard/system-health',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const supabase = getSupabase();
      const startTime = Date.now();

      // Test database connectivity and performance
      const dbStart = Date.now();
      const { data: dbTest, error: dbError } = await supabase
        .from('users')
        .select('id', { count: 'exact', head: true });
      const dbResponseTime = Date.now() - dbStart;

      if (dbError) {
        logger.error('Database connectivity test failed:', dbError);
      }

      // Get active sessions count (with error handling)
      let activeSessions = { count: 0 };
      try {
        const sessionResult = await supabase
          .from('sessions')
          .select('id', { count: 'exact', head: true })
          .eq('is_active', true)
          .gte('expires_at', new Date().toISOString());

        if (!sessionResult.error) {
          activeSessions = sessionResult.data || { count: 0 };
        } else {
          logger.warn('Sessions query failed:', sessionResult.error);
        }
      } catch (sessionError) {
        logger.warn('Sessions table might not exist:', sessionError);
      }

      // Get recent error rate (from crash reports if available)
      let recentErrors = { count: 0 };
      try {
        const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const errorResult = await supabase
          .from('crash_reports')
          .select('id', { count: 'exact', head: true })
          .gte('created_at', last24Hours.toISOString());

        if (!errorResult.error) {
          recentErrors = errorResult.data || { count: 0 };
        } else {
          logger.warn('Crash reports query failed:', errorResult.error);
        }
      } catch (crashError) {
        logger.warn('Crash reports table might not exist:', crashError);
      }

      // Calculate uptime (simplified - in production, use actual monitoring)
      const uptime = process.uptime();
      const uptimeHours = Math.floor(uptime / 3600);
      const uptimePercentage = Math.min(99.9, 100 - (recentErrors?.count || 0) * 0.1);

      const totalResponseTime = Date.now() - startTime;

      const systemHealth = {
        overall: dbError ? 'critical' : (dbResponseTime > 1000 ? 'warning' : 'healthy'),
        uptime: `${uptimePercentage.toFixed(1)}%`,
        lastCheck: new Date().toISOString(),
        metrics: [
          {
            name: 'API Response Time',
            status: totalResponseTime < 200 ? 'healthy' : (totalResponseTime < 1000 ? 'warning' : 'critical'),
            value: `${totalResponseTime}ms`,
            description: 'Average response time'
          },
          {
            name: 'Database Performance',
            status: dbError ? 'critical' : (dbResponseTime < 500 ? 'healthy' : 'warning'),
            value: dbError ? 'Error' : `${dbResponseTime}ms`,
            description: 'Database query time'
          },
          {
            name: 'Error Rate',
            status: (recentErrors?.count || 0) < 10 ? 'healthy' : ((recentErrors?.count || 0) < 50 ? 'warning' : 'critical'),
            value: `${((recentErrors?.count || 0) / 1000 * 100).toFixed(2)}%`,
            description: 'Last 24 hours'
          },
          {
            name: 'Active Sessions',
            status: 'healthy',
            value: (activeSessions?.count || 0).toLocaleString(),
            description: 'Current active sessions'
          },
          {
            name: 'Server Uptime',
            status: 'healthy',
            value: uptimeHours > 0 ? `${uptimeHours}h` : `${Math.floor(uptime / 60)}m`,
            description: 'Server running time'
          }
        ]
      };

      res.status(200).json({
        status: 'success',
        data: systemHealth
      });

    } catch (error) {
      logger.error('System health check error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch system health data',
        data: {
          overall: 'critical',
          uptime: '0%',
          lastCheck: new Date().toISOString(),
          metrics: [
            {
              name: 'System Status',
              status: 'critical',
              value: 'Error',
              description: 'System health check failed'
            }
          ]
        }
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/dashboard/users
 * @desc    Get user statistics for admin dashboard
 * @access  Private (Admin only)
 */
router.get('/dashboard/users',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const supabase = getSupabase();

      // Get current date ranges
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Get user statistics
      const [
        totalUsers,
        activeUsers,
        newUsersToday,
        newUsersYesterday,
        newUsersThisWeek,
        recentActivity
      ] = await Promise.all([
        // Total users count
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true }),

        // Active users (logged in within last 30 days)
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true })
          .eq('is_active', true)
          .gte('last_login', lastMonth.toISOString()),

        // New users today
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true })
          .gte('created_at', today.toISOString()),

        // New users yesterday
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true })
          .gte('created_at', yesterday.toISOString())
          .lt('created_at', today.toISOString()),

        // New users this week
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true })
          .gte('created_at', lastWeek.toISOString()),

        // Recent activity (last 7 days)
        supabase
          .from('users')
          .select('created_at')
          .gte('created_at', lastWeek.toISOString())
          .order('created_at', { ascending: true })
      ]);

      // Process daily activity data
      const dailyActivity = {};
      recentActivity.data?.forEach(user => {
        const date = new Date(user.created_at).toISOString().split('T')[0];
        dailyActivity[date] = (dailyActivity[date] || 0) + 1;
      });

      // Fill in missing dates with 0
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        if (!dailyActivity[date]) {
          dailyActivity[date] = 0;
        }
      }

      const userStats = {
        totalUsers: totalUsers.count || 0,
        activeUsers: activeUsers.count || 0,
        newUsersToday: newUsersToday.count || 0,
        newUsersYesterday: newUsersYesterday.count || 0,
        newUsersThisWeek: newUsersThisWeek.count || 0,
        inactiveUsers: (totalUsers.count || 0) - (activeUsers.count || 0),
        recentActivity: Object.entries(dailyActivity).map(([date, count]) => ({
          date,
          newUsers: count,
          activeUsers: count // Simplified for now
        })).sort((a, b) => new Date(a.date) - new Date(b.date))
      };

      res.status(200).json({
        status: 'success',
        data: userStats
      });

    } catch (error) {
      logger.error('User dashboard data fetch error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch user statistics'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/dashboard/recent-activity
 * @desc    Get recent activity for admin dashboard
 * @access  Private (Admin only)
 */
router.get('/dashboard/recent-activity',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const supabase = getSupabase();
      const limit = parseInt(req.query.limit) || 10;

      // Get recent activities from multiple sources
      const [
        recentUsers,
        recentSessions,
        recentTransactions,
        recentSMS
      ] = await Promise.all([
        // Recent user registrations
        supabase
          .from('users')
          .select('id, first_name, last_name, phone_number, created_at')
          .order('created_at', { ascending: false })
          .limit(5),

        // Recent login sessions
        supabase
          .from('sessions')
          .select(`
            id,
            user_id,
            device_info,
            ip_address,
            created_at,
            users!inner(first_name, last_name, phone_number)
          `)
          .order('created_at', { ascending: false })
          .limit(5),

        // Recent transactions
        supabase
          .from('transactions')
          .select(`
            id,
            user_id,
            type,
            amount,
            status,
            created_at,
            users!inner(first_name, last_name, phone_number)
          `)
          .order('created_at', { ascending: false })
          .limit(5),

        // Recent SMS logs
        supabase
          .from('sms_logs')
          .select(`
            id,
            user_id,
            phone_number,
            message_type,
            status,
            created_at,
            users(first_name, last_name)
          `)
          .order('created_at', { ascending: false })
          .limit(5)
      ]);

      // Combine and format activities
      const activities = [];

      // Add user registrations
      recentUsers.data?.forEach(user => {
        activities.push({
          id: `user-${user.id}`,
          type: 'user_registration',
          title: 'New User Registration',
          description: `${user.first_name || 'User'} ${user.last_name || ''} registered`.trim(),
          user: {
            name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Unknown User',
            phone: user.phone_number
          },
          timestamp: user.created_at,
          icon: 'user-plus',
          color: 'success'
        });
      });

      // Add login sessions
      recentSessions.data?.forEach(session => {
        const deviceType = session.device_info?.type || 'unknown';
        activities.push({
          id: `session-${session.id}`,
          type: 'user_login',
          title: 'User Login',
          description: `Login from ${deviceType} device`,
          user: {
            name: `${session.users?.first_name || ''} ${session.users?.last_name || ''}`.trim() || 'Unknown User',
            phone: session.users?.phone_number
          },
          metadata: {
            ip: session.ip_address,
            device: deviceType
          },
          timestamp: session.created_at,
          icon: 'login',
          color: 'primary'
        });
      });

      // Add transactions
      recentTransactions.data?.forEach(transaction => {
        activities.push({
          id: `transaction-${transaction.id}`,
          type: 'transaction',
          title: `${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)} Transaction`,
          description: `₦${transaction.amount} - ${transaction.status}`,
          user: {
            name: `${transaction.users?.first_name || ''} ${transaction.users?.last_name || ''}`.trim() || 'Unknown User',
            phone: transaction.users?.phone_number
          },
          metadata: {
            amount: transaction.amount,
            status: transaction.status,
            type: transaction.type
          },
          timestamp: transaction.created_at,
          icon: 'currency',
          color: transaction.status === 'completed' ? 'success' : (transaction.status === 'failed' ? 'danger' : 'warning')
        });
      });

      // Add SMS activities
      recentSMS.data?.forEach(sms => {
        activities.push({
          id: `sms-${sms.id}`,
          type: 'sms_sent',
          title: 'SMS Sent',
          description: `${sms.message_type} message - ${sms.status}`,
          user: {
            name: sms.users ? `${sms.users.first_name || ''} ${sms.users.last_name || ''}`.trim() : 'Unknown User',
            phone: sms.phone_number
          },
          metadata: {
            type: sms.message_type,
            status: sms.status
          },
          timestamp: sms.created_at,
          icon: 'message',
          color: sms.status === 'delivered' ? 'success' : (sms.status === 'failed' ? 'danger' : 'warning')
        });
      });

      // Sort by timestamp and limit
      const sortedActivities = activities
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, limit);

      res.status(200).json({
        status: 'success',
        data: {
          activities: sortedActivities,
          total: activities.length
        }
      });

    } catch (error) {
      logger.error('Recent activity fetch error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch recent activity data'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/users
 * @desc    Get all users with pagination and filtering for admin management
 * @access  Private (Admin only) - Requires READ_USERS permission
 */
router.get('/users',
  authService.protect,
  authService.restrictTo('admin'),
  requirePermission(ADMIN_PERMISSIONS.READ_USERS, {
    logActivity: true,
    activityAction: 'VIEW_USERS_LIST',
    activityTarget: 'users'
  }),
  async (req, res) => {
    try {
      const supabase = getSupabase();

      // Parse query parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const search = req.query.search || '';
      const status = req.query.status; // 'active', 'inactive', 'locked'
      const sortBy = req.query.sortBy || 'created_at';
      const sortOrder = req.query.sortOrder || 'desc';

      const offset = (page - 1) * limit;

      // Build query
      let query = supabase
        .from('users')
        .select(`
          id,
          first_name,
          last_name,
          email,
          phone_number,
          is_active,
          is_email_verified,
          is_phone_verified,
          role,
          balance,
          created_at,
          updated_at,
          last_login,
          login_attempts,
          lock_until
        `, { count: 'exact' });

      // Apply search filter
      if (search) {
        query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,phone_number.ilike.%${search}%`);
      }

      // Apply status filter
      if (status === 'active') {
        query = query.eq('is_active', true);
      } else if (status === 'inactive') {
        query = query.eq('is_active', false);
      } else if (status === 'locked') {
        query = query.not('lock_until', 'is', null);
      }

      // Apply sorting and pagination
      query = query
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range(offset, offset + limit - 1);

      const { data: users, error, count } = await query;

      if (error) {
        throw error;
      }

      // Calculate pagination info
      const totalPages = Math.ceil(count / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      res.status(200).json({
        status: 'success',
        data: {
          users: users || [],
          pagination: {
            currentPage: page,
            totalPages,
            totalUsers: count,
            hasNextPage,
            hasPrevPage,
            limit
          }
        }
      });

    } catch (error) {
      logger.error('Admin users list error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch users'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/users/:id
 * @desc    Get detailed user information by ID
 * @access  Private (Admin only)
 */
router.get('/users/:id',
  authService.protect,
  authService.restrictTo('admin'),
  [
    param('id').isUUID().withMessage('Valid user ID is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const supabase = getSupabase();
      const userId = req.params.id;

      // Get user details with related data
      const [userResult, sessionsResult, transactionsResult] = await Promise.all([
        // User basic info
        supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .single(),

        // Recent sessions
        supabase
          .from('sessions')
          .select('id, device_info, ip_address, created_at, expires_at')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(10),

        // Recent transactions
        supabase
          .from('transactions')
          .select('id, type, amount, status, description, created_at')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(10)
      ]);

      if (userResult.error) {
        if (userResult.error.code === 'PGRST116') {
          return res.status(404).json({
            status: 'error',
            message: 'User not found'
          });
        }
        throw userResult.error;
      }

      const user = userResult.data;
      const sessions = sessionsResult.data || [];
      const transactions = transactionsResult.data || [];

      res.status(200).json({
        status: 'success',
        data: {
          user,
          recentSessions: sessions,
          recentTransactions: transactions
        }
      });

    } catch (error) {
      logger.error('Admin user detail error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch user details'
      });
    }
  }
);

/**
 * @route   PUT /api/v1/admin/users/:id
 * @desc    Update user information (admin only)
 * @access  Private (Admin only)
 */
router.put('/users/:id',
  authService.protect,
  authService.restrictTo('admin'),
  [
    param('id').isUUID().withMessage('Valid user ID is required'),
    body('firstName').optional().isString().trim().isLength({ min: 1, max: 50 }),
    body('lastName').optional().isString().trim().isLength({ min: 1, max: 50 }),
    body('email').optional().isEmail().normalizeEmail(),
    body('phoneNumber').optional().isMobilePhone(),
    body('isActive').optional().isBoolean(),
    body('role').optional().isIn(['user', 'admin']),
    body('balance').optional().isNumeric()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const supabase = getSupabase();
      const userId = req.params.id;
      const updateData = req.body;

      // Convert camelCase to snake_case for database
      const dbUpdateData = {};
      if (updateData.firstName !== undefined) dbUpdateData.first_name = updateData.firstName;
      if (updateData.lastName !== undefined) dbUpdateData.last_name = updateData.lastName;
      if (updateData.email !== undefined) dbUpdateData.email = updateData.email;
      if (updateData.phoneNumber !== undefined) dbUpdateData.phone_number = updateData.phoneNumber;
      if (updateData.isActive !== undefined) dbUpdateData.is_active = updateData.isActive;
      if (updateData.role !== undefined) dbUpdateData.role = updateData.role;
      if (updateData.balance !== undefined) dbUpdateData.balance = parseFloat(updateData.balance);

      // Add updated timestamp
      dbUpdateData.updated_at = new Date().toISOString();

      // Update user
      const { data: updatedUser, error } = await supabase
        .from('users')
        .update(dbUpdateData)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return res.status(404).json({
            status: 'error',
            message: 'User not found'
          });
        }
        throw error;
      }

      logger.info(`Admin ${req.user.id} updated user ${userId}`, {
        adminId: req.user.id,
        targetUserId: userId,
        updatedFields: Object.keys(dbUpdateData)
      });

      res.status(200).json({
        status: 'success',
        message: 'User updated successfully',
        data: { user: updatedUser }
      });

    } catch (error) {
      logger.error('Admin user update error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to update user'
      });
    }
  }
);

/**
 * @route   POST /api/v1/admin/users/:id/toggle-status
 * @desc    Toggle user active status (activate/deactivate)
 * @access  Private (Admin only)
 */
router.post('/users/:id/toggle-status',
  authService.protect,
  authService.restrictTo('admin'),
  [
    param('id').isUUID().withMessage('Valid user ID is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const supabase = getSupabase();
      const userId = req.params.id;

      // Get current user status
      const { data: currentUser, error: fetchError } = await supabase
        .from('users')
        .select('is_active, first_name, last_name')
        .eq('id', userId)
        .single();

      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          return res.status(404).json({
            status: 'error',
            message: 'User not found'
          });
        }
        throw fetchError;
      }

      // Toggle status
      const newStatus = !currentUser.is_active;

      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({
          is_active: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      const action = newStatus ? 'activated' : 'deactivated';
      logger.info(`Admin ${req.user.id} ${action} user ${userId}`, {
        adminId: req.user.id,
        targetUserId: userId,
        newStatus,
        userName: `${currentUser.first_name} ${currentUser.last_name}`
      });

      res.status(200).json({
        status: 'success',
        message: `User ${action} successfully`,
        data: {
          user: updatedUser,
          action
        }
      });

    } catch (error) {
      logger.error('Admin user status toggle error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to toggle user status'
      });
    }
  }
);

/**
 * @route   POST /api/v1/admin/users/:id/unlock
 * @desc    Unlock a locked user account
 * @access  Private (Admin only)
 */
router.post('/users/:id/unlock',
  authService.protect,
  authService.restrictTo('admin'),
  [
    param('id').isUUID().withMessage('Valid user ID is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const supabase = getSupabase();
      const userId = req.params.id;

      // Unlock user by clearing lock_until and resetting login attempts
      const { data: updatedUser, error } = await supabase
        .from('users')
        .update({
          lock_until: null,
          login_attempts: 0,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select('id, first_name, last_name, email')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return res.status(404).json({
            status: 'error',
            message: 'User not found'
          });
        }
        throw error;
      }

      logger.info(`Admin ${req.user.id} unlocked user ${userId}`, {
        adminId: req.user.id,
        targetUserId: userId,
        userName: `${updatedUser.first_name} ${updatedUser.last_name}`
      });

      res.status(200).json({
        status: 'success',
        message: 'User account unlocked successfully',
        data: { user: updatedUser }
      });

    } catch (error) {
      logger.error('Admin user unlock error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to unlock user account'
      });
    }
  }
);

/**
 * @route   DELETE /api/v1/admin/users/:id
 * @desc    Delete a user account (soft delete by deactivating)
 * @access  Private (Admin only)
 */
router.delete('/users/:id',
  authService.protect,
  authService.restrictTo('admin'),
  [
    param('id').isUUID().withMessage('Valid user ID is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const supabase = getSupabase();
      const userId = req.params.id;

      // Prevent admin from deleting themselves
      if (userId === req.user.id) {
        return res.status(400).json({
          status: 'error',
          message: 'Cannot delete your own account'
        });
      }

      // Get user info before deletion
      const { data: userToDelete, error: fetchError } = await supabase
        .from('users')
        .select('first_name, last_name, email, role')
        .eq('id', userId)
        .single();

      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          return res.status(404).json({
            status: 'error',
            message: 'User not found'
          });
        }
        throw fetchError;
      }

      // Prevent deletion of other admin accounts
      if (userToDelete.role === 'admin') {
        return res.status(403).json({
          status: 'error',
          message: 'Cannot delete admin accounts'
        });
      }

      // Soft delete by deactivating the account
      const { data: deletedUser, error: deleteError } = await supabase
        .from('users')
        .update({
          is_active: false,
          email: `deleted_${Date.now()}_${userToDelete.email}`, // Prevent email conflicts
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select('id, first_name, last_name')
        .single();

      if (deleteError) {
        throw deleteError;
      }

      logger.warn(`Admin ${req.user.id} deleted user ${userId}`, {
        adminId: req.user.id,
        deletedUserId: userId,
        deletedUserName: `${userToDelete.first_name} ${userToDelete.last_name}`,
        deletedUserEmail: userToDelete.email
      });

      res.status(200).json({
        status: 'success',
        message: 'User account deleted successfully',
        data: { user: deletedUser }
      });

    } catch (error) {
      logger.error('Admin user deletion error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to delete user account'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/users/stats
 * @desc    Get user statistics for admin dashboard
 * @access  Private (Admin only)
 */
router.get('/users/stats',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const supabase = getSupabase();

      // Get current date ranges
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Get user statistics
      const [
        totalUsers,
        activeUsers,
        newUsersToday,
        newUsersYesterday,
        newUsersThisWeek,
        recentActivity
      ] = await Promise.all([
        // Total users count
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true }),

        // Active users (logged in within last 30 days)
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true })
          .eq('is_active', true)
          .gte('last_login', lastMonth.toISOString()),

        // New users today
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true })
          .gte('created_at', today.toISOString()),

        // New users yesterday
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true })
          .gte('created_at', yesterday.toISOString())
          .lt('created_at', today.toISOString()),

        // New users this week
        supabase
          .from('users')
          .select('id', { count: 'exact', head: true })
          .gte('created_at', lastWeek.toISOString()),

        // Recent activity (last 7 days)
        supabase
          .from('users')
          .select('created_at')
          .gte('created_at', lastWeek.toISOString())
          .order('created_at', { ascending: true })
      ]);

      // Process daily activity data
      const dailyActivity = {};
      recentActivity.data?.forEach(user => {
        const date = new Date(user.created_at).toISOString().split('T')[0];
        dailyActivity[date] = (dailyActivity[date] || 0) + 1;
      });

      // Fill in missing dates with 0
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        if (!dailyActivity[date]) {
          dailyActivity[date] = 0;
        }
      }

      const userStats = {
        totalUsers: totalUsers.count || 0,
        activeUsers: activeUsers.count || 0,
        newUsersToday: newUsersToday.count || 0,
        newUsersYesterday: newUsersYesterday.count || 0,
        newUsersThisWeek: newUsersThisWeek.count || 0,
        inactiveUsers: (totalUsers.count || 0) - (activeUsers.count || 0),
        recentActivity: Object.entries(dailyActivity).map(([date, count]) => ({
          date,
          newUsers: count,
          activeUsers: count // Simplified for now
        })).sort((a, b) => new Date(a.date) - new Date(b.date))
      };

      res.status(200).json({
        status: 'success',
        data: userStats
      });

    } catch (error) {
      logger.error('User stats fetch error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch user statistics'
      });
    }
  }
);

/**
 * @route   POST /api/v1/admin/users/:id/reset-password
 * @desc    Reset user password (admin only)
 * @access  Private (Admin only)
 */
router.post('/users/:id/reset-password',
  authService.protect,
  authService.restrictTo('admin'),
  [
    param('id').isUUID().withMessage('Valid user ID is required'),
    body('newPassword').optional().isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    body('sendNotification').optional().isBoolean()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const supabase = getSupabase();
      const userId = req.params.id;
      const { newPassword, sendNotification = true } = req.body;

      // Get user info
      const { data: user, error: fetchError } = await supabase
        .from('users')
        .select('id, first_name, last_name, email, phone_number')
        .eq('id', userId)
        .single();

      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          return res.status(404).json({
            status: 'error',
            message: 'User not found'
          });
        }
        throw fetchError;
      }

      // Generate new password if not provided
      const password = newPassword || Math.random().toString(36).slice(-8);
      const hashedPassword = await bcrypt.hash(password, 12);

      // Update user password
      const { error: updateError } = await supabase
        .from('users')
        .update({
          password: hashedPassword,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) {
        throw updateError;
      }

      logger.info(`Admin ${req.user.id} reset password for user ${userId}`, {
        adminId: req.user.id,
        targetUserId: userId,
        userName: `${user.first_name} ${user.last_name}`
      });

      res.status(200).json({
        status: 'success',
        message: 'Password reset successfully',
        data: {
          userId: user.id,
          newPassword: newPassword ? undefined : password, // Only return generated password
          notificationSent: sendNotification
        }
      });

    } catch (error) {
      logger.error('Admin password reset error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to reset user password'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/permissions/me
 * @desc    Get current admin user's permissions
 * @access  Private (Admin only)
 */
router.get('/permissions/me',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      // For now, return all permissions for admin users
      // In a more complex system, you'd fetch user-specific permissions from database
      const adminPermissions = {
        users: {
          read: true,
          write: true,
          delete: true
        },
        settings: {
          read: true,
          write: true
        },
        reports: {
          read: true,
          write: true
        },
        system: {
          read: true,
          write: true,
          maintenance: true
        }
      };

      res.status(200).json({
        status: 'success',
        data: {
          userId: req.user.id,
          role: req.user.role,
          permissions: adminPermissions,
          lastUpdated: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Admin permissions fetch error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch admin permissions'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/settings/system
 * @desc    Get system settings for admin dashboard
 * @access  Private (Admin only)
 */
router.get('/settings/system',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      // Return default system settings
      // In a real application, these would be stored in database
      const systemSettings = {
        appName: 'PayVendy',
        appVersion: '1.0.0',
        maintenanceMode: false,
        maxLoginAttempts: 5,
        sessionTimeout: 24, // hours
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false,
          maxAge: 90 // days
        },
        security: {
          twoFactorRequired: false,
          ipWhitelist: [],
          maxConcurrentSessions: 3
        },
        notifications: {
          emailEnabled: true,
          smsEnabled: true,
          pushEnabled: false
        },
        features: {
          userRegistration: true,
          passwordReset: true,
          emailVerification: true,
          phoneVerification: true
        },
        limits: {
          maxTransactionAmount: 1000000, // ₦1,000,000
          dailyTransactionLimit: 5000000, // ₦5,000,000
          maxUsersPerDay: 1000
        },
        lastUpdated: new Date().toISOString(),
        updatedBy: 'system'
      };

      res.status(200).json({
        status: 'success',
        data: systemSettings
      });
    } catch (error) {
      logger.error('System settings fetch error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch system settings'
      });
    }
  }
);

/**
 * @route   PUT /api/v1/admin/settings/system
 * @desc    Update system settings
 * @access  Private (Admin only)
 */
router.put('/settings/system',
  authService.protect,
  authService.restrictTo('admin'),
  [
    body('appName').optional().isString().trim().isLength({ min: 1, max: 100 }),
    body('maintenanceMode').optional().isBoolean(),
    body('maxLoginAttempts').optional().isInt({ min: 1, max: 10 }),
    body('sessionTimeout').optional().isInt({ min: 1, max: 168 }), // max 1 week
    body('passwordPolicy.minLength').optional().isInt({ min: 6, max: 50 }),
    body('passwordPolicy.requireUppercase').optional().isBoolean(),
    body('passwordPolicy.requireLowercase').optional().isBoolean(),
    body('passwordPolicy.requireNumbers').optional().isBoolean(),
    body('passwordPolicy.requireSpecialChars').optional().isBoolean()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      // In a real application, you would save these settings to database
      // For now, just return success with the updated settings
      const updatedSettings = {
        ...req.body,
        lastUpdated: new Date().toISOString(),
        updatedBy: req.user.id
      };

      logger.info(`Admin ${req.user.id} updated system settings`, {
        adminId: req.user.id,
        updatedFields: Object.keys(req.body)
      });

      res.status(200).json({
        status: 'success',
        message: 'System settings updated successfully',
        data: updatedSettings
      });
    } catch (error) {
      logger.error('System settings update error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to update system settings'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/debug/user-info
 * @desc    Debug endpoint to check current user's role and permissions
 * @access  Private (Any authenticated user)
 */
router.get('/debug/user-info',
  authService.protect,
  async (req, res) => {
    try {
      res.status(200).json({
        status: 'success',
        data: {
          userId: req.user.id,
          role: req.user.role,
          isActive: req.user.is_active,
          email: req.user.email,
          firstName: req.user.first_name,
          lastName: req.user.last_name,
          isLocked: req.isLocked || false,
          lockUntil: req.user.lock_until,
          rawUser: req.user // Full user object for debugging
        }
      });
    } catch (error) {
      logger.error('Debug user info error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to get user info'
      });
    }
  }
);

// Mount crash reports dashboard routes
router.use('/crash-reports', crashReportsDashboard);

// Mount provider management routes (lazy loaded)
router.use('/providers', (req, res, next) => {
  if (!providerManagementRoutes) {
    providerManagementRoutes = require('./admin/providerManagement');
  }
  providerManagementRoutes(req, res, next);
});

// Mount airtime configuration routes
router.use('/airtime-config', airtimeConfigRoutes);

// Mount airtime provider routes
const airtimeProviderRoutes = require('../routes/airtimeProvider');
router.use('/airtime-providers', airtimeProviderRoutes);

/**
 * @route   POST /api/v1/admin/users/:id/revoke-tokens
 * @desc    Revoke all JWT tokens for a user (force logout)
 * @access  Private (Admin only)
 */
router.post('/users/:id/revoke-tokens',
  authService.protect,
  authService.restrictTo('admin'),
  [
    param('id').isUUID().withMessage('Valid user ID is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { id } = req.params;

      logger.info('🔐 [ADMIN] Revoking tokens for user:', { userId: id, adminId: req.user.id });

      // Check if user exists
      const user = await userService.findById(id);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Revoke all tokens by updating token_version
      const supabase = getSupabase();
      const { error } = await supabase
        .from('users')
        .update({
          token_version: (user.token_version || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        throw new Error(error.message);
      }

      logger.info('✅ [ADMIN] Tokens revoked successfully for user:', { userId: id });

      res.json({
        status: 'success',
        message: `All tokens revoked for user ${user.first_name} ${user.last_name}. User will be forced to login again.`,
        data: {
          userId: id,
          userEmail: user.email,
          revokedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('❌ [ADMIN] Error revoking tokens:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to revoke user tokens'
      });
    }
  }
);

/**
 * @route   POST /api/v1/admin/users/:id/blacklist-whatsapp
 * @desc    Blacklist user from WhatsApp bot (bot won't respond)
 * @access  Private (Admin only)
 */
router.post('/users/:id/blacklist-whatsapp',
  authService.protect,
  authService.restrictTo('admin'),
  [
    param('id').isUUID().withMessage('Valid user ID is required'),
    body('reason').optional().isString().trim().isLength({ max: 500 }).withMessage('Reason must be less than 500 characters')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      logger.info('🚫 [ADMIN] Blacklisting user from WhatsApp bot:', { userId: id, adminId: req.user.id });

      // Check if user exists
      const user = await userService.findById(id);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Update user with WhatsApp blacklist
      const supabase = getSupabase();
      const { error } = await supabase
        .from('users')
        .update({
          is_whatsapp_blocked: true,
          whatsapp_block_reason: reason || 'Blocked by admin',
          whatsapp_blocked_at: new Date().toISOString(),
          whatsapp_blocked_by: req.user.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        throw new Error(error.message);
      }

      logger.info('✅ [ADMIN] User blacklisted from WhatsApp bot:', { userId: id });

      res.json({
        status: 'success',
        message: `User ${user.first_name} ${user.last_name} has been blacklisted from WhatsApp bot`,
        data: {
          userId: id,
          userEmail: user.email,
          phoneNumber: user.phone_number,
          reason: reason || 'Blocked by admin',
          blacklistedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('❌ [ADMIN] Error blacklisting user from WhatsApp:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to blacklist user from WhatsApp bot'
      });
    }
  }
);

/**
 * @route   POST /api/v1/admin/users/:id/unblacklist-whatsapp
 * @desc    Remove user from WhatsApp bot blacklist
 * @access  Private (Admin only)
 */
router.post('/users/:id/unblacklist-whatsapp',
  authService.protect,
  authService.restrictTo('admin'),
  [
    param('id').isUUID().withMessage('Valid user ID is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { id } = req.params;

      logger.info('✅ [ADMIN] Removing user from WhatsApp blacklist:', { userId: id, adminId: req.user.id });

      // Check if user exists
      const user = await userService.findById(id);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Remove WhatsApp blacklist
      const supabase = getSupabase();
      const { error } = await supabase
        .from('users')
        .update({
          is_whatsapp_blocked: false,
          whatsapp_block_reason: null,
          whatsapp_blocked_at: null,
          whatsapp_blocked_by: null,
          whatsapp_unblocked_at: new Date().toISOString(),
          whatsapp_unblocked_by: req.user.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        throw new Error(error.message);
      }

      logger.info('✅ [ADMIN] User removed from WhatsApp blacklist:', { userId: id });

      res.json({
        status: 'success',
        message: `User ${user.first_name} ${user.last_name} has been removed from WhatsApp blacklist`,
        data: {
          userId: id,
          userEmail: user.email,
          phoneNumber: user.phone_number,
          unblockedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('❌ [ADMIN] Error removing user from WhatsApp blacklist:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to remove user from WhatsApp blacklist'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/whatsapp/blacklisted-users
 * @desc    Get list of users blacklisted from WhatsApp bot
 * @access  Private (Admin only)
 */
router.get('/whatsapp/blacklisted-users',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      logger.info('📋 [ADMIN] Getting WhatsApp blacklisted users:', { adminId: req.user.id });

      const supabase = getSupabase();
      const { data: blacklistedUsers, error } = await supabase
        .from('users')
        .select(`
          id,
          first_name,
          last_name,
          email,
          phone_number,
          is_whatsapp_blocked,
          whatsapp_block_reason,
          whatsapp_blocked_at,
          whatsapp_blocked_by,
          created_at
        `)
        .eq('is_whatsapp_blocked', true)
        .order('whatsapp_blocked_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      res.json({
        status: 'success',
        message: 'WhatsApp blacklisted users retrieved successfully',
        data: {
          users: blacklistedUsers.map(user => ({
            ...user,
            phone_number: user.phone_number?.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3')
          })),
          count: blacklistedUsers.length
        }
      });

    } catch (error) {
      logger.error('❌ [ADMIN] Error getting WhatsApp blacklisted users:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to get WhatsApp blacklisted users'
      });
    }
  }
);

module.exports = router;


