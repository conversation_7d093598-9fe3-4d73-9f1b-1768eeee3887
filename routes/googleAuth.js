const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const firebaseService = require('../services/firebaseService');
const userService = require('../services/userService');
const authService = require('../services/authService');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for Google auth
const googleAuthLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 requests per windowMs
  message: {
    status: 'error',
    message: 'Too many Google authentication attempts. Please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route   POST /api/v1/auth/google
 * @desc    Authenticate user with Google ID token
 * @access  Public
 */
router.post('/google',
  googleAuthLimiter,
  [
    body('idToken')
      .notEmpty()
      .withMessage('Google ID token is required'),
    body('deviceInfo')
      .optional()
      .isObject()
      .withMessage('Device info must be an object')
  ],
  async (req, res) => {
    try {
      console.log('🔐 [GOOGLE-AUTH] Google authentication request received');
      console.log('📧 Client IP:', req.ip);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ [GOOGLE-AUTH] Validation errors:', errors.array());
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { idToken, deviceInfo } = req.body;
      const clientIP = req.ip || req.connection.remoteAddress;

      console.log('🔍 [GOOGLE-AUTH] Verifying Google ID token...');
      console.log('🔍 [GOOGLE-AUTH] Token length:', idToken ? idToken.length : 'null');
      console.log('🔍 [GOOGLE-AUTH] Token preview:', idToken ? `${idToken.substring(0, 50)}...` : 'null');

      // Verify Google ID token with Firebase
      const googleUser = await firebaseService.verifyGoogleToken(idToken);
      
      console.log('✅ [GOOGLE-AUTH] Google token verified');
      console.log('👤 [GOOGLE-AUTH] Google user:', {
        uid: googleUser.uid,
        email: googleUser.email,
        name: googleUser.name,
        emailVerified: googleUser.emailVerified
      });

      // Check if user exists in our database
      let user = await userService.findByEmail(googleUser.email);

      if (!user) {
        console.log('➕ [GOOGLE-AUTH] User not found, creating new user...');
        
        // Create new user with Google info
        user = await userService.createUserWithGoogle({
          email: googleUser.email,
          name: googleUser.name,
          picture: googleUser.picture,
          googleUid: googleUser.uid,
          providerId: googleUser.providerId,
          emailVerified: googleUser.emailVerified
        });
        
        console.log('✅ [GOOGLE-AUTH] New user created:', user.id);
      } else {
        console.log('✅ [GOOGLE-AUTH] Existing user found:', user.id);
        
        // Update user's Google info if needed
        const updateData = {};
        if (!user.googleUid) {
          updateData.googleUid = googleUser.uid;
          updateData.providerId = googleUser.providerId;
          updateData.authProvider = 'google'; // Update auth provider when linking Google
        }
        if (!user.isEmailVerified && googleUser.emailVerified) {
          updateData.isEmailVerified = true;
        }
        if (googleUser.picture && user.picture !== googleUser.picture) {
          updateData.picture = googleUser.picture;
        }
        // Only update name if user doesn't have ANY name set OR their profile was originally from Google
        // This preserves user-customized names from manual setup
        const hasManuallySetName = user.profile_source === 'manual' && 
                                  (user.firstName && user.firstName.trim() !== '');
        
        if (googleUser.name && !hasManuallySetName && 
            (!user.firstName || user.firstName.trim() === '') && 
            (!user.lastName || user.lastName.trim() === '')) {
          const nameParts = googleUser.name.split(' ');
          updateData.first_name = nameParts[0] || '';
          updateData.last_name = nameParts.slice(1).join(' ') || '';
          updateData.profile_source = 'google'; // Mark as Google-sourced
          console.log('🔄 [GOOGLE-AUTH] Setting name from Google (user had no name):', {
            firstName: updateData.first_name,
            lastName: updateData.last_name
          });
        } else if (hasManuallySetName) {
          console.log('✋ [GOOGLE-AUTH] Preserving manually set user name, not overwriting with Google name');
        } else if (googleUser.name) {
          console.log('✋ [GOOGLE-AUTH] Preserving existing user name, not overwriting with Google name');
        }

        if (Object.keys(updateData).length > 0) {
          console.log('🔄 [GOOGLE-AUTH] Updating user with Google info...');
          user = await userService.updateUser(user.id, updateData);
        }
      }

      // Check if user is locked
      if (user.isLocked) {
        console.log('🔒 [GOOGLE-AUTH] User account is locked');
        return res.status(423).json({
          status: 'error',
          message: 'Account is temporarily locked. Please contact support.'
        });
      }

      // Add IP address and device info
      await userService.addIpAddress(user.id, clientIP);
      if (deviceInfo) {
        await userService.addDevice(user.id, deviceInfo);
      }

      // Reset login attempts on successful authentication
      await userService.resetLoginAttempts(user.id);

      // Generate JWT tokens
      const tokens = await authService.generateTokens(user.id, {
        ipAddress: clientIP,
        userAgent: req.headers['user-agent']
      });

      console.log('🎉 [GOOGLE-AUTH] Google authentication successful');
      logger.info(`Successful Google authentication for ${googleUser.email} from ${clientIP}`);

      // Restore full response with user and tokens
      console.log('➡️ [GOOGLE-AUTH] Sending full user/tokens response to client');
      res.status(200).json({
        status: 'success',
        message: 'Google authentication successful',
        data: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            picture: user.picture,
            isEmailVerified: user.is_email_verified,
            isPhoneVerified: user.is_phone_verified,
            balance: user.balance,
            createdAt: user.created_at,
            provider: 'google'
          },
          tokens,
          isNewUser: !user.is_phone_verified // Consider new if phone not verified
        }
      });
      return;

    } catch (error) {
      console.log('💥 [GOOGLE-AUTH] Error in Google authentication:', error);
      logger.error('Google authentication error:', error);

      // Add explicit log before sending error response
      console.log('➡️ [GOOGLE-AUTH] Sending error response to client');
      // Handle specific Firebase errors
      if (error.message.includes('Invalid Google token')) {
        return res.status(401).json({
          status: 'error',
          message: 'Invalid Google authentication. Please try again.'
        });
      }

      res.status(500).json({
        status: 'error',
        message: 'Internal server error during Google authentication'
      });
    }
  }
);

/**
 * @route   POST /api/v1/auth/google/link
 * @desc    Link Google account to existing user
 * @access  Private
 */
router.post('/link',
  authService.protect.bind(authService),
  googleAuthLimiter,
  [
    body('idToken')
      .notEmpty()
      .withMessage('Google ID token is required')
  ],
  async (req, res) => {
    try {
      console.log('🔗 [GOOGLE-AUTH] Link Google account request');
      console.log('👤 [GOOGLE-AUTH] User ID:', req.user.id);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { idToken } = req.body;

      // Verify Google ID token
      const googleUser = await firebaseService.verifyGoogleToken(idToken);

      // Check if Google account is already linked to another user
      const existingUser = await userService.findByGoogleUid(googleUser.uid);
      if (existingUser && existingUser.id !== req.user.id) {
        return res.status(409).json({
          status: 'error',
          message: 'This Google account is already linked to another user'
        });
      }

      // Update current user with Google info
      const updateData = {
        googleUid: googleUser.uid,
        providerId: googleUser.providerId,
        picture: googleUser.picture
      };

      if (googleUser.emailVerified && !req.user.isEmailVerified) {
        updateData.isEmailVerified = true;
      }

      const updatedUser = await userService.updateUser(req.user.id, updateData);

      console.log('✅ [GOOGLE-AUTH] Google account linked successfully');
      logger.info(`Google account linked for user ${req.user.id}`);

      res.status(200).json({
        status: 'success',
        message: 'Google account linked successfully',
        data: {
          user: {
            id: updatedUser.id,
            email: updatedUser.email,
            firstName: updatedUser.first_name,
            lastName: updatedUser.last_name,
            picture: updatedUser.picture,
            isEmailVerified: updatedUser.is_email_verified,
            isPhoneVerified: updatedUser.is_phone_verified,
            googleLinked: true
          }
        }
      });

    } catch (error) {
      console.log('💥 [GOOGLE-AUTH] Error linking Google account:', error);
      logger.error('Google account linking error:', error);

      res.status(500).json({
        status: 'error',
        message: 'Failed to link Google account'
      });
    }
  }
);

module.exports = router;
