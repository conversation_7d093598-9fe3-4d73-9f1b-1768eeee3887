const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const userService = require('../services/userService');
const authService = require('../services/authService');
const logger = require('../utils/logger');
const { getSupabase } = require('../config/database');

const router = express.Router();

// Rate limiting for setup endpoints
const setupLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 setup requests per windowMs
  message: {
    status: 'error',
    message: 'Too many setup attempts. Please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route   GET /api/v1/setup/test
 * @desc    Test endpoint to verify setup routes work without auth
 * @access  Public
 */
router.get('/test', (req, res) => {
  console.log('🧪 [SETUP] Test endpoint hit - no auth required');
  res.json({
    status: 'success',
    message: 'Setup routes are working without authentication',
    timestamp: new Date().toISOString()
  });
});

/**
 * @route   GET /api/v1/setup/status
 * @desc    Get user setup status
 * @access  Public (no authentication required)
 */
router.get('/status', async (req, res) => {
  const startTime = Date.now();

  try {
    console.log('🔍 [SETUP] Setup status request received (PUBLIC ENDPOINT)');
    console.log('🔗 [SETUP] Request URL:', req.originalUrl);
    console.log('🔑 [SETUP] Authorization header:', req.headers.authorization ? 'Present' : 'Not present');

    // FIRST: Clear any expired locks automatically
    await userService.clearExpiredLocks();

    let user = null;
    let userId = null;

    // Try to get user from token if provided (but don't fail if token is invalid)
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
      try {
        const token = req.headers.authorization.split(' ')[1];
        console.log('🔍 [SETUP] Attempting to decode token for user info...');

        // Decode token without verification to get user ID
        const jwt = require('jsonwebtoken');
        const decoded = jwt.decode(token);

        if (decoded && decoded.id) {
          userId = decoded.id;
          console.log('🆔 [SETUP] User ID from token:', userId);

          // Get user from database
          const supabase = getSupabase();
          const { data: userData, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', userId)
            .single();

          if (!error && userData) {
            user = userData;
            console.log('✅ [SETUP] User found:', user.id);
          } else {
            console.log('⚠️ [SETUP] User not found in database:', error?.message);
          }
        }
      } catch (tokenError) {
        console.log('⚠️ [SETUP] Token decode failed (continuing without auth):', tokenError.message);
      }
    }

    console.log('👤 [SETUP] Final user:', user?.id || 'none');

    // If no user found, return default setup status (needs setup)
    if (!user || !user.id) {
      console.log('ℹ️ [SETUP] No user found - returning default setup status');
      return res.status(200).json({
        status: 'success',
        data: {
          setupStatus: {
            setupComplete: false,
            hasEmailVerification: false,
            hasPinSetup: false,
            hasBiometricSetup: false,
            hasProfileSetup: false,
            currentStep: 'email_verification',
            completedSteps: [],
            nextStep: 'email_verification'
          },
          user: null
        }
      });
    }

    // CRITICAL: Handle locked accounts - they should still get their setup status
    // This prevents locked users from being forced through setup again
    const isLocked = user.lock_until && new Date(user.lock_until) > new Date();
    if (isLocked) {
      console.log('🔒 [SETUP] User is locked but preserving setup status:', user.id);
      // Continue with normal setup status logic below - don't return early
    }
    
    // User already fetched above, no need to fetch again
    console.log(`✅ [SETUP] Using user data for setup status: ${user.id}`);

    // Optimize setup status calculation
    const setupStatus = {
      hasPinSetup: !!user.pin, // Check if PIN is set (not null)
      hasBiometricSetup: user.biometric_enabled || user.biometricEnabled || false,
      hasProfileSetup: !!(user.first_name || user.firstName), // Check both field names
      isEmailVerified: user.is_email_verified || user.isEmailVerified || false,
      isPhoneVerified: user.is_phone_verified || user.isPhoneVerified || false,
      setupComplete: false
    };

    // Determine if setup is complete
    setupStatus.setupComplete = setupStatus.hasPinSetup && 
                                setupStatus.hasProfileSetup && 
                                (setupStatus.isEmailVerified || setupStatus.isPhoneVerified);

    // Prepare optimized response data
    const responseData = {
      setupStatus,
      user: {
        id: user.id,
        firstName: user.first_name || user.firstName,
        lastName: user.last_name || user.lastName,
        email: user.email,
        phoneNumber: user.phone_number || user.phoneNumber,
        isEmailVerified: user.is_email_verified || user.isEmailVerified || false,
        isPhoneVerified: user.is_phone_verified || user.isPhoneVerified || false,
        picture: user.picture
      }
    };

    const totalTime = Date.now() - startTime;
    console.log(`⏱️ [SETUP] Total setup status request took: ${totalTime}ms`);
    
    console.log('🔍 [SETUP] Setup status response:', {
      setupStatus,
      user: {
        id: user.id,
        firstName: user.first_name || user.firstName,
        lastName: user.last_name || user.lastName,
        email: user.email,
        picture: user.picture
      },
      performanceMs: totalTime
    });

    // Add performance headers for debugging
    res.set('X-Response-Time', `${totalTime}ms`);

    res.status(200).json({
      status: 'success',
      data: responseData
    });

  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error(`❌ [SETUP] Get setup status error (${totalTime}ms):`, error);
    logger.error('Get setup status error:', error);
    
    res.status(500).json({
      status: 'error',
      message: 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/v1/setup/pin
 * @desc    Set up transaction PIN for new users
 * @access  Private
 */
router.post('/pin',
  authService.protect, // Require authentication for PIN setup
  setupLimiter,
  [
    body('pin')
      .isLength({ min: 4, max: 4 })
      .isNumeric()
      .withMessage('PIN must be exactly 4 digits'),
    body('confirmPin')
      .isLength({ min: 4, max: 4 })
      .isNumeric()
      .withMessage('Confirm PIN must be exactly 4 digits')
      .custom((value, { req }) => {
        if (value !== req.body.pin) {
          throw new Error('PIN confirmation does not match');
        }
        return true;
      })
  ],
  async (req, res) => {
    try {
      console.log('🔐 [SETUP] PIN setup request received');
      console.log('👤 User ID:', req.user.id);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ [SETUP] Validation errors:', errors.array());
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { pin } = req.body;
      const userId = req.user.id;

      // Get current user
      const user = await userService.findById(userId);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Check if PIN is already set
      if (user.pin) {
        console.log('⚠️ [SETUP] PIN already set for user:', userId);
        return res.status(400).json({
          status: 'error',
          message: 'PIN is already set. Please verify your existing PIN to continue.',
          data: {
            pinAlreadySet: true,
            nextStep: 'verify-pin',
            user: {
              id: user.id,
              firstName: user.firstName,
              lastName: user.lastName,
              email: user.email,
              phoneNumber: user.phoneNumber,
              picture: user.picture
            }
          }
        });
      }

      // Hash and set the new PIN
      const bcrypt = require('bcryptjs');
      const hashedPin = await bcrypt.hash(pin, parseInt(process.env.BCRYPT_ROUNDS) || 12);
      
      await userService.updateUser(userId, { 
        pin: hashedPin,
        pin_setup_at: new Date().toISOString()
      });

      console.log('✅ [SETUP] PIN set successfully for user:', userId);
      logger.info(`PIN set successfully for user ${userId}`);

      res.status(200).json({
        status: 'success',
        message: 'Transaction PIN set successfully',
        data: {
          pinSetup: true,
          setupAt: new Date().toISOString()
        }
      });

    } catch (error) {
      console.log('💥 [SETUP] Error setting PIN:', error);
      logger.error('Set PIN error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/setup/verify-pin
 * @desc    Verify existing PIN for setup completion with account lock protection
 * @access  Private
 */
router.post('/verify-pin',
  authService.protect, // Require authentication for PIN verification
  setupLimiter,
  [
    body('pin')
      .isLength({ min: 4, max: 4 })
      .isNumeric()
      .withMessage('PIN must be exactly 4 digits')
  ],
  async (req, res) => {
    try {
      console.log('🔐 [SETUP] PIN verification request received');
      console.log('👤 User ID:', req.user.id);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ [SETUP] Validation errors:', errors.array());
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { pin } = req.body;
      const userId = req.user.id;

      // FIRST: Clear any expired locks automatically
      await userService.clearExpiredLocks();

      // Get current user with fresh data (after potential lock clearing)
      const user = await userService.findById(userId);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Check if user account is active
      if (!user.isActive) {
        console.log('❌ [SETUP] User account is deactivated:', userId);
        return res.status(401).json({
          status: 'error',
          message: 'User account is deactivated.',
          code: 'ACCOUNT_DEACTIVATED'
        });
      }

      // Check if account is currently locked (after clearing expired locks)
      const isLocked = user.lockUntil && new Date(user.lockUntil) > new Date();
      if (isLocked) {
        const lockTimeRemaining = new Date(user.lockUntil) - new Date();
        const minutesRemaining = Math.ceil(lockTimeRemaining / (60 * 1000));

        console.log('🔒 [SETUP] Account is locked until:', user.lockUntil);
        return res.status(423).json({
          status: 'error',
          message: `Account is temporarily locked due to too many failed PIN attempts. Please try again in ${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''}.`,
          code: 'ACCOUNT_LOCKED',
          lockUntil: user.lockUntil,
          minutesRemaining
        });
      }

      // Check if PIN is set
      if (!user.pin) {
        console.log('⚠️ [SETUP] PIN not set for user:', userId);
        return res.status(400).json({
          status: 'error',
          message: 'PIN is not set. Please set up your PIN first.'
        });
      }

      // Verify PIN
      const bcrypt = require('bcryptjs');
      const isPinValid = await bcrypt.compare(pin, user.pin);

      if (!isPinValid) {
        console.log('❌ [SETUP] Invalid PIN for user:', userId);

        // Increment login attempts and potentially lock account
        await userService.incLoginAttempts(userId);

        // Get updated user data to check if account is now locked
        const updatedUser = await userService.findById(userId);
        const isNowLocked = updatedUser.lockUntil && new Date(updatedUser.lockUntil) > new Date();

        if (isNowLocked) {
          const lockTimeRemaining = new Date(updatedUser.lockUntil) - new Date();
          const minutesRemaining = Math.ceil(lockTimeRemaining / (60 * 1000));

          console.log('🔒 [SETUP] Account locked after failed attempt:', userId);
          return res.status(423).json({
            status: 'error',
            message: `Too many failed PIN attempts. Account is now locked for ${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''}.`,
            code: 'ACCOUNT_LOCKED',
            lockUntil: updatedUser.lockUntil,
            minutesRemaining
          });
        } else {
          // Show remaining attempts
          const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5;
          const remainingAttempts = maxAttempts - (updatedUser.loginAttempts || 0);

          return res.status(400).json({
            status: 'error',
            message: `Invalid PIN. ${remainingAttempts} attempt${remainingAttempts !== 1 ? 's' : ''} remaining before account lock.`,
            code: 'INVALID_PIN',
            remainingAttempts
          });
        }
      }

      // PIN is valid - reset login attempts and proceed
      console.log('✅ [SETUP] PIN verified successfully for user:', userId);
      await userService.resetLoginAttempts(userId);

      console.log('✅ [SETUP] PIN verified successfully for user:', userId);
      logger.info(`PIN verified successfully for user ${userId}`);

      // Get setup status
      const setupStatus = {
        hasPinSetup: true,
        hasBiometricSetup: user.biometricEnabled || false,
        hasProfileSetup: !!user.firstName, // Only firstName is required
        isEmailVerified: user.isEmailVerified || false,
        isPhoneVerified: user.isPhoneVerified || false,
        setupComplete: false
      };

      // Determine if setup is complete
      setupStatus.setupComplete = setupStatus.hasPinSetup && 
                                  setupStatus.hasProfileSetup && 
                                  (setupStatus.isEmailVerified || setupStatus.isPhoneVerified);

      res.status(200).json({
        status: 'success',
        message: 'PIN verified successfully',
        data: {
          pinVerified: true,
          setupStatus,
          user: {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            phoneNumber: user.phoneNumber,
            isEmailVerified: user.isEmailVerified,
            isPhoneVerified: user.isPhoneVerified,
            picture: user.picture,
            balance: user.balance
          },
          welcomeMessage: `Welcome back, ${user.firstName || 'User'}!`
        }
      });

    } catch (error) {
      console.log('💥 [SETUP] Error verifying PIN:', error);
      logger.error('Verify PIN error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/setup/change-pin
 * @desc    Change existing PIN
 * @access  Private
 */
router.post('/change-pin',
  authService.protect, // Require authentication for PIN change
  setupLimiter,
  [
    body('currentPin')
      .isLength({ min: 4, max: 4 })
      .isNumeric()
      .withMessage('Current PIN must be exactly 4 digits'),
    body('newPin')
      .isLength({ min: 4, max: 4 })
      .isNumeric()
      .withMessage('New PIN must be exactly 4 digits'),
    body('confirmPin')
      .isLength({ min: 4, max: 4 })
      .isNumeric()
      .withMessage('Confirm PIN must be exactly 4 digits')
      .custom((value, { req }) => {
        if (value !== req.body.newPin) {
          throw new Error('PIN confirmation does not match');
        }
        return true;
      })
  ],
  async (req, res) => {
    try {
      console.log('🔄 [SETUP] PIN change request received');
      console.log('👤 User ID:', req.user.id);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ [SETUP] Validation errors:', errors.array());
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { currentPin, newPin } = req.body;
      const userId = req.user.id;

      // Get current user
      const user = await userService.findById(userId);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Check if user account is active
      if (!user.isActive) {
        console.log('❌ [SETUP] User account is deactivated:', userId);
        return res.status(401).json({
          status: 'error',
          message: 'User account is deactivated.',
          code: 'ACCOUNT_DEACTIVATED'
        });
      }

      // Check if account is currently locked
      const isLocked = user.lockUntil && new Date(user.lockUntil) > new Date();
      if (isLocked) {
        const lockTimeRemaining = new Date(user.lockUntil) - new Date();
        const minutesRemaining = Math.ceil(lockTimeRemaining / (60 * 1000));

        console.log('🔒 [SETUP] Account is locked until:', user.lockUntil);
        return res.status(423).json({
          status: 'error',
          message: `Account is temporarily locked due to too many failed PIN attempts. Please try again in ${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''}.`,
          code: 'ACCOUNT_LOCKED',
          lockUntil: user.lockUntil,
          minutesRemaining
        });
      }

      // Check if PIN is set
      if (!user.pin) {
        console.log('⚠️ [SETUP] PIN not set for user:', userId);
        return res.status(400).json({
          status: 'error',
          message: 'PIN is not set. Please set up your PIN first.'
        });
      }

      // Verify current PIN
      const bcrypt = require('bcryptjs');
      const isCurrentPinValid = await bcrypt.compare(currentPin, user.pin);

      if (!isCurrentPinValid) {
        console.log('❌ [SETUP] Invalid current PIN for user:', userId);

        // Increment login attempts and potentially lock account
        await userService.incLoginAttempts(userId);

        // Get updated user data to check if account is now locked
        const updatedUser = await userService.findById(userId);
        const isNowLocked = updatedUser.lockUntil && new Date(updatedUser.lockUntil) > new Date();

        if (isNowLocked) {
          const lockTimeRemaining = new Date(updatedUser.lockUntil) - new Date();
          const minutesRemaining = Math.ceil(lockTimeRemaining / (60 * 1000));

          console.log('🔒 [SETUP] Account locked after failed PIN change attempt:', userId);
          return res.status(423).json({
            status: 'error',
            message: `Too many failed PIN attempts. Account is now locked for ${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''}.`,
            code: 'ACCOUNT_LOCKED',
            lockUntil: updatedUser.lockUntil,
            minutesRemaining
          });
        } else {
          // Show remaining attempts
          const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5;
          const remainingAttempts = maxAttempts - (updatedUser.loginAttempts || 0);

          return res.status(400).json({
            status: 'error',
            message: `Current PIN is incorrect. ${remainingAttempts} attempt${remainingAttempts !== 1 ? 's' : ''} remaining before account lock.`,
            code: 'INVALID_PIN',
            remainingAttempts
          });
        }
      }

      // Current PIN is valid - reset login attempts before proceeding
      await userService.resetLoginAttempts(userId);

      // Hash and update with new PIN
      const hashedNewPin = await bcrypt.hash(newPin, parseInt(process.env.BCRYPT_ROUNDS) || 12);
      
      await userService.updateUser(userId, { 
        pin: hashedNewPin,
        pin_updated_at: new Date().toISOString()
      });

      console.log('✅ [SETUP] PIN changed successfully for user:', userId);
      logger.info(`PIN changed successfully for user ${userId}`);

      res.status(200).json({
        status: 'success',
        message: 'PIN changed successfully',
        data: {
          pinChanged: true,
          changedAt: new Date().toISOString()
        }
      });

    } catch (error) {
      console.log('💥 [SETUP] Error changing PIN:', error);
      logger.error('Change PIN error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/setup/biometric
 * @desc    Enable/disable biometric authentication
 * @access  Private
 */
router.post('/biometric',
  authService.protect, // Require authentication for biometric setup
  setupLimiter,
  [
    body('enabled')
      .isBoolean()
      .withMessage('Enabled must be a boolean value'),
    body('biometricType')
      .optional()
      .isIn(['fingerprint', 'face', 'voice', 'iris'])
      .withMessage('Invalid biometric type'),
    body('deviceInfo')
      .optional()
      .isObject()
      .withMessage('Device info must be an object')
  ],
  async (req, res) => {
    try {
      console.log('👆 [SETUP] Biometric setup request received');
      console.log('👤 User ID:', req.user.id);
      console.log('📱 Request body:', JSON.stringify(req.body, null, 2));

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ [SETUP] Validation errors:', errors.array());
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { enabled, biometricType, deviceInfo } = req.body;
      const userId = req.user.id;

      // Get current user
      const user = await userService.findById(userId);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Prepare update data
      const updateData = {
        biometric_enabled: enabled,
        biometric_setup_at: new Date().toISOString()
      };

      if (enabled) {
        updateData.biometric_type = biometricType || 'fingerprint';
        if (deviceInfo) {
          updateData.biometric_device_info = deviceInfo;
        }
      } else {
        // If disabling, clear biometric data
        updateData.biometric_type = null;
        updateData.biometric_device_info = null;
      }

      // Update user biometric settings
      await userService.updateUser(userId, updateData);

      console.log(`✅ [SETUP] Biometric ${enabled ? 'enabled' : 'disabled'} for user:`, userId);
      logger.info(`Biometric ${enabled ? 'enabled' : 'disabled'} for user ${userId}`);

      res.status(200).json({
        status: 'success',
        message: `Biometric authentication ${enabled ? 'enabled' : 'disabled'} successfully`,
        data: {
          biometricEnabled: enabled,
          biometricType: enabled ? (biometricType || 'fingerprint') : null,
          setupAt: new Date().toISOString()
        }
      });

    } catch (error) {
      console.log('💥 [SETUP] Error setting biometric:', error);
      logger.error('Set biometric error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/setup/profile
 * @desc    Complete profile setup
 * @access  Private
 */
router.post('/profile',
  authService.protect, // Require authentication for profile setup
  setupLimiter,
  [
    body('firstName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
      .trim()
      .optional()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters if provided'),
    body('dateOfBirth')
      .optional()
      .isISO8601()
      .withMessage('Date of birth must be a valid date'),
    body('avatar')
      .optional()
      .custom((value) => {
        // Allow URLs or local asset paths
        if (typeof value === 'string' && (
          value.startsWith('http://') ||
          value.startsWith('https://') ||
          value.startsWith('assets/')
        )) {
          return true;
        }
        throw new Error('Avatar must be a valid URL or local asset path');
      })
  ],
  async (req, res) => {
    try {
      console.log('👤 [SETUP] Profile setup request received');
      console.log('👤 User ID:', req.user.id);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ [SETUP] Validation errors:', errors.array());
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { firstName, lastName, dateOfBirth, avatar } = req.body;
      const userId = req.user.id;

      // Get current user
      const user = await userService.findById(userId);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Prepare update data
      const updateData = {
        first_name: firstName,
        last_name: lastName,
        profile_source: 'manual', // Track that this was manually set by user
        profile_setup_at: new Date().toISOString()
      };

      if (dateOfBirth) {
        updateData.date_of_birth = dateOfBirth;
      }

      if (avatar) {
        updateData.picture = avatar;
      }

      // Update user profile
      const updatedUser = await userService.updateUser(userId, updateData);

      console.log('✅ [SETUP] Profile setup completed for user:', userId);
      logger.info(`Profile setup completed for user ${userId}`);

      res.status(200).json({
        status: 'success',
        message: 'Profile setup completed successfully',
        data: {
          user: {
            id: updatedUser.id,
            firstName: updatedUser.firstName,
            lastName: updatedUser.lastName,
            email: updatedUser.email,
            phoneNumber: updatedUser.phoneNumber,
            picture: updatedUser.picture,
            dateOfBirth: updatedUser.dateOfBirth
          },
          profileSetup: true,
          setupAt: new Date().toISOString()
        }
      });

    } catch (error) {
      console.log('💥 [SETUP] Error setting up profile:', error);
      logger.error('Profile setup error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/setup/complete
 * @desc    Mark setup as complete and redirect to main app
 * @access  Private
 */
router.post('/complete', authService.protect, async (req, res) => {
  try {
    console.log('🎉 [SETUP] Setup completion request received');
    console.log('👤 User ID:', req.user.id);

    const userId = req.user.id;

    // Get current user and verify setup is actually complete
    const user = await userService.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    // Verify required setup steps
    const hasPinSetup = user.pin && user.pin !== '0000';
    const hasProfileSetup = !!user.firstName;
    const hasVerification = user.isEmailVerified || user.isPhoneVerified;

    if (!hasPinSetup || !hasProfileSetup || !hasVerification) {
      return res.status(400).json({
        status: 'error',
        message: 'Setup is not complete. Please complete all required steps.',
        data: {
          missing: {
            pin: !hasPinSetup,
            profile: !hasProfileSetup,
            verification: !hasVerification
          }
        }
      });
    }

    // Mark setup as complete
    await userService.updateUser(userId, {
      setup_completed: true,
      setup_completed_at: new Date().toISOString()
    });

    console.log('🎉 [SETUP] Setup marked as complete for user:', userId);
    logger.info(`Setup completed for user ${userId}`);

    res.status(200).json({
      status: 'success',
      message: 'Setup completed successfully! Welcome to Vendy!',
      data: {
        setupComplete: true,
        completedAt: new Date().toISOString(),
        user: {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
          picture: user.picture
        }
      }
    });

  } catch (error) {
    console.log('💥 [SETUP] Error completing setup:', error);
    logger.error('Complete setup error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Internal server error'
    });
  }
});

module.exports = router;
