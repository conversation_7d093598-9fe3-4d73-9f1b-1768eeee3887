const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const authService = require('../services/authService');
const logger = require('../utils/logger');
const { getSupabase } = require('../config/database');

const router = express.Router();

// Rate limiting for crash reports - more restrictive to prevent spam
const crashReportLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 3, // limit each IP to 3 crash reports per 5 minutes
  message: {
    error: 'Too many crash reports, please try again later.',
    retryAfter: 300
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// In-memory cache to prevent duplicate crash reports
const recentCrashReports = new Map();
const DUPLICATE_WINDOW = 5 * 60 * 1000; // 5 minutes

// Circuit breaker for crash report loops
const crashReportCircuitBreaker = new Map();
const CIRCUIT_BREAKER_THRESHOLD = 5; // Max reports per user per 10 minutes
const CIRCUIT_BREAKER_WINDOW = 10 * 60 * 1000; // 10 minutes
const CIRCUIT_BREAKER_COOLDOWN = 30 * 60 * 1000; // 30 minutes cooldown

// Clean up old entries periodically
setInterval(() => {
  const now = Date.now();

  // Clean up duplicate tracking
  for (const [key, timestamp] of recentCrashReports.entries()) {
    if (now - timestamp > DUPLICATE_WINDOW) {
      recentCrashReports.delete(key);
    }
  }

  // Clean up circuit breaker tracking
  for (const [key, data] of crashReportCircuitBreaker.entries()) {
    if (now - data.windowStart > CIRCUIT_BREAKER_WINDOW &&
        now - data.blockedUntil > CIRCUIT_BREAKER_COOLDOWN) {
      crashReportCircuitBreaker.delete(key);
    }
  }
}, 60 * 1000); // Clean up every minute

/**
 * Middleware to handle authentication errors gracefully for crash reports
 */
const crashReportAuthHandler = async (req, res, next) => {
  try {
    // Try normal authentication first
    await authService.protect(req, res, next);
  } catch (error) {
    // If authentication fails, check if this is an auth-related crash report
    const body = req.body;
    const isAuthError = body?.message && (
      body.message.toLowerCase().includes('session expired') ||
      body.message.toLowerCase().includes('token expired') ||
      body.message.toLowerCase().includes('unauthorized') ||
      body.message.toLowerCase().includes('login again')
    );

    if (isAuthError) {
      // For auth errors, allow the request to proceed without authentication
      // but mark it as anonymous
      req.isAnonymousAuthError = true;
      req.user = { id: null }; // Set null user for anonymous reporting
      next();
    } else {
      // For non-auth errors, require authentication
      throw error;
    }
  }
};

/**
 * @route   POST /api/v1/crash-reports
 * @desc    Submit crash report
 * @access  Private (with fallback for auth errors)
 */
router.post('/',
  crashReportAuthHandler,
  crashReportLimiter,
  [
    body('errorId')
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage('Error ID is required and must be a valid string'),
    body('message')
      .isString()
      .isLength({ min: 1, max: 1000 })
      .withMessage('Error message is required and must be less than 1000 characters'),
    body('timestamp')
      .isISO8601()
      .withMessage('Valid timestamp is required'),
    body('appVersion')
      .isString()
      .isLength({ min: 1, max: 20 })
      .withMessage('App version is required'),
    body('platform')
      .isIn(['ios', 'android'])
      .withMessage('Platform must be ios or android'),
    body('deviceInfo')
      .isObject()
      .withMessage('Device info must be an object'),
    body('breadcrumbs')
      .isArray()
      .withMessage('Breadcrumbs must be an array')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        errorId,
        message,
        stack,
        componentStack,
        timestamp,
        appVersion,
        platform,
        deviceInfo,
        breadcrumbs
      } = req.body;

      const userId = req.user.id;
      const isAnonymous = req.isAnonymousAuthError || !userId;

      // Create a unique key for deduplication based on user/IP, error message, and platform
      const userIdentifier = userId || req.ip;
      const deduplicationKey = `${userIdentifier}:${message.substring(0, 100)}:${platform}`;
      const circuitBreakerKey = `${userIdentifier}:${platform}`;
      const now = Date.now();

      // Check circuit breaker - if user is sending too many crash reports, block them temporarily
      if (crashReportCircuitBreaker.has(circuitBreakerKey)) {
        const circuitData = crashReportCircuitBreaker.get(circuitBreakerKey);

        // If still in cooldown period, reject the request
        if (circuitData.blocked && now < circuitData.blockedUntil) {
          const remainingCooldown = Math.ceil((circuitData.blockedUntil - now) / 60000);
          logger.warn(`Crash report blocked by circuit breaker for ${isAnonymous ? 'anonymous user' : 'user ' + userId}`, {
            remainingCooldownMinutes: remainingCooldown,
            platform,
            ip: req.ip
          });

          return res.status(429).json({
            status: 'error',
            message: 'Too many crash reports. Please wait before sending more.',
            retryAfter: remainingCooldown * 60
          });
        }

        // Reset circuit breaker if window has passed
        if (now - circuitData.windowStart > CIRCUIT_BREAKER_WINDOW) {
          circuitData.count = 0;
          circuitData.windowStart = now;
          circuitData.blocked = false;
        }

        // Increment counter
        circuitData.count++;

        // Check if threshold exceeded
        if (circuitData.count > CIRCUIT_BREAKER_THRESHOLD) {
          circuitData.blocked = true;
          circuitData.blockedUntil = now + CIRCUIT_BREAKER_COOLDOWN;

          logger.error(`Circuit breaker activated for ${isAnonymous ? 'anonymous user' : 'user ' + userId} - too many crash reports`, {
            count: circuitData.count,
            platform,
            ip: req.ip,
            cooldownMinutes: CIRCUIT_BREAKER_COOLDOWN / 60000
          });

          return res.status(429).json({
            status: 'error',
            message: 'Too many crash reports. Circuit breaker activated.',
            retryAfter: CIRCUIT_BREAKER_COOLDOWN / 1000
          });
        }
      } else {
        // Initialize circuit breaker for this user
        crashReportCircuitBreaker.set(circuitBreakerKey, {
          count: 1,
          windowStart: now,
          blocked: false,
          blockedUntil: 0
        });
      }

      // Check if this is a duplicate crash report within the time window
      if (recentCrashReports.has(deduplicationKey)) {
        const lastReportTime = recentCrashReports.get(deduplicationKey);
        if (now - lastReportTime < DUPLICATE_WINDOW) {
          logger.info(`Duplicate crash report ignored from ${isAnonymous ? 'anonymous user' : 'user ' + userId}`, {
            errorId,
            message: message.substring(0, 50) + '...',
            platform,
            isAnonymous,
            timeSinceLastReport: Math.round((now - lastReportTime) / 1000) + 's'
          });

          // Add header to tell mobile app to stop sending duplicates
          res.set('X-Crash-Report-Status', 'duplicate-ignored');

          return res.status(200).json({
            status: 'success',
            message: 'Crash report received (duplicate ignored)',
            data: {
              errorId: errorId,
              duplicate: true
            }
          });
        }
      }

      // Record this crash report to prevent duplicates
      recentCrashReports.set(deduplicationKey, now);

      // Special handling for authentication-related errors
      const isAuthError = message.toLowerCase().includes('session expired') ||
                         message.toLowerCase().includes('token expired') ||
                         message.toLowerCase().includes('unauthorized') ||
                         message.toLowerCase().includes('login again');

      if (isAuthError) {
        logger.warn(`Authentication-related crash report from ${isAnonymous ? 'anonymous user' : 'user ' + userId}`, {
          errorId,
          message: message.substring(0, 100) + '...',
          platform,
          appVersion,
          isAnonymous,
          ip: req.ip,
          note: 'This may indicate a session management issue'
        });
      } else {
        logger.info(`Crash report received from ${isAnonymous ? 'anonymous user' : 'user ' + userId}`, {
          errorId,
          message: message.substring(0, 100) + '...',
          platform,
          appVersion,
          isAnonymous
        });
      }

      // For authentication errors, check if we already have a recent similar report
      // to avoid database spam
      if (isAuthError && !isAnonymous) {
        const supabase = getSupabase();
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();

        const { data: recentAuthErrors } = await supabase
          .from('crash_reports')
          .select('id')
          .eq('user_id', userId)
          .eq('platform', platform)
          .or('message.ilike.%session expired%,message.ilike.%token expired%,message.ilike.%login again%')
          .gte('created_at', fiveMinutesAgo)
          .limit(1);

        if (recentAuthErrors && recentAuthErrors.length > 0) {
          logger.info(`Skipping duplicate auth error crash report for user ${userId}`);

          // Add header to tell mobile app to stop sending auth error reports
          res.set('X-Crash-Report-Status', 'auth-error-duplicate');

          return res.status(200).json({
            status: 'success',
            message: 'Crash report received (auth error duplicate skipped)',
            data: {
              errorId: errorId,
              authErrorDuplicate: true
            }
          });
        }
      }

      // Store crash report in database
      const supabase = getSupabase();
      const { data, error } = await supabase
        .from('crash_reports')
        .insert([{
          error_id: errorId,
          user_id: isAnonymous ? null : userId,
          message: isAnonymous ? `[ANONYMOUS] ${message}` : message,
          stack: stack,
          component_stack: componentStack,
          timestamp: timestamp,
          app_version: appVersion,
          platform: platform,
          device_info: isAnonymous ? {
            ...deviceInfo,
            ip: req.ip,
            userAgent: req.headers['user-agent'],
            anonymous: true
          } : deviceInfo,
          breadcrumbs: breadcrumbs,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        logger.error('Failed to store crash report:', error);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to store crash report'
        });
      }

      // Log critical errors for immediate attention
      if (message.toLowerCase().includes('fatal') || 
          message.toLowerCase().includes('crash') ||
          stack?.includes('ReferenceError') ||
          stack?.includes('TypeError')) {
        logger.error('CRITICAL CRASH REPORT', {
          errorId,
          userId,
          message,
          platform,
          appVersion,
          deviceModel: deviceInfo?.model
        });
      }

      // Add success header
      res.set('X-Crash-Report-Status', 'received');

      res.status(200).json({
        status: 'success',
        message: 'Crash report received successfully',
        data: {
          reportId: data.id,
          errorId: errorId,
          isAnonymous: isAnonymous
        }
      });

    } catch (error) {
      logger.error('Crash report submission error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/v1/crash-reports
 * @desc    Get crash reports for admin
 * @access  Private (Admin only)
 */
router.get('/',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { page = 1, limit = 50, platform, severity } = req.query;
      const offset = (page - 1) * limit;

      const supabase = getSupabase();
      let query = supabase
        .from('crash_reports')
        .select(`
          id,
          error_id,
          user_id,
          message,
          timestamp,
          app_version,
          platform,
          device_info,
          created_at,
          users!inner(email, first_name, last_name)
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (platform) {
        query = query.eq('platform', platform);
      }

      const { data: reports, error } = await query;

      if (error) {
        logger.error('Failed to fetch crash reports:', error);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to fetch crash reports'
        });
      }

      // Get total count for pagination
      let countQuery = supabase
        .from('crash_reports')
        .select('id', { count: 'exact', head: true });

      if (platform) {
        countQuery = countQuery.eq('platform', platform);
      }

      const { count, error: countError } = await countQuery;

      if (countError) {
        logger.warn('Failed to get crash reports count:', countError);
      }

      res.status(200).json({
        status: 'success',
        data: {
          reports: reports || [],
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count || 0,
            pages: Math.ceil((count || 0) / limit)
          }
        }
      });

    } catch (error) {
      logger.error('Get crash reports error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/v1/crash-reports/:id
 * @desc    Get specific crash report details
 * @access  Private (Admin only)
 */
router.get('/:id',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { id } = req.params;

      const supabase = getSupabase();
      const { data: report, error } = await supabase
        .from('crash_reports')
        .select(`
          *,
          users!inner(email, first_name, last_name, phone_number)
        `)
        .eq('id', id)
        .single();

      if (error || !report) {
        return res.status(404).json({
          status: 'error',
          message: 'Crash report not found'
        });
      }

      res.status(200).json({
        status: 'success',
        data: report
      });

    } catch (error) {
      logger.error('Get crash report details error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   DELETE /api/v1/crash-reports/:id
 * @desc    Delete crash report
 * @access  Private (Admin only)
 */
router.delete('/:id',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { id } = req.params;

      const supabase = getSupabase();
      const { error } = await supabase
        .from('crash_reports')
        .delete()
        .eq('id', id);

      if (error) {
        logger.error('Failed to delete crash report:', error);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to delete crash report'
        });
      }

      logger.info(`Crash report ${id} deleted by admin ${req.user.id}`);

      res.status(200).json({
        status: 'success',
        message: 'Crash report deleted successfully'
      });

    } catch (error) {
      logger.error('Delete crash report error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/crash-reports/anonymous
 * @desc    Submit crash report without authentication (for auth-related errors)
 * @access  Public (with strict rate limiting)
 */
router.post('/anonymous',
  rateLimit({
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: 2, // limit each IP to 2 anonymous crash reports per 10 minutes
    message: {
      error: 'Too many anonymous crash reports, please try again later.',
      retryAfter: 600
    },
    standardHeaders: true,
    legacyHeaders: false,
  }),
  [
    body('errorId')
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage('Error ID is required and must be a valid string'),
    body('message')
      .isString()
      .isLength({ min: 1, max: 1000 })
      .withMessage('Error message is required and must be less than 1000 characters'),
    body('timestamp')
      .isISO8601()
      .withMessage('Valid timestamp is required'),
    body('appVersion')
      .isString()
      .isLength({ min: 1, max: 20 })
      .withMessage('App version is required'),
    body('platform')
      .isIn(['ios', 'android'])
      .withMessage('Platform must be ios or android'),
    body('deviceInfo')
      .isObject()
      .withMessage('Device info must be an object'),
    body('userIdentifier')
      .optional()
      .isString()
      .isLength({ max: 100 })
      .withMessage('User identifier must be a string')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const {
        errorId,
        message,
        stack,
        componentStack,
        timestamp,
        appVersion,
        platform,
        deviceInfo,
        breadcrumbs,
        userIdentifier
      } = req.body;

      // Only allow authentication-related errors through this endpoint
      const isAuthError = message.toLowerCase().includes('session expired') ||
                         message.toLowerCase().includes('token expired') ||
                         message.toLowerCase().includes('unauthorized') ||
                         message.toLowerCase().includes('login again') ||
                         message.toLowerCase().includes('authentication failed');

      if (!isAuthError) {
        return res.status(400).json({
          status: 'error',
          message: 'Anonymous crash reports are only allowed for authentication-related errors'
        });
      }

      logger.warn(`Anonymous authentication crash report received`, {
        errorId,
        message: message.substring(0, 100) + '...',
        platform,
        appVersion,
        userIdentifier: userIdentifier || 'unknown',
        ip: req.ip
      });

      // Store crash report in database without user_id
      const supabase = getSupabase();
      const { data, error } = await supabase
        .from('crash_reports')
        .insert([{
          error_id: errorId,
          user_id: null, // No user ID for anonymous reports
          message: `[ANONYMOUS] ${message}`,
          stack: stack,
          component_stack: componentStack,
          timestamp: timestamp,
          app_version: appVersion,
          platform: platform,
          device_info: {
            ...deviceInfo,
            userIdentifier: userIdentifier || 'anonymous',
            ip: req.ip
          },
          breadcrumbs: breadcrumbs,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        logger.error('Failed to store anonymous crash report:', error);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to store crash report'
        });
      }

      res.status(200).json({
        status: 'success',
        message: 'Anonymous crash report received successfully',
        data: {
          reportId: data.id,
          errorId: errorId
        }
      });

    } catch (error) {
      logger.error('Anonymous crash report submission error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

module.exports = router;
