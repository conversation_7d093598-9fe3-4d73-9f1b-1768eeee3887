// backend/routes/networkDetect.js
const express = require('express');
const router = express.Router();

// Nigerian network prefixes (not exhaustive, but covers most common)
const NETWORK_PREFIXES = [
  { name: 'mtn', prefixes: [
    '0803','0806','0703','0706','0813','0816','0810','0814','0903','0906','0913','0916','07025','07026','0704','07025','07026','0704','0703','0706','0803','0806','0810','0813','0814','0816','0903','0906','0913','0916'
  ] },
  { name: 'glo', prefixes: [
    '0805','0807','0705','0811','0815','0905','0915'
  ] },
  { name: 'airtel', prefixes: [
    '0802','0808','0708','0812','0701','0902','0907','0901','0912'
  ] },
  { name: '9<PERSON>', prefixes: [
    '0809','0817','0818','0909','0908'
  ] },
];

function normalizeNumber(number) {
  // Remove spaces, dashes, parentheses, country code
  let n = number.replace(/[^\d]/g, '');
  if (n.startsWith('234')) n = '0' + n.slice(3);
  if (n.length > 11) n = n.slice(-11); // last 11 digits
  return n;
}

function detectNetwork(number) {
  const n = normalizeNumber(number);
  for (const net of NETWORK_PREFIXES) {
    for (const prefix of net.prefixes) {
      if (n.startsWith(prefix)) return net.name;
    }
  }
  return 'unknown';
}

// POST or GET /api/network-detect?number=08031234567
router.get('/', (req, res) => {
  const { number } = req.query;
  if (!number) return res.status(400).json({ error: 'number is required' });
  const network = detectNetwork(number);
  res.json({ network });
});

router.post('/', (req, res) => {
  const { number } = req.body;
  if (!number) return res.status(400).json({ error: 'number is required' });
  const network = detectNetwork(number);
  res.json({ network });
});

module.exports = router;
