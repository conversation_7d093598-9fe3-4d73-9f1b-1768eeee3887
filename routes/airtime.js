/**
 * Airtime API Routes
 * 
 * Secure API routes for MTN airtime purchases via VTpass with comprehensive
 * security, validation, rate limiting, and monitoring.
 * 
 * Routes:
 * - POST /api/v1/airtime/purchase - Purchase MTN airtime
 * - GET /api/v1/airtime/history - Get purchase history
 * - GET /api/v1/airtime/providers - Get available providers
 * - GET /api/v1/airtime/limits - Get user limits and restrictions
 * 
 * Security Features:
 * - JWT authentication required
 * - Payment-specific security middleware
 * - Rate limiting for purchase operations
 * - Input validation and sanitization
 * - Fraud detection integration
 * - Comprehensive audit logging
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, query, validationResult } = require('express-validator');

// Import controllers and middleware
const { 
  purchaseAirtime, 
  getPurchaseHistory, 
  getValidationRules 
} = require('../controllers/airtimeController');

const authService = require('../services/authService');
const { 
  validateTransactionLimits, 
  detectFraud, 
  createPaymentRateLimit 
} = require('../middleware/paymentSecurity');

const logger = require('../utils/logger');
const vtpassConfig = require('../config/vtpass');

const router = express.Router();

// =====================================================
// MIDDLEWARE SETUP
// =====================================================

/**
 * Authentication middleware - All routes require valid JWT
 */
router.use(authService.protect);

/**
 * User existence check - Ensure user account is active
 */
router.use(async (req, res, next) => {
  try {
    if (!req.user || !req.user.is_active) {
      logger.warn('🚫 [AIRTIME_ROUTES] Inactive user attempted access:', {
        userId: req.user?.id,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        message: 'Account is not active',
        code: 'ACCOUNT_INACTIVE'
      });
    }
    next();
  } catch (error) {
    logger.error('❌ [AIRTIME_ROUTES] User check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
});

/**
 * Rate limiting for airtime purchase operations
 * More restrictive than general API rate limiting
 */
const airtimePurchaseRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: (req) => {
    // Verified users get higher limits
    return req.user?.is_email_verified && req.user?.is_phone_verified ? 20 : 10;
  },
  keyGenerator: (req) => `airtime_purchase:${req.user.id}:${req.ip}`,
  message: {
    success: false,
    message: 'Too many airtime purchase attempts. Please try again later.',
    code: 'PURCHASE_RATE_LIMIT_EXCEEDED',
    retryAfter: 3600
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('🚫 [AIRTIME_ROUTES] Purchase rate limit exceeded:', {
      userId: req.user?.id,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.status(429).json({
      success: false,
      message: 'Too many airtime purchase attempts. Please try again later.',
      code: 'PURCHASE_RATE_LIMIT_EXCEEDED',
      retryAfter: 3600
    });
  }
});

/**
 * General rate limiting for other airtime operations
 */
const airtimeGeneralRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per 15 minutes
  keyGenerator: (req) => `airtime_general:${req.user.id}:${req.ip}`,
  message: {
    success: false,
    message: 'Too many requests. Please try again later.',
    code: 'GENERAL_RATE_LIMIT_EXCEEDED'
  }
});

// =====================================================
// ROUTE DEFINITIONS
// =====================================================

/**
 * @route   POST /api/v1/airtime/purchase
 * @desc    Purchase MTN airtime via VTpass
 * @access  Private (Authenticated users only)
 * @security JWT, Rate Limiting, Fraud Detection, Transaction Limits
 */
router.post('/purchase',
  airtimePurchaseRateLimit,
  getValidationRules(),
  validateTransactionLimits,
  detectFraud,
  async (req, res) => {
    const startTime = Date.now();
    
    try {
      logger.info('🎯 [AIRTIME_ROUTES] Purchase request received:', {
        userId: req.user.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        amount: req.body.amount,
        phone: req.body.phone?.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
        riskScore: req.riskScore
      });

      await purchaseAirtime(req, res);
      
      const duration = Date.now() - startTime;
      logger.info('✅ [AIRTIME_ROUTES] Purchase request completed:', {
        userId: req.user.id,
        duration: `${duration}ms`,
        status: res.statusCode
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('❌ [AIRTIME_ROUTES] Purchase request failed:', {
        userId: req.user.id,
        duration: `${duration}ms`,
        error: error.message
      });
      
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Purchase request failed',
          code: 'PURCHASE_ERROR'
        });
      }
    }
  }
);

/**
 * @route   GET /api/v1/airtime/history
 * @desc    Get user's airtime purchase history
 * @access  Private
 */
router.get('/history',
  airtimeGeneralRateLimit,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('status')
      .optional()
      .isIn(['pending', 'completed', 'failed', 'cancelled'])
      .withMessage('Invalid status value')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      logger.info('📋 [AIRTIME_ROUTES] History request:', {
        userId: req.user.id,
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status
      });

      await getPurchaseHistory(req, res);
    } catch (error) {
      logger.error('❌ [AIRTIME_ROUTES] History request failed:', {
        userId: req.user.id,
        error: error.message
      });
      
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve purchase history'
      });
    }
  }
);

/**
 * @route   GET /api/v1/airtime/providers
 * @desc    Get available airtime providers and their details
 * @access  Private
 */
router.get('/providers',
  airtimeGeneralRateLimit,
  async (req, res) => {
    try {
      logger.info('📱 [AIRTIME_ROUTES] Providers request:', {
        userId: req.user.id
      });

      const networkPrefixes = vtpassConfig.getNetworkPrefixes();
      const config = vtpassConfig.getRawConfig();

      const providers = [
        {
          id: 'mtn',
          name: 'MTN Nigeria',
          code: 'MTN',
          logo: '/images/providers/mtn-logo.png',
          minAmount: config.minAmount,
          maxAmount: config.maxAmount,
          available: true,
          processingTime: '1-5 minutes',
          description: 'MTN Nigeria airtime top-up via VTpass',
          supportedPrefixes: networkPrefixes.mtn,
          features: [
            'Instant delivery',
            'Real-time confirmation',
            '24/7 availability',
            'Secure transactions'
          ]
        },
        {
          id: 'glo',
          name: 'Globacom (GLO)',
          code: 'GLO',
          logo: '/images/providers/glo-logo.png',
          minAmount: config.minAmount,
          maxAmount: config.maxAmount,
          available: true,
          processingTime: '1-5 minutes',
          description: 'GLO Nigeria airtime top-up via VTpass',
          supportedPrefixes: networkPrefixes.glo,
          features: [
            'Instant delivery',
            'Real-time confirmation',
            '24/7 availability',
            'Secure transactions'
          ]
        },
        {
          id: 'airtel',
          name: 'Airtel Nigeria',
          code: 'AIRTEL',
          logo: '/images/providers/airtel-logo.png',
          minAmount: config.minAmount,
          maxAmount: config.maxAmount,
          available: true,
          processingTime: '1-5 minutes',
          description: 'Airtel Nigeria airtime top-up via VTpass',
          supportedPrefixes: networkPrefixes.airtel,
          features: [
            'Instant delivery',
            'Real-time confirmation',
            '24/7 availability',
            'Secure transactions'
          ]
        },
        {
          id: 'etisalat',
          name: '9mobile (formerly Etisalat)',
          code: '9MOBILE',
          logo: '/images/providers/9mobile-logo.png',
          minAmount: config.minAmount,
          maxAmount: config.maxAmount,
          available: true,
          processingTime: '1-5 minutes',
          description: '9mobile Nigeria airtime top-up via VTpass',
          supportedPrefixes: networkPrefixes.etisalat,
          features: [
            'Instant delivery',
            'Real-time confirmation',
            '24/7 availability',
            'Secure transactions'
          ]
        }
      ];

      res.status(200).json({
        success: true,
        data: {
          providers,
          totalProviders: providers.length,
          lastUpdated: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('❌ [AIRTIME_ROUTES] Providers request failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve providers'
      });
    }
  }
);

/**
 * @route   GET /api/v1/airtime/limits
 * @desc    Get user's transaction limits and restrictions
 * @access  Private
 */
router.get('/limits',
  airtimeGeneralRateLimit,
  async (req, res) => {
    try {
      logger.info('📊 [AIRTIME_ROUTES] Limits request:', {
        userId: req.user.id
      });

      const config = vtpassConfig.getRawConfig();
      const isVerified = req.user.is_email_verified && req.user.is_phone_verified;

      const limits = {
        transaction: {
          min: config.minAmount,
          max: isVerified ? config.maxAmount : config.maxAmount * 0.5, // Reduced for unverified
          currency: 'NGN'
        },
        daily: {
          amount: isVerified ? 1000000 : 500000, // ₦1M for verified, ₦500K for unverified
          transactions: isVerified ? 50 : 25,
          currency: 'NGN'
        },
        hourly: {
          transactions: isVerified ? 20 : 10
        },
        verification: {
          emailVerified: req.user.is_email_verified,
          phoneVerified: req.user.is_phone_verified,
          benefits: isVerified ? [] : [
            'Higher transaction limits',
            'Increased daily limits',
            'Priority customer support',
            'Advanced features access'
          ]
        },
        restrictions: {
          requiresPinForLargeAmounts: true,
          pinThreshold: 10000, // ₦10K
          maintenanceMode: false
        }
      };

      res.status(200).json({
        success: true,
        data: limits
      });
    } catch (error) {
      logger.error('❌ [AIRTIME_ROUTES] Limits request failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve limits'
      });
    }
  }
);

/**
 * @route   GET /api/v1/airtime/validate-phone
 * @desc    Validate phone number and detect network
 * @access  Private
 */
router.get('/validate-phone',
  airtimeGeneralRateLimit,
  [
    query('phone')
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('en-NG')
      .withMessage('Invalid Nigerian phone number format'),
    query('network')
      .optional()
      .isIn(['mtn', 'glo', 'airtel', 'etisalat'])
      .withMessage('Network must be one of: mtn, glo, airtel, etisalat')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { phone, network } = req.query;
      const detection = vtpassConfig.detectNetwork(phone);

      logger.info('📞 [AIRTIME_ROUTES] Phone validation:', {
        userId: req.user.id,
        phone: phone.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
        valid: detection.valid,
        detectedNetwork: detection.network,
        requestedNetwork: network
      });

      // If specific network was requested, validate against it
      if (network && detection.valid && detection.network !== network) {
        return res.status(400).json({
          success: false,
          data: {
            valid: false,
            error: `Phone number belongs to ${detection.networkName}, not ${network.toUpperCase()}`,
            detectedNetwork: detection.network,
            detectedNetworkName: detection.networkName,
            requestedNetwork: network
          }
        });
      }

      res.status(200).json({
        success: true,
        data: {
          valid: detection.valid,
          normalized: detection.normalized,
          international: detection.international,
          network: detection.network,
          networkName: detection.networkName,
          serviceId: detection.serviceId,
          prefix: detection.prefix,
          error: detection.error || null
        }
      });
    } catch (error) {
      logger.error('❌ [AIRTIME_ROUTES] Phone validation failed:', error);
      res.status(500).json({
        success: false,
        message: 'Phone validation failed'
      });
    }
  }
);

/**
 * @route   GET /api/v1/airtime/networks
 * @desc    Get all supported networks and their prefixes
 * @access  Private
 */
router.get('/networks',
  airtimeGeneralRateLimit,
  async (req, res) => {
    try {
      logger.info('🌐 [AIRTIME_ROUTES] Networks request:', {
        userId: req.user.id
      });

      const networkPrefixes = vtpassConfig.getNetworkPrefixes();
      const config = vtpassConfig.getRawConfig();

      const networks = Object.keys(networkPrefixes).map(networkId => ({
        id: networkId,
        name: vtpassConfig.getNetworkName(networkId),
        serviceId: config.services[networkId],
        prefixes: networkPrefixes[networkId],
        minAmount: config.minAmount,
        maxAmount: config.maxAmount,
        available: true
      }));

      res.status(200).json({
        success: true,
        data: {
          networks,
          totalNetworks: networks.length,
          lastUpdated: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('❌ [AIRTIME_ROUTES] Networks request failed:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve networks'
      });
    }
  }
);

// =====================================================
// ERROR HANDLING
// =====================================================

/**
 * Route-specific error handler
 */
router.use((error, req, res, next) => {
  logger.error('❌ [AIRTIME_ROUTES] Unhandled route error:', {
    error: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    userId: req.user?.id,
    ip: req.ip
  });

  if (!res.headersSent) {
    res.status(500).json({
      success: false,
      message: 'An unexpected error occurred',
      code: 'INTERNAL_ERROR'
    });
  }
});

module.exports = router;
