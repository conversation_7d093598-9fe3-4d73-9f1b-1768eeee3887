const express = require('express');
const router = express.Router();

// In production, fetch from DB or cache
const ALL_FLAGS = [
  {
    key: 'new_home_design',
    enabled: true,
    userGroups: ['default', 'beta'],
    platforms: ['android', 'ios'],
    minAppVersion: '1.0.0',
    rolloutPercentage: 100,
  },
  {
    key: 'beta_features',
    enabled: false,
    userGroups: ['beta'],
    platforms: ['android'],
    minAppVersion: '1.0.0',
    rolloutPercentage: 10,
  },
  // ...add more flags as needed
];

// Middleware to authenticate user (replace with your real auth if needed)
const authenticate = (req, res, next) => {
  // Example: get userId from JWT or session
  // req.user = { id: 'user-id-from-token', group: 'default' };
  next();
};

router.get('/feature-flags', authenticate, (req, res) => {
  const { userId, userGroup = 'default', appVersion, platform, deviceId } = req.query;

  // Filter flags for user, group, platform, version, etc.
  const flags = ALL_FLAGS.filter(flag => {
    if (flag.userGroups && !flag.userGroups.includes(userGroup)) return false;
    if (flag.platforms && !flag.platforms.includes(platform)) return false;
    if (flag.minAppVersion && appVersion && appVersion < flag.minAppVersion) return false;
    if (flag.rolloutPercentage !== undefined && flag.rolloutPercentage < 100) {
      // Simple deterministic rollout by user/device
      const hash = Math.abs((userId || deviceId || '').split('').reduce((a, c) => a + c.charCodeAt(0), 0)) % 100;
      if (hash > flag.rolloutPercentage) return false;
    }
    return true;
  });

  res.json({ flags });
});

module.exports = router;
