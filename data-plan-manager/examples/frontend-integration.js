/**
 * Frontend Integration Example
 * 
 * Example code showing how to integrate the data plan manager
 * with frontend applications for real-time updates
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// React Hook Example for Data Plans
const useDataPlans = () => {
  const [plans, setPlans] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  // WebSocket connection for real-time updates
  useEffect(() => {
    let ws = null;

    const connectWebSocket = () => {
      ws = new WebSocket(`${process.env.REACT_APP_WS_URL}/ws/data-plans`);

      ws.onopen = () => {
        console.log('Connected to data plans WebSocket');
        
        // Set client type
        ws.send(JSON.stringify({
          type: 'set_client_type',
          clientType: 'user'
        }));

        // Request current plans
        ws.send(JSON.stringify({
          type: 'request_current_plans'
        }));
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
          case 'current_plans':
            setPlans(data.data);
            setLoading(false);
            setLastUpdate(data.timestamp);
            break;
            
          case 'plans_updated':
            // Refresh plans when updated
            fetchPlans();
            setLastUpdate(data.timestamp);
            break;
            
          case 'connection_established':
            console.log('WebSocket connection established:', data.clientId);
            break;
        }
      };

      ws.onclose = () => {
        console.log('WebSocket connection closed, attempting to reconnect...');
        setTimeout(connectWebSocket, 5000);
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setError('Real-time connection failed');
      };
    };

    connectWebSocket();

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, []);

  // Fetch plans via REST API
  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/data-plans`);
      const result = await response.json();
      
      if (result.success) {
        setPlans(result.data);
        setLastUpdate(result.timestamp);
        setError(null);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to fetch data plans');
    } finally {
      setLoading(false);
    }
  };

  // Get plans by network
  const getPlansByNetwork = async (network) => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/data-plans/network/${network}`);
      const result = await response.json();
      return result.success ? result.data : null;
    } catch (err) {
      console.error('Failed to fetch network plans:', err);
      return null;
    }
  };

  // Get cheapest plan
  const getCheapestPlan = async (network, size) => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/data-plans/cheapest/${network}/${size}`);
      const result = await response.json();
      return result.success ? result.data : null;
    } catch (err) {
      console.error('Failed to fetch cheapest plan:', err);
      return null;
    }
  };

  return {
    plans,
    loading,
    error,
    lastUpdate,
    fetchPlans,
    getPlansByNetwork,
    getCheapestPlan
  };
};

// Admin Hook Example for Plan Management
const useAdminDataPlans = () => {
  const [status, setStatus] = useState(null);
  const [priceChanges, setPriceChanges] = useState([]);
  const [vendors, setVendors] = useState([]);

  // WebSocket connection for admin updates
  useEffect(() => {
    let ws = null;

    const connectWebSocket = () => {
      ws = new WebSocket(`${process.env.REACT_APP_WS_URL}/ws/data-plans`);

      ws.onopen = () => {
        console.log('Connected to admin data plans WebSocket');
        
        // Set client type as admin
        ws.send(JSON.stringify({
          type: 'set_client_type',
          clientType: 'admin'
        }));
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
          case 'price_changes':
            setPriceChanges(prev => [data, ...prev.slice(0, 49)]); // Keep last 50
            // Show notification
            showNotification(`Price changes detected for ${data.vendor}`, 'info');
            break;
            
          case 'vendor_error':
            // Show error notification
            showNotification(`Vendor error: ${data.vendor} - ${data.error}`, 'error');
            break;
            
          case 'plans_updated':
            // Show success notification
            showNotification(`Plans updated for ${data.vendor}`, 'success');
            break;
        }
      };

      ws.onclose = () => {
        console.log('Admin WebSocket connection closed, attempting to reconnect...');
        setTimeout(connectWebSocket, 5000);
      };
    };

    connectWebSocket();

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, []);

  // Force update all vendors
  const forceUpdateAll = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/data-plans/admin/force-update`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      const result = await response.json();
      return result;
    } catch (err) {
      console.error('Failed to force update:', err);
      return { success: false, error: err.message };
    }
  };

  // Force update specific vendor
  const forceUpdateVendor = async (vendorId) => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/data-plans/admin/force-update/${vendorId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      const result = await response.json();
      return result;
    } catch (err) {
      console.error('Failed to force update vendor:', err);
      return { success: false, error: err.message };
    }
  };

  // Clear cache
  const clearCache = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/data-plans/admin/cache`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      
      const result = await response.json();
      return result;
    } catch (err) {
      console.error('Failed to clear cache:', err);
      return { success: false, error: err.message };
    }
  };

  // Get system status
  const getStatus = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/data-plans/status`);
      const result = await response.json();
      
      if (result.success) {
        setStatus(result.data);
      }
      
      return result;
    } catch (err) {
      console.error('Failed to get status:', err);
      return { success: false, error: err.message };
    }
  };

  // Get vendors
  const getVendors = async () => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/data-plans/admin/vendors`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });
      
      const result = await response.json();
      
      if (result.success) {
        setVendors(result.data);
      }
      
      return result;
    } catch (err) {
      console.error('Failed to get vendors:', err);
      return { success: false, error: err.message };
    }
  };

  return {
    status,
    priceChanges,
    vendors,
    forceUpdateAll,
    forceUpdateVendor,
    clearCache,
    getStatus,
    getVendors
  };
};

// Utility functions
const getAuthToken = () => {
  // Implement your auth token retrieval logic
  return localStorage.getItem('authToken');
};

const showNotification = (message, type) => {
  // Implement your notification system
  console.log(`[${type.toUpperCase()}] ${message}`);
};

// Export hooks for use in components
export { useDataPlans, useAdminDataPlans };
