/**
 * Debug Redis Cloud Connection
 * 
 * Simple script to debug Redis Cloud connection issues
 * Tests different connection configurations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

require('dotenv').config({ path: require('path').join(__dirname, '.env') });
const Redis = require('ioredis');

async function debugRedisConnection() {
  console.log('🔍 [REDIS_DEBUG] Starting Redis Cloud connection debug...\n');

  // Show configuration
  console.log('🔧 Configuration:');
  console.log('   REDIS_HOST:', process.env.REDIS_HOST);
  console.log('   REDIS_PORT:', process.env.REDIS_PORT);
  console.log('   REDIS_PASSWORD:', process.env.REDIS_PASSWORD ? '***' + process.env.REDIS_PASSWORD.slice(-3) : 'NOT SET');
  console.log('   REDIS_TLS:', process.env.REDIS_TLS);
  console.log('   REDIS_DB:', process.env.REDIS_DB);
  console.log('');

  // Parse host and port
  let redisHost = process.env.REDIS_HOST || 'localhost';
  let redisPort = parseInt(process.env.REDIS_PORT) || 6379;
  
  if (redisHost.includes(':')) {
    const [host, port] = redisHost.split(':');
    redisHost = host;
    redisPort = parseInt(port) || redisPort;
  }

  console.log('📋 Parsed Configuration:');
  console.log('   Host:', redisHost);
  console.log('   Port:', redisPort);
  console.log('   TLS Required:', process.env.REDIS_TLS === 'true');
  console.log('');

  // Test different connection configurations
  const configs = [
    {
      name: 'Standard TLS Connection',
      config: {
        host: redisHost,
        port: redisPort,
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB) || 0,
        tls: {
          rejectUnauthorized: true,
          servername: redisHost
        },
        connectTimeout: 30000,
        lazyConnect: false
      }
    },
    {
      name: 'Relaxed TLS Connection',
      config: {
        host: redisHost,
        port: redisPort,
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB) || 0,
        tls: {
          rejectUnauthorized: false
        },
        connectTimeout: 30000,
        lazyConnect: false
      }
    },
    {
      name: 'No TLS Connection',
      config: {
        host: redisHost,
        port: redisPort,
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB) || 0,
        connectTimeout: 30000,
        lazyConnect: false
      }
    }
  ];

  for (const { name, config } of configs) {
    console.log(`🧪 Testing: ${name}`);
    
    let redis = null;
    try {
      redis = new Redis(config);
      
      // Set up event listeners for debugging
      redis.on('connect', () => {
        console.log('   ✅ Connected');
      });
      
      redis.on('ready', () => {
        console.log('   ✅ Ready');
      });
      
      redis.on('error', (error) => {
        console.log('   ❌ Error:', error.message);
      });

      // Test connection
      const result = await redis.ping();
      console.log('   🏓 Ping result:', result);
      
      if (result === 'PONG') {
        console.log('   🎉 SUCCESS! This configuration works.\n');
        
        // Test basic operations
        await redis.set('test:key', 'test:value');
        const value = await redis.get('test:key');
        console.log('   📝 SET/GET test:', value === 'test:value' ? 'PASSED' : 'FAILED');
        await redis.del('test:key');
        
        await redis.quit();
        return config;
      }
      
    } catch (error) {
      console.log('   ❌ Failed:', error.message);
      console.log('   📋 Error details:', {
        code: error.code,
        errno: error.errno,
        syscall: error.syscall
      });
    } finally {
      if (redis) {
        try {
          await redis.quit();
        } catch (e) {
          // Ignore cleanup errors
        }
      }
    }
    
    console.log('');
  }

  console.log('❌ All connection attempts failed. Please check:');
  console.log('   1. Redis Cloud instance is running');
  console.log('   2. Credentials are correct');
  console.log('   3. IP address is whitelisted in Redis Cloud');
  console.log('   4. Firewall allows outbound connections on port', redisPort);
  console.log('   5. TLS/SSL settings match Redis Cloud requirements');
}

// Run debug if this file is executed directly
if (require.main === module) {
  debugRedisConnection().catch(console.error);
}

module.exports = debugRedisConnection;
