/**
 * PluginNG Data Plan Configuration
 * 
 * Configuration for PluginNG vendor data plan management
 * Handles authentication, API endpoints, and plan parsing
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const logger = require('../../utils/logger');

class PluginNGConfig {
  constructor() {
    this.vendorId = 'pluginng';
    this.vendorName = 'PluginNG';
    this.baseUrl = process.env.PLUGINNG_BASE_URL || 'https://pluginng.com/api';
    
    // Authentication credentials
    this.credentials = {
      email: process.env.PLUGINNG_EMAIL || '<EMAIL>',
      password: process.env.PLUGINNG_PASSWORD || 'password'
    };
    
    // API endpoints
    this.endpoints = {
      login: '/login',
      getPlans: '/get/plans',
      purchase: '/purchase/data'
    };
    
    // Update configuration
    this.updateConfig = {
      frequency: '0 */6 * * *', // Every 6 hours
      enabled: true,
      retryAttempts: 3,
      retryDelay: 5000
    };
    
    // Pricing configuration
    this.pricingConfig = {
      markupPercentage: 15, // 15% markup
      minimumMarkup: 20, // Minimum ₦20 markup
      roundTo: 10, // Round to nearest ₦10
      maxPriceIncrease: 50 // Max 50% price increase alert
    };
    
    // Network mapping
    this.networkMapping = {
      'MTN SME': { network: 'mtn', type: 'sme' },
      'MTN CG': { network: 'mtn', type: 'cg' },
      'AIRTEL DIRECT': { network: 'airtel', type: 'direct' },
      'GLO DIRECT': { network: 'glo', type: 'direct' },
      '9MOBILE DIRECT': { network: '9mobile', type: 'direct' }
    };
  }

  /**
   * Get authentication headers
   */
  getAuthHeaders(token = null) {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    return headers;
  }

  /**
   * Parse plan name to extract size and validity
   */
  parsePlanName(planName) {
    try {
      // Extract data size (e.g., "1GB", "500MB")
      const sizeMatch = planName.match(/(\d+(?:\.\d+)?)(GB|MB)/i);
      const size = sizeMatch ? `${sizeMatch[1]}${sizeMatch[2].toUpperCase()}` : null;
      
      // Extract validity period (e.g., "30 days", "7 days")
      const validityMatch = planName.match(/(\d+)\s*(day|days)/i);
      const validity = validityMatch ? `${validityMatch[1]} days` : '30 days';
      
      // Extract plan type (CG, SME, etc.)
      const typeMatch = planName.match(/\[(CG|SME|DIRECT)\]/i);
      const type = typeMatch ? typeMatch[1].toLowerCase() : 'direct';
      
      return {
        size,
        validity,
        type,
        originalName: planName
      };
    } catch (error) {
      logger.error('❌ [PLUGINNG_CONFIG] Plan name parsing failed:', {
        planName,
        error: error.message
      });
      
      return {
        size: null,
        validity: '30 days',
        type: 'direct',
        originalName: planName
      };
    }
  }

  /**
   * Calculate our selling price
   */
  calculateSellingPrice(vendorPrice) {
    try {
      const markupAmount = Math.max(
        vendorPrice * (this.pricingConfig.markupPercentage / 100),
        this.pricingConfig.minimumMarkup
      );
      
      const totalPrice = vendorPrice + markupAmount;
      
      // Round to nearest configured amount
      const roundedPrice = Math.ceil(totalPrice / this.pricingConfig.roundTo) * this.pricingConfig.roundTo;
      
      return {
        vendorPrice,
        markupAmount: Math.round(markupAmount),
        sellingPrice: roundedPrice,
        profitMargin: Math.round(((roundedPrice - vendorPrice) / vendorPrice) * 100)
      };
    } catch (error) {
      logger.error('❌ [PLUGINNG_CONFIG] Price calculation failed:', {
        vendorPrice,
        error: error.message
      });
      
      return {
        vendorPrice,
        markupAmount: this.pricingConfig.minimumMarkup,
        sellingPrice: vendorPrice + this.pricingConfig.minimumMarkup,
        profitMargin: 0
      };
    }
  }

  /**
   * Validate plan data
   */
  validatePlanData(planData) {
    const errors = [];
    
    if (!planData.subcategory_id) {
      errors.push('Missing subcategory_id');
    }
    
    if (!planData.title) {
      errors.push('Missing title');
    }
    
    if (!planData.plan || !Array.isArray(planData.plan)) {
      errors.push('Missing or invalid plan array');
    }
    
    if (planData.plan) {
      planData.plan.forEach((plan, index) => {
        if (!plan.plan) {
          errors.push(`Plan ${index}: Missing plan name`);
        }
        if (!plan.amount || isNaN(plan.amount)) {
          errors.push(`Plan ${index}: Missing or invalid amount`);
        }
      });
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get network info from title
   */
  getNetworkInfo(title) {
    const networkInfo = this.networkMapping[title];
    if (networkInfo) {
      return networkInfo;
    }
    
    // Fallback parsing
    const titleUpper = title.toUpperCase();
    if (titleUpper.includes('MTN')) {
      return { network: 'mtn', type: titleUpper.includes('SME') ? 'sme' : 'cg' };
    } else if (titleUpper.includes('AIRTEL')) {
      return { network: 'airtel', type: 'direct' };
    } else if (titleUpper.includes('GLO')) {
      return { network: 'glo', type: 'direct' };
    } else if (titleUpper.includes('9MOBILE')) {
      return { network: '9mobile', type: 'direct' };
    }
    
    return { network: 'unknown', type: 'direct' };
  }
}

// Create and export singleton instance
const pluginNGConfig = new PluginNGConfig();

module.exports = pluginNGConfig;
