/**
 * Data Plan Routes
 * 
 * API routes for data plan management
 * Handles plan retrieval, updates, and admin operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const express = require('express');
const router = express.Router();
const logger = require('../../utils/logger');
const { requireAdmin } = require('../../middleware/auth');
const rateLimitMiddleware = require('../../middleware/rateLimitMiddleware');
const { errorHandlingMiddleware } = require('../middleware/security.middleware');

// Debug: Log when this module is loaded
console.log('📊 [DATA_PLAN_ROUTES] Routes module loaded');

// Import data plan manager
let dataPlanManager;
try {
  dataPlanManager = require('../services/dataPlanManager');
  console.log('✅ [DATA_PLAN_ROUTES] Data plan manager imported successfully');
} catch (error) {
  console.error('❌ [DATA_PLAN_ROUTES] Failed to import data plan manager:', error);
}

console.log('📊 [DATA_PLAN_ROUTES] About to define routes...');

// Simple test route first
router.get('/test', (req, res) => {
  console.log('🧪 [DATA_PLAN_ROUTES] Test route hit!');
  res.json({
    success: true,
    message: 'Data plan routes are working',
    timestamp: new Date().toISOString(),
    route: 'test'
  });
});

/**
 * GET /api/data-plans
 * Get all data plans from all vendors
 */
console.log('📊 [DATA_PLAN_ROUTES] Defining main GET / route...');

router.get('/', async (req, res) => {
  try {
    console.log('🎯 [DATA_PLAN_ROUTES] Main route hit!');
    logger.info('📥 [DATA_PLAN_ROUTES] Get all plans request');

    // Check if data plan manager is available
    if (!dataPlanManager) {
      throw new Error('Data plan manager not available');
    }

    const plans = await dataPlanManager.getAllPlans();

    res.json({
      success: true,
      data: plans,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [DATA_PLAN_ROUTES] Failed to get all plans:', {
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch data plans',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/data-plans/network/:network
 * Get data plans for specific network
 */
router.get('/network/:network', async (req, res) => {
  try {
    const { network } = req.params;
    
    logger.info('📥 [DATA_PLAN_ROUTES] Get plans by network request:', {
      network,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    });

    const plans = await dataPlanManager.getPlansByNetwork(network);
    
    if (!plans || Object.keys(plans).length === 0) {
      return res.status(404).json({
        success: false,
        error: `No plans found for network: ${network}`,
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      data: plans,
      network,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [DATA_PLAN_ROUTES] Failed to get plans by network:', {
      network: req.params.network,
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch network plans',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/data-plans/cheapest/:network/:size
 * Get cheapest plan for specific network and size
 */
router.get('/cheapest/:network/:size', async (req, res) => {
  try {
    const { network, size } = req.params;
    
    logger.info('📥 [DATA_PLAN_ROUTES] Get cheapest plan request:', {
      network,
      size,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    });

    const plan = await dataPlanManager.getCheapestPlan(network, size);
    
    if (!plan) {
      return res.status(404).json({
        success: false,
        error: `No plan found for ${size} on ${network} network`,
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      data: plan,
      network,
      size,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [DATA_PLAN_ROUTES] Failed to get cheapest plan:', {
      network: req.params.network,
      size: req.params.size,
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch cheapest plan',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/data-plans/status
 * Get data plan manager status
 */
router.get('/status', async (req, res) => {
  try {
    logger.info('📥 [DATA_PLAN_ROUTES] Get status request');

    // Check if data plan manager is available
    if (!dataPlanManager) {
      throw new Error('Data plan manager not available');
    }

    const managerStatus = await dataPlanManager.getStatus();

    // Try to get realtime status safely
    let realtimeStatus = { status: 'unknown' };
    try {
      const realTimeUpdateService = require('../services/realTimeUpdateService');
      realtimeStatus = realTimeUpdateService.getStatus();
    } catch (realtimeError) {
      logger.warn('⚠️ [DATA_PLAN_ROUTES] Realtime service not available:', realtimeError.message);
    }

    res.json({
      success: true,
      data: {
        manager: managerStatus,
        realtime: realtimeStatus,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('❌ [DATA_PLAN_ROUTES] Failed to get status:', {
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch status',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Admin routes (require admin authentication)

/**
 * POST /api/data-plans/admin/force-update
 * Force update all vendor plans
 */
router.post('/admin/force-update', async (req, res) => {
  try {
    logger.info('🔄 [DATA_PLAN_ROUTES] Force update request from admin');

    const results = await dataPlanManager.forceUpdateAll();
    
    res.json({
      success: true,
      data: results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [DATA_PLAN_ROUTES] Failed to force update:', {
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Failed to force update plans',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * POST /api/data-plans/admin/force-update/:vendor
 * Force update specific vendor plans
 */
router.post('/admin/force-update/:vendor', requireAdmin, rateLimitMiddleware('authentication'), async (req, res) => {
  try {
    const { vendor } = req.params;
    
    logger.info('🔄 [DATA_PLAN_ROUTES] Force update vendor request from admin:', {
      vendor
    });

    // Get vendor service
    const vendorInfo = dataPlanManager.vendors.get(vendor);
    if (!vendorInfo) {
      return res.status(404).json({
        success: false,
        error: `Vendor not found: ${vendor}`,
        timestamp: new Date().toISOString()
      });
    }

    const result = await vendorInfo.service.forceUpdate();
    
    res.json({
      success: true,
      data: result,
      vendor,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [DATA_PLAN_ROUTES] Failed to force update vendor:', {
      vendor: req.params.vendor,
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Failed to force update vendor plans',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * DELETE /api/data-plans/admin/cache
 * Clear all cache
 */
router.delete('/admin/cache', async (req, res) => {
  try {
    logger.info('🗑️ [DATA_PLAN_ROUTES] Clear cache request from admin');

    dataPlanManager.clearAllCache();
    
    res.json({
      success: true,
      message: 'Cache cleared successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [DATA_PLAN_ROUTES] Failed to clear cache:', {
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Failed to clear cache',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/data-plans/admin/vendors
 * Get all registered vendors
 */
router.get('/admin/vendors', async (req, res) => {
  try {
    logger.info('📥 [DATA_PLAN_ROUTES] Get vendors request from admin');

    const vendors = Array.from(dataPlanManager.vendors.entries()).map(([id, info]) => ({
      id,
      name: info.name,
      status: info.status,
      priority: info.priority,
      capabilities: info.capabilities,
      networks: info.networks
    }));
    
    res.json({
      success: true,
      data: vendors,
      totalVendors: vendors.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [DATA_PLAN_ROUTES] Failed to get vendors:', {
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch vendors',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/data-plans/admin/realtime-clients
 * Get real-time connected clients info
 */
router.get('/admin/realtime-clients', async (req, res) => {
  try {
    logger.info('📥 [DATA_PLAN_ROUTES] Get realtime clients request from admin');

    const status = realTimeUpdateService.getStatus();
    
    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('❌ [DATA_PLAN_ROUTES] Failed to get realtime clients:', {
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: 'Failed to fetch realtime clients info',
      timestamp: new Date().toISOString()
    });
  }
});

// Apply error handling middleware
router.use(errorHandlingMiddleware);

// Debug: Check router before export
console.log('📊 [DATA_PLAN_ROUTES] Router stack length:', router.stack ? router.stack.length : 'undefined');
console.log('📊 [DATA_PLAN_ROUTES] Router methods:', Object.getOwnPropertyNames(router));

module.exports = router;
