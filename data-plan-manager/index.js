/**
 * Data Plan Manager - Main Entry Point
 * 
 * Initializes and exports all data plan management services
 * Integrates with the main backend server
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const logger = require('../utils/logger');

// Import services
const dataPlanManager = require('./services/dataPlanManager');
const realTimeUpdateService = require('./services/realTimeUpdateService');
const pluginNGDataPlanService = require('./services/pluginngDataPlanService');

// Import routes
const dataPlanRoutes = require('./routes/dataPlanRoutes');

class DataPlanManagerModule {
  constructor() {
    this.isInitialized = false;
    this.services = {
      dataPlanManager,
      realTimeUpdateService,
      pluginNGDataPlanService
    };
  }

  /**
   * Initialize the data plan manager module
   */
  async initialize(app, server) {
    try {
      logger.info('🚀 [DATA_PLAN_MANAGER_MODULE] Initializing data plan manager...');

      // Initialize data plan manager first
      logger.info('🚀 [DATA_PLAN_MANAGER_MODULE] Initializing core data plan manager...');
      await dataPlanManager.initializeManager();
      logger.info('✅ [DATA_PLAN_MANAGER_MODULE] Core data plan manager initialized');

      // Initialize real-time update service with WebSocket (skip if ws not available)
      if (server) {
        try {
          logger.info('🚀 [DATA_PLAN_MANAGER_MODULE] Initializing WebSocket service...');
          realTimeUpdateService.initialize(server);
          logger.info('✅ [DATA_PLAN_MANAGER_MODULE] WebSocket service initialized');
        } catch (wsError) {
          logger.warn('⚠️ [DATA_PLAN_MANAGER_MODULE] WebSocket initialization failed, continuing without it:', wsError.message);
        }
      }

      // Register API routes
      logger.info('🚀 [DATA_PLAN_MANAGER_MODULE] Registering API routes...');

      // Check if routes already exist
      const existingRoutes = app._router.stack.filter(layer =>
        layer.regexp && layer.regexp.source.includes('data-plans')
      );
      logger.info('📊 [DATA_PLAN_MANAGER_MODULE] Existing data-plans routes:', existingRoutes.length);

      // Register the routes
      app.use('/api/data-plans', dataPlanRoutes);

      // Verify registration
      const newRoutes = app._router.stack.filter(layer =>
        layer.regexp && layer.regexp.source.includes('data-plans')
      );
      logger.info('📊 [DATA_PLAN_MANAGER_MODULE] Total data-plans routes after registration:', newRoutes.length);
      logger.info('✅ [DATA_PLAN_MANAGER_MODULE] API routes registered successfully');

      // Trigger initial data fetch for PluginNG
      setTimeout(async () => {
        try {
          logger.info('🔄 [DATA_PLAN_MANAGER_MODULE] Triggering initial data fetch...');
          await pluginNGDataPlanService.forceUpdate();
          logger.info('✅ [DATA_PLAN_MANAGER_MODULE] Initial data fetch completed');
        } catch (error) {
          logger.error('❌ [DATA_PLAN_MANAGER_MODULE] Initial data fetch failed:', {
            error: error.message
          });
        }
      }, 5000); // Wait 5 seconds after server start

      this.isInitialized = true;
      
      logger.info('✅ [DATA_PLAN_MANAGER_MODULE] Data plan manager initialized successfully');

      return {
        success: true,
        services: this.services,
        routes: '/api/data-plans',
        websocket: '/ws/data-plans'
      };

    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER_MODULE] Failed to initialize data plan manager:', {
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Get module status
   */
  async getStatus() {
    if (!this.isInitialized) {
      return {
        initialized: false,
        error: 'Module not initialized'
      };
    }

    try {
      const managerStatus = await dataPlanManager.getStatus();
      const realtimeStatus = realTimeUpdateService.getStatus();

      return {
        initialized: true,
        manager: managerStatus,
        realtime: realtimeStatus,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        initialized: true,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Shutdown the module gracefully
   */
  async shutdown() {
    try {
      logger.info('🛑 [DATA_PLAN_MANAGER_MODULE] Shutting down data plan manager...');

      // Stop scheduled updates
      pluginNGDataPlanService.stopScheduledUpdates();

      // Close WebSocket connections
      if (realTimeUpdateService.wss) {
        realTimeUpdateService.wss.close();
      }

      this.isInitialized = false;
      
      logger.info('✅ [DATA_PLAN_MANAGER_MODULE] Data plan manager shutdown completed');
    } catch (error) {
      logger.error('❌ [DATA_PLAN_MANAGER_MODULE] Error during shutdown:', {
        error: error.message
      });
    }
  }
}

// Create and export singleton instance
const dataPlanManagerModule = new DataPlanManagerModule();

module.exports = dataPlanManagerModule;
