/**
 * Security Test Script
 * 
 * Test script to verify all security measures are working correctly
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const securityConfig = require('./config/security.config');

class SecurityTester {
  constructor() {
    this.testResults = [];
  }

  /**
   * Run all security tests
   */
  async runAllTests() {
    console.log('🔒 [SECURITY_TEST] Starting comprehensive security tests...\n');

    try {
      this.testDataMasking();
      this.testRateLimiting();
      this.testInputSanitization();
      this.testEncryption();
      this.testSecurityHeaders();
      this.testOriginValidation();

      this.printTestResults();
    } catch (error) {
      console.error('❌ [SECURITY_TEST] Test suite failed:', error.message);
    }
  }

  /**
   * Test data masking functionality
   */
  testDataMasking() {
    console.log('🎭 Testing Data Masking...');
    
    try {
      // Test email masking
      const email = '<EMAIL>';
      const maskedEmail = securityConfig.maskSensitiveData(email, 'email');
      const expectedEmail = 'lex***@gmail.com';
      
      if (maskedEmail === expectedEmail) {
        this.addTestResult('Email Masking', true, `${email} → ${maskedEmail}`);
        console.log('✅ Email masking test passed');
      } else {
        this.addTestResult('Email Masking', false, `Expected: ${expectedEmail}, Got: ${maskedEmail}`);
        console.log('❌ Email masking test failed');
      }

      // Test token masking
      const token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJhdWQiOiI5NjllN2M2Yy00YzM5LTQ5YTItYTFjZS02OTYyOWIzNWEwOTYi';
      const maskedToken = securityConfig.maskSensitiveData(token, 'token');
      
      if (maskedToken.includes('...') && maskedToken.length < token.length) {
        this.addTestResult('Token Masking', true, `Token masked successfully`);
        console.log('✅ Token masking test passed');
      } else {
        this.addTestResult('Token Masking', false, 'Token not properly masked');
        console.log('❌ Token masking test failed');
      }

      // Test phone masking
      const phone = '09060734570';
      const maskedPhone = securityConfig.maskSensitiveData(phone, 'phone');
      const expectedPhone = '090***4570';
      
      if (maskedPhone === expectedPhone) {
        this.addTestResult('Phone Masking', true, `${phone} → ${maskedPhone}`);
        console.log('✅ Phone masking test passed');
      } else {
        this.addTestResult('Phone Masking', false, `Expected: ${expectedPhone}, Got: ${maskedPhone}`);
        console.log('❌ Phone masking test failed');
      }

      console.log('');
    } catch (error) {
      this.addTestResult('Data Masking', false, error.message);
      console.log('❌ Data masking test failed:', error.message, '\n');
    }
  }

  /**
   * Test rate limiting functionality
   */
  testRateLimiting() {
    console.log('⏱️ Testing Rate Limiting...');
    
    try {
      const testIP = '*************';
      
      // Test normal requests
      let result = securityConfig.checkRateLimit(testIP, 'apiCalls');
      if (result.allowed) {
        this.addTestResult('Rate Limiting - Normal Request', true, `Allowed with ${result.remaining} remaining`);
        console.log('✅ Normal request test passed');
      } else {
        this.addTestResult('Rate Limiting - Normal Request', false, 'Normal request blocked');
        console.log('❌ Normal request test failed');
      }

      // Test rate limit enforcement (simulate multiple requests)
      let blockedRequest = null;
      for (let i = 0; i < 65; i++) { // Exceed the limit
        blockedRequest = securityConfig.checkRateLimit(`test_${testIP}`, 'apiCalls');
      }
      
      if (!blockedRequest.allowed) {
        this.addTestResult('Rate Limiting - Enforcement', true, `Request blocked: ${blockedRequest.reason}`);
        console.log('✅ Rate limit enforcement test passed');
      } else {
        this.addTestResult('Rate Limiting - Enforcement', false, 'Rate limit not enforced');
        console.log('❌ Rate limit enforcement test failed');
      }

      console.log('');
    } catch (error) {
      this.addTestResult('Rate Limiting', false, error.message);
      console.log('❌ Rate limiting test failed:', error.message, '\n');
    }
  }

  /**
   * Test input sanitization
   */
  testInputSanitization() {
    console.log('🧹 Testing Input Sanitization...');
    
    try {
      // Test script tag removal
      const maliciousInput = '<script>alert("XSS")</script>Hello World';
      const sanitized = securityConfig.sanitizeInput(maliciousInput);
      
      if (!sanitized.includes('<script>') && sanitized.includes('Hello World')) {
        this.addTestResult('Input Sanitization - Script Removal', true, 'Script tags removed successfully');
        console.log('✅ Script tag removal test passed');
      } else {
        this.addTestResult('Input Sanitization - Script Removal', false, 'Script tags not removed');
        console.log('❌ Script tag removal test failed');
      }

      // Test javascript: protocol removal
      const jsInput = 'javascript:alert("XSS")';
      const sanitizedJs = securityConfig.sanitizeInput(jsInput);
      
      if (!sanitizedJs.includes('javascript:')) {
        this.addTestResult('Input Sanitization - JS Protocol', true, 'JavaScript protocol removed');
        console.log('✅ JavaScript protocol removal test passed');
      } else {
        this.addTestResult('Input Sanitization - JS Protocol', false, 'JavaScript protocol not removed');
        console.log('❌ JavaScript protocol removal test failed');
      }

      console.log('');
    } catch (error) {
      this.addTestResult('Input Sanitization', false, error.message);
      console.log('❌ Input sanitization test failed:', error.message, '\n');
    }
  }

  /**
   * Test encryption functionality
   */
  testEncryption() {
    console.log('🔐 Testing Encryption...');
    
    try {
      const testData = 'sensitive-data-12345';
      
      // Test encryption
      const encrypted = securityConfig.encrypt(testData);
      if (encrypted.encrypted && encrypted.iv && encrypted.authTag) {
        this.addTestResult('Encryption', true, 'Data encrypted successfully');
        console.log('✅ Encryption test passed');
        
        // Test decryption
        const decrypted = securityConfig.decrypt(encrypted);
        if (decrypted === testData) {
          this.addTestResult('Decryption', true, 'Data decrypted successfully');
          console.log('✅ Decryption test passed');
        } else {
          this.addTestResult('Decryption', false, 'Decrypted data does not match original');
          console.log('❌ Decryption test failed');
        }
      } else {
        this.addTestResult('Encryption', false, 'Encryption failed');
        console.log('❌ Encryption test failed');
      }

      console.log('');
    } catch (error) {
      this.addTestResult('Encryption', false, error.message);
      console.log('❌ Encryption test failed:', error.message, '\n');
    }
  }

  /**
   * Test security headers
   */
  testSecurityHeaders() {
    console.log('🛡️ Testing Security Headers...');
    
    try {
      const headers = securityConfig.getSecurityHeaders();
      
      const requiredHeaders = [
        'X-Content-Type-Options',
        'X-Frame-Options',
        'X-XSS-Protection',
        'Strict-Transport-Security',
        'Referrer-Policy'
      ];
      
      let allHeadersPresent = true;
      const missingHeaders = [];
      
      requiredHeaders.forEach(header => {
        if (!headers[header]) {
          allHeadersPresent = false;
          missingHeaders.push(header);
        }
      });
      
      if (allHeadersPresent) {
        this.addTestResult('Security Headers', true, `All ${requiredHeaders.length} security headers present`);
        console.log('✅ Security headers test passed');
      } else {
        this.addTestResult('Security Headers', false, `Missing headers: ${missingHeaders.join(', ')}`);
        console.log('❌ Security headers test failed');
      }

      console.log('');
    } catch (error) {
      this.addTestResult('Security Headers', false, error.message);
      console.log('❌ Security headers test failed:', error.message, '\n');
    }
  }

  /**
   * Test origin validation
   */
  testOriginValidation() {
    console.log('🌐 Testing Origin Validation...');
    
    try {
      // Test valid origin
      const validReq = {
        headers: {
          origin: 'http://localhost:3000'
        }
      };
      
      const validResult = securityConfig.validateRequestOrigin(validReq);
      if (validResult) {
        this.addTestResult('Origin Validation - Valid', true, 'Valid origin accepted');
        console.log('✅ Valid origin test passed');
      } else {
        this.addTestResult('Origin Validation - Valid', false, 'Valid origin rejected');
        console.log('❌ Valid origin test failed');
      }

      // Test invalid origin
      const invalidReq = {
        headers: {
          origin: 'https://malicious-site.com'
        }
      };
      
      const invalidResult = securityConfig.validateRequestOrigin(invalidReq);
      if (!invalidResult) {
        this.addTestResult('Origin Validation - Invalid', true, 'Invalid origin rejected');
        console.log('✅ Invalid origin test passed');
      } else {
        this.addTestResult('Origin Validation - Invalid', false, 'Invalid origin accepted');
        console.log('❌ Invalid origin test failed');
      }

      console.log('');
    } catch (error) {
      this.addTestResult('Origin Validation', false, error.message);
      console.log('❌ Origin validation test failed:', error.message, '\n');
    }
  }

  /**
   * Add test result
   */
  addTestResult(testName, passed, details) {
    this.testResults.push({
      testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Print test results summary
   */
  printTestResults() {
    console.log('📊 SECURITY TEST RESULTS SUMMARY');
    console.log('=================================\n');
    
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.testName}`);
      console.log(`   Details: ${result.details}\n`);
    });
    
    console.log(`Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All security tests passed! The system is properly secured.');
    } else {
      console.log('⚠️ Some security tests failed. Please review and fix the issues.');
    }

    // Security status
    console.log('\n🔒 Security Status:');
    const status = securityConfig.getSecurityStatus();
    console.log(`   Blocked IPs: ${status.blockedIPs}`);
    console.log(`   Active Rate Limits: ${status.activeRateLimits}`);
    console.log(`   Encryption Enabled: ${status.encryptionEnabled}`);
    console.log(`   Security Headers Enabled: ${status.securityHeadersEnabled}`);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new SecurityTester();
  tester.runAllTests().catch(console.error);
}

module.exports = SecurityTester;
