# Data Plan Manager Environment Variables
# Copy this file to .env and update with your actual values

# PluginNG Configuration
PLUGINNG_BASE_URL=https://pluginng.com/api
PLUGINNG_EMAIL=<EMAIL>
PLUGINNG_PASSWORD=your-pluginng-password

# Data Plan Manager Configuration
DATA_PLAN_UPDATE_FREQUENCY=0 */6 * * *
DATA_PLAN_CACHE_TTL=300000
DATA_PLAN_MAX_CACHE_SIZE=100

# Real-time Updates Configuration
WEBSOCKET_HEARTBEAT_INTERVAL=30000
WEBSOCKET_MAX_CLIENTS=1000

# Pricing Configuration
DEFAULT_MARKUP_PERCENTAGE=15
MINIMUM_MARKUP_AMOUNT=20
PRICE_ROUND_TO=10
MAX_PRICE_INCREASE_ALERT=50

# Security Configuration
DATA_PLAN_ENCRYPTION_KEY=your-32-character-encryption-key-here
FRONTEND_URL=http://localhost:3000
ADMIN_JWT_SECRET=your-admin-jwt-secret-here

# Rate Limiting
API_RATE_LIMIT_WINDOW=60000
API_RATE_LIMIT_MAX_REQUESTS=60
AUTH_RATE_LIMIT_WINDOW=900000
AUTH_RATE_LIMIT_MAX_ATTEMPTS=5
