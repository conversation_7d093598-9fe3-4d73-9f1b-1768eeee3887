/**
 * Test Authentication Fix
 * 
 * Quick test to verify the authentication fix works
 */

require('dotenv').config();
const pluginNGDataPlanService = require('./services/pluginngDataPlanService');

async function testAuthFix() {
  console.log('🧪 Testing PluginNG Authentication Fix...\n');
  
  try {
    // Test authentication
    console.log('🔐 Testing authentication...');
    await pluginNGDataPlanService.authenticate();
    console.log('✅ Authentication successful!\n');
    
    // Test data fetching
    console.log('📥 Testing data plan fetching...');
    const plans = await pluginNGDataPlanService.fetchDataPlans();
    
    if (plans && Array.isArray(plans)) {
      console.log('✅ Data plans fetched successfully!');
      console.log(`📊 Total categories: ${plans.length}`);
      
      if (plans.length > 0) {
        console.log('📋 Sample category:', {
          title: plans[0].title,
          category: plans[0].category,
          plans_count: plans[0].plan?.length || 0,
          status: plans[0].status
        });
        
        if (plans[0].plan && plans[0].plan.length > 0) {
          console.log('💰 Sample plan:', {
            plan: plans[0].plan[0].plan,
            amount: plans[0].plan[0].amount
          });
        }
      }
      
      console.log('\n🎉 All tests passed! The fix is working correctly.');
    } else {
      console.log('❌ Invalid data format received');
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    console.log('Error details:', error);
  }
}

testAuthFix();
