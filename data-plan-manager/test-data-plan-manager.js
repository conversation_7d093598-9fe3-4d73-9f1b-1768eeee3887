/**
 * Data Plan Manager Test Script
 *
 * Test script to verify the data plan manager functionality
 * Run this script to test PluginNG integration and data fetching
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Load environment variables from the data-plan-manager .env file
require('dotenv').config({ path: require('path').join(__dirname, '.env') });

const logger = require('../utils/logger');

// Import services
const pluginNGDataPlanService = require('./services/pluginngDataPlanService');
const dataPlanManager = require('./services/dataPlanManager');

class DataPlanManagerTester {
  constructor() {
    this.testResults = [];
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 [DATA_PLAN_MANAGER_TEST] Starting comprehensive tests...\n');

    // Debug environment variables
    console.log('🔧 [DEBUG] Environment check:');
    console.log('   PLUGINNG_EMAIL:', process.env.PLUGINNG_EMAIL || 'NOT SET');
    console.log('   PLUGINNG_PASSWORD:', process.env.PLUGINNG_PASSWORD ? '***' + process.env.PLUGINNG_PASSWORD.slice(-3) : 'NOT SET');
    console.log('   PLUGINNG_BASE_URL:', process.env.PLUGINNG_BASE_URL || 'NOT SET');
    console.log('');

    try {
      await this.testPluginNGAuthentication();
      await this.testPluginNGDataFetch();
      await this.testDataPlanParsing();
      await this.testDataPlanManager();
      await this.testCacheSystem();
      await this.testPriceChangeDetection();

      this.printTestResults();
    } catch (error) {
      console.error('❌ [DATA_PLAN_MANAGER_TEST] Test suite failed:', error.message);
    }
  }

  /**
   * Test PluginNG authentication
   */
  async testPluginNGAuthentication() {
    console.log('🔐 Testing PluginNG Authentication...');
    
    try {
      await pluginNGDataPlanService.authenticate();
      this.addTestResult('PluginNG Authentication', true, 'Authentication successful');
      console.log('✅ Authentication test passed\n');
    } catch (error) {
      this.addTestResult('PluginNG Authentication', false, error.message);
      console.log('❌ Authentication test failed:', error.message, '\n');
    }
  }

  /**
   * Test PluginNG data fetching
   */
  async testPluginNGDataFetch() {
    console.log('📥 Testing PluginNG Data Fetching...');
    
    try {
      const rawPlans = await pluginNGDataPlanService.fetchDataPlans();
      
      if (rawPlans && Array.isArray(rawPlans) && rawPlans.length > 0) {
        this.addTestResult('PluginNG Data Fetch', true, `Fetched ${rawPlans.length} plan categories`);
        console.log(`✅ Data fetch test passed - ${rawPlans.length} categories fetched`);
        
        // Log sample data
        console.log('📊 Sample category:', {
          title: rawPlans[0].title,
          category: rawPlans[0].category,
          plansCount: rawPlans[0].plan?.length || 0,
          status: rawPlans[0].status
        });
      } else {
        this.addTestResult('PluginNG Data Fetch', false, 'No data returned or invalid format');
        console.log('❌ Data fetch test failed - No valid data returned');
      }
      console.log('');
    } catch (error) {
      this.addTestResult('PluginNG Data Fetch', false, error.message);
      console.log('❌ Data fetch test failed:', error.message, '\n');
    }
  }

  /**
   * Test data plan parsing
   */
  async testDataPlanParsing() {
    console.log('🔄 Testing Data Plan Parsing...');
    
    try {
      const rawPlans = await pluginNGDataPlanService.fetchDataPlans();
      const parsedPlans = pluginNGDataPlanService.parseDataPlans(rawPlans);
      
      if (parsedPlans && parsedPlans.vendor && parsedPlans.networks) {
        const networkCount = Object.keys(parsedPlans.networks).length;
        const totalPlans = parsedPlans.vendor.totalPlans;
        
        this.addTestResult('Data Plan Parsing', true, `Parsed ${networkCount} networks with ${totalPlans} total plans`);
        console.log(`✅ Parsing test passed - ${networkCount} networks, ${totalPlans} plans`);
        
        // Log sample parsed plan
        const firstNetwork = Object.keys(parsedPlans.networks)[0];
        const firstPlan = parsedPlans.networks[firstNetwork].plans[0];
        console.log('📊 Sample parsed plan:', {
          planId: firstPlan.planId,
          name: firstPlan.name,
          size: firstPlan.size,
          vendorPrice: firstPlan.vendorPrice,
          sellingPrice: firstPlan.sellingPrice,
          profitMargin: firstPlan.profitMargin
        });
      } else {
        this.addTestResult('Data Plan Parsing', false, 'Invalid parsed data structure');
        console.log('❌ Parsing test failed - Invalid data structure');
      }
      console.log('');
    } catch (error) {
      this.addTestResult('Data Plan Parsing', false, error.message);
      console.log('❌ Parsing test failed:', error.message, '\n');
    }
  }

  /**
   * Test data plan manager
   */
  async testDataPlanManager() {
    console.log('🎯 Testing Data Plan Manager...');
    
    try {
      // Force update to ensure fresh data
      await pluginNGDataPlanService.forceUpdate();
      
      // Test getting all plans
      const allPlans = await dataPlanManager.getAllPlans();
      if (allPlans && allPlans.vendors) {
        const vendorCount = Object.keys(allPlans.vendors).length;
        this.addTestResult('Data Plan Manager - Get All Plans', true, `Retrieved plans from ${vendorCount} vendors`);
        console.log(`✅ Get all plans test passed - ${vendorCount} vendors`);
      } else {
        this.addTestResult('Data Plan Manager - Get All Plans', false, 'No plans retrieved');
        console.log('❌ Get all plans test failed');
      }
      
      // Test getting plans by network
      const mtnPlans = await dataPlanManager.getPlansByNetwork('mtn');
      if (mtnPlans && Object.keys(mtnPlans).length > 0) {
        this.addTestResult('Data Plan Manager - Get MTN Plans', true, 'MTN plans retrieved successfully');
        console.log('✅ Get MTN plans test passed');
      } else {
        this.addTestResult('Data Plan Manager - Get MTN Plans', false, 'No MTN plans found');
        console.log('❌ Get MTN plans test failed');
      }
      
      // Test getting cheapest plan
      const cheapestPlan = await dataPlanManager.getCheapestPlan('mtn', '1GB');
      if (cheapestPlan) {
        this.addTestResult('Data Plan Manager - Get Cheapest Plan', true, `Found cheapest 1GB MTN plan: ₦${cheapestPlan.sellingPrice}`);
        console.log(`✅ Get cheapest plan test passed - ₦${cheapestPlan.sellingPrice}`);
      } else {
        this.addTestResult('Data Plan Manager - Get Cheapest Plan', false, 'No cheapest plan found');
        console.log('❌ Get cheapest plan test failed');
      }
      
      console.log('');
    } catch (error) {
      this.addTestResult('Data Plan Manager', false, error.message);
      console.log('❌ Data Plan Manager test failed:', error.message, '\n');
    }
  }

  /**
   * Test cache system
   */
  async testCacheSystem() {
    console.log('💾 Testing Cache System...');
    
    try {
      // Clear cache first
      dataPlanManager.clearAllCache();
      
      // First call (should hit API)
      const start1 = Date.now();
      await dataPlanManager.getAllPlans();
      const time1 = Date.now() - start1;
      
      // Second call (should hit cache)
      const start2 = Date.now();
      await dataPlanManager.getAllPlans();
      const time2 = Date.now() - start2;
      
      if (time2 < time1) {
        this.addTestResult('Cache System', true, `Cache working - First call: ${time1}ms, Second call: ${time2}ms`);
        console.log(`✅ Cache test passed - First: ${time1}ms, Second: ${time2}ms`);
      } else {
        this.addTestResult('Cache System', false, 'Cache not working effectively');
        console.log('⚠️ Cache test inconclusive - times similar');
      }
      
      console.log('');
    } catch (error) {
      this.addTestResult('Cache System', false, error.message);
      console.log('❌ Cache test failed:', error.message, '\n');
    }
  }

  /**
   * Test price change detection
   */
  async testPriceChangeDetection() {
    console.log('💰 Testing Price Change Detection...');
    
    try {
      // Get current plans
      const currentPlans = await pluginNGDataPlanService.getCurrentPlans();
      
      if (currentPlans) {
        // Simulate price change detection
        const changes = pluginNGDataPlanService.detectPriceChanges(null, currentPlans);
        
        if (changes.type === 'initial_load') {
          this.addTestResult('Price Change Detection', true, 'Initial load detected correctly');
          console.log('✅ Price change detection test passed - Initial load');
        } else {
          this.addTestResult('Price Change Detection', true, `Detected ${changes.changes.length} changes`);
          console.log(`✅ Price change detection test passed - ${changes.changes.length} changes`);
        }
      } else {
        this.addTestResult('Price Change Detection', false, 'No current plans to test with');
        console.log('❌ Price change detection test failed - No plans');
      }
      
      console.log('');
    } catch (error) {
      this.addTestResult('Price Change Detection', false, error.message);
      console.log('❌ Price change detection test failed:', error.message, '\n');
    }
  }

  /**
   * Add test result
   */
  addTestResult(testName, passed, details) {
    this.testResults.push({
      testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Print test results summary
   */
  printTestResults() {
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('========================\n');
    
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.testName}`);
      console.log(`   Details: ${result.details}\n`);
    });
    
    console.log(`Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! Data Plan Manager is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please check the configuration and try again.');
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new DataPlanManagerTester();
  tester.runAllTests().catch(console.error);
}

module.exports = DataPlanManagerTester;
