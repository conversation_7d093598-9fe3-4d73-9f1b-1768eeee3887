/**
 * PluginNG Debug Script
 * 
 * Debug script to test PluginNG API directly and identify issues
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const axios = require('axios');
require('dotenv').config();

class PluginNGDebugger {
  constructor() {
    this.baseUrl = process.env.PLUGINNG_BASE_URL || 'https://pluginng.com/api';
    this.credentials = {
      email: process.env.PLUGINNG_EMAIL,
      password: process.env.PLUGINNG_PASSWORD
    };
    
    console.log('🔧 [PLUGINNG_DEBUG] Configuration:');
    console.log('   Base URL:', this.baseUrl);
    console.log('   Email:', this.credentials.email);
    console.log('   Password:', this.credentials.password ? '***' + this.credentials.password.slice(-3) : 'NOT SET');
    console.log('');
  }

  /**
   * Test basic connectivity
   */
  async testConnectivity() {
    console.log('🌐 [PLUGINNG_DEBUG] Testing basic connectivity...');
    
    try {
      const response = await axios.get(this.baseUrl, {
        timeout: 10000,
        validateStatus: () => true // Accept any status code
      });
      
      console.log('✅ Connection successful');
      console.log('   Status:', response.status);
      console.log('   Headers:', JSON.stringify(response.headers, null, 2));
      console.log('   Data preview:', JSON.stringify(response.data, null, 2).substring(0, 500));
      console.log('');
      
      return true;
    } catch (error) {
      console.log('❌ Connection failed');
      console.log('   Error:', error.message);
      console.log('   Code:', error.code);
      if (error.response) {
        console.log('   Status:', error.response.status);
        console.log('   Data:', error.response.data);
      }
      console.log('');
      
      return false;
    }
  }

  /**
   * Test authentication endpoint
   */
  async testAuthentication() {
    console.log('🔐 [PLUGINNG_DEBUG] Testing authentication...');
    
    if (!this.credentials.email || !this.credentials.password) {
      console.log('❌ Missing credentials in environment variables');
      console.log('   Please check PLUGINNG_EMAIL and PLUGINNG_PASSWORD in .env file');
      console.log('');
      return false;
    }
    
    try {
      const loginUrl = `${this.baseUrl}/login`;
      console.log('   Login URL:', loginUrl);
      console.log('   Payload:', { email: this.credentials.email, password: '***' });
      
      const response = await axios.post(loginUrl, this.credentials, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'Vendy-DataPlan-Manager/1.0'
        },
        timeout: 30000,
        validateStatus: () => true // Accept any status code
      });
      
      console.log('📤 Request sent successfully');
      console.log('   Status:', response.status);
      console.log('   Status Text:', response.statusText);
      console.log('   Headers:', JSON.stringify(response.headers, null, 2));
      
      if (response.data) {
        console.log('   Response Data:', JSON.stringify(response.data, null, 2));
      }
      
      if (response.status === 200) {
        if (response.headers['authorization']) {
          const token = response.headers['authorization'].replace('Bearer ', '');
          console.log('✅ Authentication successful');
          console.log('   Token received:', token.substring(0, 20) + '...');
          console.log('');
          return token;
        } else {
          console.log('⚠️ Status 200 but no authorization header found');
          console.log('   Available headers:', Object.keys(response.headers));
          console.log('');
          return false;
        }
      } else {
        console.log('❌ Authentication failed');
        console.log('   This might indicate:');
        console.log('   - Invalid credentials');
        console.log('   - API endpoint changed');
        console.log('   - Server-side issues');
        console.log('   - Rate limiting');
        console.log('');
        return false;
      }
      
    } catch (error) {
      console.log('❌ Authentication request failed');
      console.log('   Error:', error.message);
      console.log('   Code:', error.code);
      
      if (error.response) {
        console.log('   Response Status:', error.response.status);
        console.log('   Response Headers:', JSON.stringify(error.response.headers, null, 2));
        console.log('   Response Data:', JSON.stringify(error.response.data, null, 2));
      }
      
      if (error.request) {
        console.log('   Request was made but no response received');
        console.log('   This might indicate network issues or server downtime');
      }
      
      console.log('');
      return false;
    }
  }

  /**
   * Test data plans endpoint
   */
  async testDataPlansEndpoint(token) {
    console.log('📊 [PLUGINNG_DEBUG] Testing data plans endpoint...');
    
    if (!token) {
      console.log('❌ No token available for testing');
      console.log('');
      return false;
    }
    
    try {
      const plansUrl = `${this.baseUrl}/get/plans`;
      console.log('   Plans URL:', plansUrl);
      console.log('   Token:', token.substring(0, 20) + '...');
      
      const response = await axios.get(plansUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'Vendy-DataPlan-Manager/1.0'
        },
        timeout: 30000,
        validateStatus: () => true
      });
      
      console.log('📤 Request sent successfully');
      console.log('   Status:', response.status);
      console.log('   Status Text:', response.statusText);
      
      if (response.status === 200 && response.data) {
        console.log('✅ Data plans fetched successfully');
        
        if (response.data.data && Array.isArray(response.data.data)) {
          console.log('   Total categories:', response.data.data.length);
          
          // Show sample data
          if (response.data.data.length > 0) {
            const sample = response.data.data[0];
            console.log('   Sample category:', {
              subcategory_id: sample.subcategory_id,
              title: sample.title,
              category: sample.category,
              plans_count: sample.plan?.length || 0,
              status: sample.status
            });
            
            if (sample.plan && sample.plan.length > 0) {
              console.log('   Sample plan:', {
                plan: sample.plan[0].plan,
                amount: sample.plan[0].amount
              });
            }
          }
        } else {
          console.log('⚠️ Unexpected data format');
          console.log('   Data:', JSON.stringify(response.data, null, 2).substring(0, 500));
        }
        
        console.log('');
        return true;
      } else {
        console.log('❌ Failed to fetch data plans');
        console.log('   Status:', response.status);
        console.log('   Data:', JSON.stringify(response.data, null, 2));
        console.log('');
        return false;
      }
      
    } catch (error) {
      console.log('❌ Data plans request failed');
      console.log('   Error:', error.message);
      
      if (error.response) {
        console.log('   Response Status:', error.response.status);
        console.log('   Response Data:', JSON.stringify(error.response.data, null, 2));
      }
      
      console.log('');
      return false;
    }
  }

  /**
   * Test alternative endpoints
   */
  async testAlternativeEndpoints() {
    console.log('🔍 [PLUGINNG_DEBUG] Testing alternative endpoints...');
    
    const endpoints = [
      '/api/login',
      '/v1/login',
      '/auth/login',
      '/user/login'
    ];
    
    for (const endpoint of endpoints) {
      try {
        const url = `${this.baseUrl}${endpoint}`;
        console.log(`   Testing: ${url}`);
        
        const response = await axios.post(url, this.credentials, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          timeout: 10000,
          validateStatus: () => true
        });
        
        console.log(`   Status: ${response.status}`);
        
        if (response.status !== 404) {
          console.log(`   ✅ Endpoint exists: ${endpoint}`);
          if (response.data) {
            console.log(`   Data: ${JSON.stringify(response.data, null, 2).substring(0, 200)}`);
          }
        }
        
      } catch (error) {
        console.log(`   ❌ ${endpoint}: ${error.message}`);
      }
    }
    
    console.log('');
  }

  /**
   * Run comprehensive debug
   */
  async runDebug() {
    console.log('🚀 [PLUGINNG_DEBUG] Starting comprehensive PluginNG API debug...\n');
    
    // Test 1: Basic connectivity
    const connected = await this.testConnectivity();
    
    // Test 2: Alternative endpoints if main fails
    if (!connected) {
      await this.testAlternativeEndpoints();
    }
    
    // Test 3: Authentication
    const token = await this.testAuthentication();
    
    // Test 4: Data plans endpoint
    if (token) {
      await this.testDataPlansEndpoint(token);
    }
    
    console.log('🏁 [PLUGINNG_DEBUG] Debug completed');
    console.log('');
    console.log('💡 Recommendations:');
    console.log('   1. Check if PluginNG API is currently operational');
    console.log('   2. Verify your credentials are correct');
    console.log('   3. Check if API endpoints have changed');
    console.log('   4. Consider contacting PluginNG support if issues persist');
  }
}

// Run debug if this file is executed directly
if (require.main === module) {
  const debugger = new PluginNGDebugger();
  debugger.runDebug().catch(console.error);
}

module.exports = PluginNGDebugger;
