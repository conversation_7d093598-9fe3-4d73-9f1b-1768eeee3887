/**
 * Real-Time Update Service
 * 
 * WebSocket service for real-time data plan updates to frontend
 * Handles client connections and broadcasts plan changes
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

let WebSocket;
try {
  WebSocket = require('ws');
} catch (error) {
  console.warn('⚠️ WebSocket (ws) package not available, WebSocket features disabled');
}
const EventEmitter = require('events');

const logger = require('../../utils/logger');
const dataPlanManager = require('./dataPlanManager');
const { websocketSecurityMiddleware } = require('../middleware/security.middleware');

class RealTimeUpdateService extends EventEmitter {
  constructor() {
    super();
    this.wss = null;
    this.clients = new Map();
    this.isInitialized = false;
    
    // Client types
    this.CLIENT_TYPES = {
      ADMIN: 'admin',
      USER: 'user',
      SYSTEM: 'system'
    };
    
    // Message types
    this.MESSAGE_TYPES = {
      PLANS_UPDATED: 'plans_updated',
      PRICE_CHANGES: 'price_changes',
      VENDOR_ERROR: 'vendor_error',
      STATUS_UPDATE: 'status_update',
      HEARTBEAT: 'heartbeat'
    };
  }

  /**
   * Initialize WebSocket server
   */
  initialize(server) {
    try {
      if (!WebSocket) {
        logger.warn('⚠️ [REALTIME_UPDATE_SERVICE] WebSocket not available, skipping initialization');
        return;
      }

      this.wss = new WebSocket.Server({
        server,
        path: '/ws/data-plans'
      });

      this.setupWebSocketHandlers();
      this.setupDataPlanManagerListeners();
      this.startHeartbeat();
      
      this.isInitialized = true;
      
      logger.info('✅ [REALTIME_UPDATE_SERVICE] WebSocket server initialized');
    } catch (error) {
      logger.error('❌ [REALTIME_UPDATE_SERVICE] Failed to initialize WebSocket server:', {
        error: error.message
      });
    }
  }

  /**
   * Set up WebSocket event handlers
   */
  setupWebSocketHandlers() {
    this.wss.on('connection', (ws, request) => {
      this.handleNewConnection(ws, request);
    });

    this.wss.on('error', (error) => {
      logger.error('❌ [REALTIME_UPDATE_SERVICE] WebSocket server error:', {
        error: error.message
      });
    });
  }

  /**
   * Handle new WebSocket connection
   */
  handleNewConnection(ws, request) {
    // Apply security middleware
    if (!websocketSecurityMiddleware(ws, request)) {
      return; // Connection was rejected by security middleware
    }

    const clientId = this.generateClientId();
    const clientInfo = {
      id: clientId,
      ws,
      type: this.CLIENT_TYPES.USER, // Default type
      connectedAt: new Date().toISOString(),
      lastHeartbeat: Date.now(),
      isAlive: true,
      ip: request.connection.remoteAddress
    };

    // Store client
    this.clients.set(clientId, clientInfo);

    logger.info('🔗 [REALTIME_UPDATE_SERVICE] New client connected:', {
      clientId,
      totalClients: this.clients.size,
      userAgent: request.headers['user-agent']
    });

    // Set up client event handlers
    ws.on('message', (message) => {
      this.handleClientMessage(clientId, message);
    });

    ws.on('close', (code, reason) => {
      this.handleClientDisconnect(clientId, code, reason);
    });

    ws.on('error', (error) => {
      logger.error('❌ [REALTIME_UPDATE_SERVICE] Client error:', {
        clientId,
        error: error.message
      });
    });

    ws.on('pong', () => {
      const client = this.clients.get(clientId);
      if (client) {
        client.isAlive = true;
        client.lastHeartbeat = Date.now();
      }
    });

    // Send welcome message
    this.sendToClient(clientId, {
      type: 'connection_established',
      clientId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle client messages
   */
  handleClientMessage(clientId, message) {
    try {
      const data = JSON.parse(message);
      const client = this.clients.get(clientId);
      
      if (!client) return;

      logger.debug('📨 [REALTIME_UPDATE_SERVICE] Client message received:', {
        clientId,
        type: data.type
      });

      switch (data.type) {
        case 'set_client_type':
          this.handleSetClientType(clientId, data);
          break;
          
        case 'subscribe_to_vendor':
          this.handleVendorSubscription(clientId, data);
          break;
          
        case 'request_current_plans':
          this.handleCurrentPlansRequest(clientId, data);
          break;
          
        case 'heartbeat':
          this.handleHeartbeat(clientId);
          break;
          
        default:
          logger.warn('⚠️ [REALTIME_UPDATE_SERVICE] Unknown message type:', {
            clientId,
            type: data.type
          });
      }
    } catch (error) {
      logger.error('❌ [REALTIME_UPDATE_SERVICE] Failed to handle client message:', {
        clientId,
        error: error.message
      });
    }
  }

  /**
   * Handle client type setting
   */
  handleSetClientType(clientId, data) {
    const client = this.clients.get(clientId);
    if (!client) return;

    if (Object.values(this.CLIENT_TYPES).includes(data.clientType)) {
      client.type = data.clientType;
      client.metadata = data.metadata || {};
      
      logger.info('👤 [REALTIME_UPDATE_SERVICE] Client type set:', {
        clientId,
        type: data.clientType
      });

      this.sendToClient(clientId, {
        type: 'client_type_set',
        clientType: data.clientType,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Handle vendor subscription
   */
  handleVendorSubscription(clientId, data) {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.subscribedVendors = data.vendors || [];
    
    logger.info('📡 [REALTIME_UPDATE_SERVICE] Client subscribed to vendors:', {
      clientId,
      vendors: client.subscribedVendors
    });

    this.sendToClient(clientId, {
      type: 'subscription_confirmed',
      vendors: client.subscribedVendors,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Handle current plans request
   */
  async handleCurrentPlansRequest(clientId, data) {
    try {
      const plans = await dataPlanManager.getAllPlans();
      
      this.sendToClient(clientId, {
        type: 'current_plans',
        data: plans,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      this.sendToClient(clientId, {
        type: 'error',
        message: 'Failed to fetch current plans',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Handle heartbeat
   */
  handleHeartbeat(clientId) {
    const client = this.clients.get(clientId);
    if (client) {
      client.lastHeartbeat = Date.now();
      client.isAlive = true;
    }
  }

  /**
   * Handle client disconnect
   */
  handleClientDisconnect(clientId, code, reason) {
    this.clients.delete(clientId);
    
    logger.info('🔌 [REALTIME_UPDATE_SERVICE] Client disconnected:', {
      clientId,
      code,
      reason: reason?.toString(),
      totalClients: this.clients.size
    });
  }

  /**
   * Set up data plan manager listeners
   */
  setupDataPlanManagerListeners() {
    dataPlanManager.on('dataPlansUpdated', (data) => {
      this.broadcastPlansUpdate(data);
    });

    dataPlanManager.on('priceChangesDetected', (data) => {
      this.broadcastPriceChanges(data);
    });

    dataPlanManager.on('vendorError', (data) => {
      this.broadcastVendorError(data);
    });
  }

  /**
   * Broadcast plans update to clients
   */
  broadcastPlansUpdate(data) {
    const message = {
      type: this.MESSAGE_TYPES.PLANS_UPDATED,
      vendor: data.vendor,
      updateId: data.updateId,
      changes: data.changes,
      timestamp: data.timestamp
    };

    this.broadcast(message, (client) => {
      // Send to all clients or only subscribed ones
      return !client.subscribedVendors || 
             client.subscribedVendors.length === 0 || 
             client.subscribedVendors.includes(data.vendor);
    });

    logger.info('📢 [REALTIME_UPDATE_SERVICE] Plans update broadcasted:', {
      vendor: data.vendor,
      clientsNotified: this.getActiveClientsCount()
    });
  }

  /**
   * Broadcast price changes to clients
   */
  broadcastPriceChanges(data) {
    const message = {
      type: this.MESSAGE_TYPES.PRICE_CHANGES,
      vendor: data.vendor,
      changes: data.changes,
      timestamp: data.timestamp
    };

    // Send to admin clients only
    this.broadcast(message, (client) => {
      return client.type === this.CLIENT_TYPES.ADMIN;
    });

    logger.info('💰 [REALTIME_UPDATE_SERVICE] Price changes broadcasted to admins:', {
      vendor: data.vendor,
      totalChanges: data.changes.length
    });
  }

  /**
   * Broadcast vendor error to clients
   */
  broadcastVendorError(data) {
    const message = {
      type: this.MESSAGE_TYPES.VENDOR_ERROR,
      vendor: data.vendor,
      error: data.error,
      timestamp: data.timestamp
    };

    // Send to admin clients only
    this.broadcast(message, (client) => {
      return client.type === this.CLIENT_TYPES.ADMIN;
    });

    logger.warn('⚠️ [REALTIME_UPDATE_SERVICE] Vendor error broadcasted to admins:', {
      vendor: data.vendor
    });
  }

  /**
   * Send message to specific client
   */
  sendToClient(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      client.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      logger.error('❌ [REALTIME_UPDATE_SERVICE] Failed to send message to client:', {
        clientId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Broadcast message to multiple clients
   */
  broadcast(message, filter = null) {
    let sentCount = 0;

    for (const [clientId, client] of this.clients) {
      if (client.ws.readyState === WebSocket.OPEN) {
        if (!filter || filter(client)) {
          if (this.sendToClient(clientId, message)) {
            sentCount++;
          }
        }
      }
    }

    return sentCount;
  }

  /**
   * Start heartbeat to detect dead connections
   */
  startHeartbeat() {
    setInterval(() => {
      this.wss.clients.forEach((ws) => {
        const client = Array.from(this.clients.values()).find(c => c.ws === ws);
        
        if (client) {
          if (!client.isAlive) {
            logger.info('💀 [REALTIME_UPDATE_SERVICE] Terminating dead connection:', {
              clientId: client.id
            });
            ws.terminate();
            this.clients.delete(client.id);
            return;
          }
          
          client.isAlive = false;
          ws.ping();
        }
      });
    }, 30000); // 30 seconds
  }

  /**
   * Generate unique client ID
   */
  generateClientId() {
    return `client_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Get active clients count
   */
  getActiveClientsCount() {
    return Array.from(this.clients.values()).filter(
      client => client.ws.readyState === WebSocket.OPEN
    ).length;
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      totalClients: this.clients.size,
      activeClients: this.getActiveClientsCount(),
      clientTypes: this.getClientTypeDistribution()
    };
  }

  /**
   * Get client type distribution
   */
  getClientTypeDistribution() {
    const distribution = {};
    
    for (const client of this.clients.values()) {
      distribution[client.type] = (distribution[client.type] || 0) + 1;
    }
    
    return distribution;
  }
}

// Create and export singleton instance
const realTimeUpdateService = new RealTimeUpdateService();

module.exports = realTimeUpdateService;
