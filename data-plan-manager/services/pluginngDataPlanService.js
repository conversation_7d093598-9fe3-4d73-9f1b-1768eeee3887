/**
 * PluginNG Data Plan Service
 * 
 * Service for fetching, parsing, and managing PluginNG data plans
 * Handles authentication, plan retrieval, and price change detection
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const cron = require('node-cron');
const EventEmitter = require('events');

const logger = require('../../utils/logger');
const pluginNGConfig = require('../config/pluginng.config');

class PluginNGDataPlanService extends EventEmitter {
  constructor() {
    super();
    this.vendorId = 'pluginng';
    this.authToken = null;
    this.tokenExpiry = null;
    this.isUpdating = false;
    this.lastUpdate = null;
    this.updateJob = null;
    
    // File paths
    this.dataDir = path.join(__dirname, '../data/vendors/pluginng');
    this.plansFile = path.join(this.dataDir, 'plans.json');
    this.metadataFile = path.join(this.dataDir, 'metadata.json');
    this.historyFile = path.join(this.dataDir, 'price-history.json');
    
    this.initializeService();
  }

  /**
   * Initialize the service
   */
  async initializeService() {
    try {
      // Create data directory if it doesn't exist
      await this.ensureDataDirectory();
      
      // Start scheduled updates
      this.startScheduledUpdates();
      
      logger.info('✅ [PLUGINNG_DATA_SERVICE] Service initialized successfully');
    } catch (error) {
      logger.error('❌ [PLUGINNG_DATA_SERVICE] Service initialization failed:', {
        error: error.message
      });
    }
  }

  /**
   * Ensure data directory exists
   */
  async ensureDataDirectory() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  /**
   * Authenticate with PluginNG API
   */
  async authenticate() {
    try {
      logger.info('🔐 [PLUGINNG_DATA_SERVICE] Authenticating with PluginNG API...');

      // Use exact same format as your working script
      const url = 'https://pluginng.com/api/login';
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };
      const body = {
        email: pluginNGConfig.credentials.email,
        password: pluginNGConfig.credentials.password
      };

      logger.info('🔐 [PLUGINNG_DATA_SERVICE] Request details:', {
        url,
        email: body.email.replace(/(.{3}).*(@.*)/, '$1***$2'), // Mask email for security
        headers: { ...headers, 'Content-Type': headers['Content-Type'] } // Only log safe headers
      });

      const response = await axios.post(url, body, { headers });

      logger.info('📥 [PLUGINNG_DATA_SERVICE] Authentication response:', {
        status: response.status,
        success: response.data?.success,
        hasToken: !!response.data?.data?.token,
        message: response.data?.message
      });

      if (response.data?.success && response.data?.data?.token) {
        this.authToken = response.data.data.token;
        this.tokenExpiry = Date.now() + (8 * 60 * 1000); // 8 minutes from now

        logger.info('✅ [PLUGINNG_DATA_SERVICE] Authentication successful:', {
          tokenPreview: this.authToken.substring(0, 20) + '...' + this.authToken.slice(-10), // Show only start and end
          userInfo: {
            name: `${response.data.data.firstname} ${response.data.data.lastname}`,
            email: response.data.data.email.replace(/(.{3}).*(@.*)/, '$1***$2'), // Mask email
            wallet: response.data.data.wallet,
            userId: response.data.data.id
          }
        });
        return true;
      } else {
        throw new Error(`Authentication failed - Success: ${response.data?.success}, Message: ${response.data?.message}`);
      }
    } catch (error) {
      logger.error('❌ [PLUGINNG_DATA_SERVICE] Authentication failed:', {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
      throw error;
    }
  }

  /**
   * Check if token is valid
   */
  isTokenValid() {
    const isValid = this.authToken && this.tokenExpiry && Date.now() < this.tokenExpiry;

    if (!isValid && this.authToken) {
      logger.info('🔄 [PLUGINNG_DATA_SERVICE] Token expired, will re-authenticate');
    }

    return isValid;
  }

  /**
   * Ensure valid authentication
   */
  async ensureAuthenticated() {
    if (!this.isTokenValid()) {
      await this.authenticate();
    }
  }

  /**
   * Fetch data plans from PluginNG API
   */
  async fetchDataPlans() {
    try {
      await this.ensureAuthenticated();

      logger.info('📥 [PLUGINNG_DATA_SERVICE] Fetching data plans from API...');
      logger.info('📥 [PLUGINNG_DATA_SERVICE] Request details:', {
        url: `${pluginNGConfig.baseUrl}${pluginNGConfig.endpoints.getPlans}`,
        hasToken: !!this.authToken,
        tokenPreview: this.authToken ? this.authToken.substring(0, 15) + '...' : 'NO TOKEN'
      });

      const response = await axios.get(
        `${pluginNGConfig.baseUrl}${pluginNGConfig.endpoints.getPlans}`,
        {
          headers: pluginNGConfig.getAuthHeaders(this.authToken),
          timeout: 30000,
          validateStatus: function (status) {
            return status < 600; // Accept any status less than 600
          }
        }
      );

      logger.info('📥 [PLUGINNG_DATA_SERVICE] Data plans response:', {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        dataType: typeof response.data,
        dataKeys: response.data ? Object.keys(response.data) : 'NO DATA'
      });

      if (response.status === 200 && response.data?.data) {
        logger.info('✅ [PLUGINNG_DATA_SERVICE] Data plans fetched successfully:', {
          totalCategories: response.data.data.length
        });

        return response.data.data;
      } else {
        throw new Error(`Invalid response - Status: ${response.status}, Data: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      logger.error('❌ [PLUGINNG_DATA_SERVICE] Failed to fetch data plans:', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        headers: error.response?.headers,
        data: error.response?.data
      });
      throw error;
    }
  }

  /**
   * Parse and normalize plan data
   */
  parseDataPlans(rawPlans) {
    try {
      const normalizedPlans = {
        vendor: {
          id: this.vendorId,
          name: pluginNGConfig.vendorName,
          lastUpdated: new Date().toISOString(),
          totalCategories: rawPlans.length
        },
        networks: {}
      };

      let totalPlans = 0;

      rawPlans.forEach(category => {
        // Filter only data plan categories (skip electricity, TV, etc.)
        const isDataCategory = category.category === 'Data' ||
                              (category.title && category.title.toLowerCase().includes('data')) ||
                              ['MTN SME', 'MTN CG', 'AIRTEL DIRECT', 'GLO DIRECT', '9MOBILE DIRECT'].includes(category.title);

        if (!isDataCategory) {
          logger.debug('📋 [PLUGINNG_DATA_SERVICE] Skipping non-data category:', {
            title: category.title,
            category: category.category
          });
          return;
        }

        // Validate category data
        const validation = pluginNGConfig.validatePlanData(category);
        if (!validation.isValid) {
          logger.warn('⚠️ [PLUGINNG_DATA_SERVICE] Invalid data category:', {
            title: category.title,
            errors: validation.errors
          });
          return;
        }

        // Get network info
        const networkInfo = pluginNGConfig.getNetworkInfo(category.title);
        const networkKey = `${networkInfo.network}_${networkInfo.type}`;

        // Initialize network if not exists
        if (!normalizedPlans.networks[networkKey]) {
          normalizedPlans.networks[networkKey] = {
            network: networkInfo.network,
            type: networkInfo.type,
            displayName: category.title,
            subcategoryId: category.subcategory_id,
            status: category.status === '1' ? 'active' : 'inactive',
            plans: []
          };
        }

        // Process individual plans
        category.plan.forEach(plan => {
          const parsedPlan = pluginNGConfig.parsePlanName(plan.plan);
          const pricing = pluginNGConfig.calculateSellingPrice(plan.amount);

          const normalizedPlan = {
            planId: `${this.vendorId}_${category.subcategory_id}_${plan.plan.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}`,
            name: plan.plan,
            size: parsedPlan.size,
            validity: parsedPlan.validity,
            type: parsedPlan.type,
            vendorPrice: plan.amount,
            markup: pricing.markupAmount,
            sellingPrice: pricing.sellingPrice,
            profitMargin: pricing.profitMargin,
            subcategoryId: category.subcategory_id,
            status: 'active',
            lastUpdated: new Date().toISOString()
          };

          normalizedPlans.networks[networkKey].plans.push(normalizedPlan);
          totalPlans++;
        });
      });

      normalizedPlans.vendor.totalPlans = totalPlans;

      logger.info('✅ [PLUGINNG_DATA_SERVICE] Data plans parsed successfully:', {
        totalNetworks: Object.keys(normalizedPlans.networks).length,
        totalPlans
      });

      return normalizedPlans;
    } catch (error) {
      logger.error('❌ [PLUGINNG_DATA_SERVICE] Plan parsing failed:', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Load existing plans from file
   */
  async loadExistingPlans() {
    try {
      const data = await fs.readFile(this.plansFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return null; // File doesn't exist
      }
      throw error;
    }
  }

  /**
   * Detect price changes
   */
  detectPriceChanges(oldPlans, newPlans) {
    const changes = [];

    if (!oldPlans) {
      return { type: 'initial_load', changes: [] };
    }

    // Compare plans across networks
    Object.keys(newPlans.networks).forEach(networkKey => {
      const newNetwork = newPlans.networks[networkKey];
      const oldNetwork = oldPlans.networks?.[networkKey];

      if (!oldNetwork) {
        changes.push({
          type: 'new_network',
          network: networkKey,
          plansCount: newNetwork.plans.length
        });
        return;
      }

      // Compare individual plans
      newNetwork.plans.forEach(newPlan => {
        const oldPlan = oldNetwork.plans.find(p => p.planId === newPlan.planId);

        if (!oldPlan) {
          changes.push({
            type: 'new_plan',
            network: networkKey,
            plan: newPlan
          });
        } else if (oldPlan.vendorPrice !== newPlan.vendorPrice) {
          changes.push({
            type: 'price_change',
            network: networkKey,
            planId: newPlan.planId,
            planName: newPlan.name,
            oldPrice: oldPlan.vendorPrice,
            newPrice: newPlan.vendorPrice,
            oldSellingPrice: oldPlan.sellingPrice,
            newSellingPrice: newPlan.sellingPrice,
            priceChange: newPlan.vendorPrice - oldPlan.vendorPrice,
            percentageChange: Math.round(((newPlan.vendorPrice - oldPlan.vendorPrice) / oldPlan.vendorPrice) * 100)
          });
        }
      });
    });

    return {
      type: 'update',
      changes,
      hasChanges: changes.length > 0
    };
  }

  /**
   * Save plans to file
   */
  async savePlans(plans) {
    try {
      await fs.writeFile(this.plansFile, JSON.stringify(plans, null, 2));
      
      // Update metadata
      const metadata = {
        lastUpdate: new Date().toISOString(),
        totalNetworks: Object.keys(plans.networks).length,
        totalPlans: plans.vendor.totalPlans,
        vendor: plans.vendor
      };
      
      await fs.writeFile(this.metadataFile, JSON.stringify(metadata, null, 2));
      
      logger.info('✅ [PLUGINNG_DATA_SERVICE] Plans saved successfully');
    } catch (error) {
      logger.error('❌ [PLUGINNG_DATA_SERVICE] Failed to save plans:', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Save price change history
   */
  async savePriceHistory(changes) {
    try {
      if (!changes.hasChanges) return;

      let history = [];
      try {
        const data = await fs.readFile(this.historyFile, 'utf8');
        history = JSON.parse(data);
      } catch (error) {
        // File doesn't exist, start with empty array
      }

      const historyEntry = {
        timestamp: new Date().toISOString(),
        changes: changes.changes,
        totalChanges: changes.changes.length
      };

      history.unshift(historyEntry);

      // Keep only last 100 entries
      if (history.length > 100) {
        history = history.slice(0, 100);
      }

      await fs.writeFile(this.historyFile, JSON.stringify(history, null, 2));

      logger.info('✅ [PLUGINNG_DATA_SERVICE] Price history saved');
    } catch (error) {
      logger.error('❌ [PLUGINNG_DATA_SERVICE] Failed to save price history:', {
        error: error.message
      });
    }
  }

  /**
   * Update data plans
   */
  async updateDataPlans() {
    if (this.isUpdating) {
      logger.warn('⚠️ [PLUGINNG_DATA_SERVICE] Update already in progress, skipping...');
      return;
    }

    this.isUpdating = true;
    const updateId = `update_${Date.now()}`;

    try {
      logger.info('🔄 [PLUGINNG_DATA_SERVICE] Starting data plan update:', { updateId });

      // Fetch fresh data from API
      const rawPlans = await this.fetchDataPlans();

      // Parse and normalize the data
      const newPlans = this.parseDataPlans(rawPlans);

      // Load existing plans for comparison
      const oldPlans = await this.loadExistingPlans();

      // Detect changes
      const changes = this.detectPriceChanges(oldPlans, newPlans);

      // Save new plans
      await this.savePlans(newPlans);

      // Save price history if there are changes
      await this.savePriceHistory(changes);

      // Update last update time
      this.lastUpdate = new Date().toISOString();

      // Emit events for real-time updates
      this.emit('plansUpdated', {
        updateId,
        vendor: this.vendorId,
        changes,
        timestamp: this.lastUpdate
      });

      if (changes.hasChanges) {
        this.emit('priceChanges', {
          updateId,
          vendor: this.vendorId,
          changes: changes.changes,
          timestamp: this.lastUpdate
        });
      }

      logger.info('✅ [PLUGINNG_DATA_SERVICE] Data plan update completed:', {
        updateId,
        hasChanges: changes.hasChanges,
        totalChanges: changes.changes?.length || 0,
        totalPlans: newPlans.vendor.totalPlans
      });

      return {
        success: true,
        updateId,
        changes,
        totalPlans: newPlans.vendor.totalPlans
      };

    } catch (error) {
      logger.error('❌ [PLUGINNG_DATA_SERVICE] Data plan update failed:', {
        updateId,
        error: error.message
      });

      this.emit('updateError', {
        updateId,
        vendor: this.vendorId,
        error: error.message,
        timestamp: new Date().toISOString()
      });

      throw error;
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * Start scheduled updates
   */
  startScheduledUpdates() {
    if (this.updateJob) {
      this.updateJob.stop();
    }

    if (!pluginNGConfig.updateConfig.enabled) {
      logger.info('ℹ️ [PLUGINNG_DATA_SERVICE] Scheduled updates disabled');
      return;
    }

    this.updateJob = cron.schedule(pluginNGConfig.updateConfig.frequency, async () => {
      try {
        await this.updateDataPlans();
      } catch (error) {
        logger.error('❌ [PLUGINNG_DATA_SERVICE] Scheduled update failed:', {
          error: error.message
        });
      }
    }, {
      scheduled: true,
      timezone: 'Africa/Lagos'
    });

    logger.info('✅ [PLUGINNG_DATA_SERVICE] Scheduled updates started:', {
      frequency: pluginNGConfig.updateConfig.frequency
    });
  }

  /**
   * Stop scheduled updates
   */
  stopScheduledUpdates() {
    if (this.updateJob) {
      this.updateJob.stop();
      this.updateJob = null;
      logger.info('🛑 [PLUGINNG_DATA_SERVICE] Scheduled updates stopped');
    }
  }

  /**
   * Get current plans
   */
  async getCurrentPlans() {
    try {
      return await this.loadExistingPlans();
    } catch (error) {
      logger.error('❌ [PLUGINNG_DATA_SERVICE] Failed to load current plans:', {
        error: error.message
      });
      return null;
    }
  }

  /**
   * Get plans by network
   */
  async getPlansByNetwork(network) {
    try {
      const plans = await this.getCurrentPlans();
      if (!plans) return null;

      const networkPlans = {};
      Object.keys(plans.networks).forEach(key => {
        if (plans.networks[key].network === network) {
          networkPlans[key] = plans.networks[key];
        }
      });

      return Object.keys(networkPlans).length > 0 ? networkPlans : null;
    } catch (error) {
      logger.error('❌ [PLUGINNG_DATA_SERVICE] Failed to get plans by network:', {
        network,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Get service metadata
   */
  async getMetadata() {
    try {
      const data = await fs.readFile(this.metadataFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      return {
        lastUpdate: null,
        totalNetworks: 0,
        totalPlans: 0,
        vendor: {
          id: this.vendorId,
          name: pluginNGConfig.vendorName
        }
      };
    }
  }

  /**
   * Force update (manual trigger)
   */
  async forceUpdate() {
    logger.info('🔄 [PLUGINNG_DATA_SERVICE] Manual update triggered');
    return await this.updateDataPlans();
  }
}

// Create and export singleton instance
const pluginNGDataPlanService = new PluginNGDataPlanService();

module.exports = pluginNGDataPlanService;

