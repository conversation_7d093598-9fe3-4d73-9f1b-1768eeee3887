#!/bin/bash

# SSL Setup Script for Vendy Admin Dashboard
# Run this AFTER adding the DNS A record for admin.payvendy.name.ng

set -e

echo "🔐 Setting up SSL certificate for admin.payvendy.name.ng..."

# Check if domain resolves
echo "🔍 Checking DNS resolution..."
DNS_RESULT=$(dig +short admin.payvendy.name.ng @*******)
if [ -z "$DNS_RESULT" ]; then
    echo "❌ DNS not resolved yet. Please add the A record first:"
    echo "   Type: A"
    echo "   Name: admin"
    echo "   Value: ***************"
    echo "   TTL: 3600"
    exit 1
fi

echo "✅ DNS resolved to: $DNS_RESULT"

echo "✅ DNS resolved successfully"

# Get SSL certificate
echo "🔐 Obtaining SSL certificate with Certbot..."
sudo certbot --nginx -d admin.payvendy.name.ng \
    --non-interactive \
    --agree-tos \
    --email <EMAIL> \
    --redirect

# Test Nginx configuration
echo "🧪 Testing Nginx configuration..."
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx configuration is valid"
    sudo systemctl reload nginx
    echo "🔄 Nginx reloaded successfully"
else
    echo "❌ Nginx configuration test failed"
    exit 1
fi

# Test HTTPS access
echo "🌐 Testing HTTPS access..."
sleep 5

if curl -f -s https://admin.payvendy.name.ng/health > /dev/null; then
    echo "✅ HTTPS setup successful!"
    echo ""
    echo "🎉 Admin Dashboard is now available at:"
    echo "   https://admin.payvendy.name.ng"
    echo ""
    echo "🔐 Login with your admin credentials"
else
    echo "⚠️ HTTPS test failed. Please check the logs:"
    echo "   sudo tail -f /var/log/nginx/error.log"
fi

echo ""
echo "📋 Next steps:"
echo "1. Visit https://admin.payvendy.name.ng"
echo "2. Login with your admin account"
echo "3. Start managing OTA updates!"

exit 0
