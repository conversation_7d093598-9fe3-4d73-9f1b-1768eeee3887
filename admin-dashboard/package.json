{"name": "vendy-admin-dashboard", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON> Dashboard for OTA Updates Management", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required for static files'", "test": "echo 'No tests specified'"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "axios": "^1.4.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^2.0.22"}, "keywords": ["admin", "dashboard", "ota", "updates", "vendy"], "author": "Vendy Team", "license": "MIT"}