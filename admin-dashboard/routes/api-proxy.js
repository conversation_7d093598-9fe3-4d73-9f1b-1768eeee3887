/**
 * API Proxy Routes
 * 
 * Proxies requests to the main API to avoid CORS issues
 */

const express = require('express');
const axios = require('axios');
const multer = require('multer');
const router = express.Router();

const API_BASE_URL = process.env.API_BASE_URL || 'https://api.payvendy.name.ng';

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  }
});

// Helper function to forward requests
const forwardRequest = async (req, res, method, endpoint, data = null, headers = {}) => {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        ...headers,
        'Authorization': req.headers.authorization || req.cookies.adminToken ? `Bearer ${req.cookies.adminToken}` : undefined
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    res.status(response.status).json(response.data);
  } catch (error) {
    console.error('API Proxy Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json(
      error.response?.data || { error: 'API request failed' }
    );
  }
};

// Authentication routes
router.post('/auth/login', async (req, res) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/v1/admin/login`, req.body);
    
    if (response.data.status === 'success' && response.data.data.user.role === 'admin') {
      // Set HTTP-only cookie for security
      res.cookie('adminToken', response.data.data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });
      
      res.json({
        success: true,
        message: 'Login successful',
        user: {
          id: response.data.data.user.id,
          email: response.data.data.user.email,
          role: response.data.data.user.role
        }
      });
    } else {
      res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }
  } catch (error) {
    console.error('Login Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json(
      error.response?.data || { error: 'Login failed' }
    );
  }
});

// OTA Updates routes
router.get('/admin/ota/updates', (req, res) => {
  forwardRequest(req, res, 'GET', '/api/v1/admin/ota/updates');
});

router.post('/admin/ota/updates', upload.single('bundle'), async (req, res) => {
  try {
    const FormData = require('form-data');
    const formData = new FormData();
    
    // Add file if present
    if (req.file) {
      formData.append('bundle', req.file.buffer, {
        filename: req.file.originalname,
        contentType: req.file.mimetype
      });
    }
    
    // Add other fields
    Object.keys(req.body).forEach(key => {
      formData.append(key, req.body[key]);
    });

    const response = await axios.post(
      `${API_BASE_URL}/api/v1/admin/ota/updates`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': req.cookies.adminToken ? `Bearer ${req.cookies.adminToken}` : req.headers.authorization
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      }
    );

    res.status(response.status).json(response.data);
  } catch (error) {
    console.error('Upload Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json(
      error.response?.data || { error: 'Upload failed' }
    );
  }
});

router.put('/admin/ota/updates/:updateId', (req, res) => {
  forwardRequest(req, res, 'PUT', `/api/v1/admin/ota/updates/${req.params.updateId}`, req.body);
});

router.delete('/admin/ota/updates/:updateId', (req, res) => {
  forwardRequest(req, res, 'DELETE', `/api/v1/admin/ota/updates/${req.params.updateId}`);
});

// App updates routes (for testing)
router.post('/app/updates/check', (req, res) => {
  forwardRequest(req, res, 'POST', '/api/v1/app/updates/check', req.body);
});

router.post('/app/updates/analytics', (req, res) => {
  forwardRequest(req, res, 'POST', '/api/v1/app/updates/analytics', req.body);
});

// Health check
router.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    apiBaseUrl: API_BASE_URL
  });
});

// User management routes (if needed)
router.get('/admin/users', (req, res) => {
  forwardRequest(req, res, 'GET', '/api/v1/admin/users');
});

// System stats
router.get('/admin/stats', (req, res) => {
  forwardRequest(req, res, 'GET', '/api/v1/admin/stats');
});

module.exports = router;
