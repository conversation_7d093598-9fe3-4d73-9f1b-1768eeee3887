/**
 * Vendy Admin Dashboard Server
 * 
 * Serves the admin dashboard for managing OTA updates
 */

const express = require('express');
const path = require('path');
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const cookieParser = require('cookie-parser');

const app = express();
const PORT = process.env.ADMIN_PORT || 6000;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", process.env.API_BASE_URL || "https://api.payvendy.name.ng"]
    }
  }
}));

// CORS configuration
app.use(cors({
  origin: [
    'https://admin.payvendy.name.ng',
    'https://payvendy.name.ng',
    'http://localhost:3001',
    'http://localhost:3000'
  ],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs (increased for admin dashboard)
  message: 'Too many requests from this IP, please try again later.',
  skip: (req) => {
    // Skip rate limiting for localhost in development
    const isLocalhost = req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === '::ffff:127.0.0.1';
    return process.env.NODE_ENV === 'development' && isLocalhost;
  }
});
app.use(limiter);

// Middleware
app.set('trust proxy', 1);
app.use(compression());
app.use(cookieParser());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// API proxy routes (to avoid CORS issues)
app.use('/api', require('./routes/api-proxy'));

// Admin authentication middleware
const authenticateAdmin = (req, res, next) => {
  const token = req.cookies.adminToken || req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    if (req.path === '/login' || req.path === '/') {
      return next();
    }
    return res.redirect('/login');
  }
  
  // In a real app, verify the JWT token here
  // For now, we'll assume it's valid if present
  req.user = { isAdmin: true };
  next();
};

// Routes
app.get('/', (req, res) => {
  res.redirect('/dashboard');
});

app.get('/login', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

app.get('/dashboard', authenticateAdmin, (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'dashboard.html'));
});

app.get('/ota-updates', authenticateAdmin, (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'ota-updates.html'));
});

app.get('/analytics', authenticateAdmin, (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'analytics.html'));
});

app.get('/settings', authenticateAdmin, (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'settings.html'));
});

// Logout route
app.post('/logout', (req, res) => {
  res.clearCookie('adminToken');
  res.redirect('/login');
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'vendy-admin-dashboard'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).sendFile(path.join(__dirname, 'public', '404.html'));
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Admin Dashboard Error:', err);
  res.status(500).json({ 
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Vendy Admin Dashboard running on port ${PORT}`);
  console.log(`📊 Dashboard URL: http://localhost:${PORT}`);
  console.log(`🔐 Login URL: http://localhost:${PORT}/login`);
});

module.exports = app;
