# Vendy Admin Dashboard Environment Configuration

# Server Configuration
NODE_ENV=production
ADMIN_PORT=3001

# API Configuration
API_BASE_URL=https://api.payvendy.name.ng

# Security
SESSION_SECRET=your-super-secret-session-key-here
JWT_SECRET=your-jwt-secret-here

# CORS Configuration
ALLOWED_ORIGINS=https://admin.payvendy.name.ng,https://payvendy.name.ng

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=104857600
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/admin-dashboard.log

# Database (if needed for admin-specific data)
# DATABASE_URL=postgresql://username:password@localhost:5432/vendy_admin

# Email Configuration (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-email-password
# FROM_EMAIL=<EMAIL>

# Monitoring
# SENTRY_DSN=https://your-sentry-dsn-here

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_FILE_UPLOAD=true
ENABLE_BULK_OPERATIONS=true

# Cache Configuration
CACHE_TTL=3600
REDIS_URL=redis://localhost:6379

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
