#!/bin/bash

# Vendy Admin Dashboard Deployment Script
# This script deploys the admin dashboard to admin.payvendy.name.ng

set -e

echo "🚀 Starting Vendy Admin Dashboard Deployment..."

# Configuration
DOMAIN="admin.payvendy.name.ng"
APP_DIR="/var/www/vendy-admin"
NGINX_SITE="/etc/nginx/sites-available/$DOMAIN"
SERVICE_NAME="vendy-admin-dashboard"
PORT=3001

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check if required commands exist
command -v node >/dev/null 2>&1 || { print_error "Node.js is required but not installed. Aborting."; exit 1; }
command -v npm >/dev/null 2>&1 || { print_error "npm is required but not installed. Aborting."; exit 1; }
command -v pm2 >/dev/null 2>&1 || { print_error "PM2 is required but not installed. Run: npm install -g pm2"; exit 1; }

print_status "Creating application directory..."
sudo mkdir -p $APP_DIR
sudo chown $USER:$USER $APP_DIR

print_status "Copying application files..."
cp -r . $APP_DIR/
cd $APP_DIR

print_status "Installing dependencies..."
npm install --production

print_status "Setting up environment variables..."
if [ ! -f .env ]; then
    cat > .env << EOF
NODE_ENV=production
ADMIN_PORT=$PORT
API_BASE_URL=https://api.payvendy.name.ng
SESSION_SECRET=$(openssl rand -base64 32)
EOF
    print_status "Created .env file with default values"
    print_warning "Please update the .env file with your actual configuration"
fi

print_status "Setting up PM2 ecosystem file..."
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '$SERVICE_NAME',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: $PORT
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

print_status "Creating logs directory..."
mkdir -p logs

print_status "Setting up Nginx configuration..."
sudo cp nginx.conf $NGINX_SITE

print_status "Testing Nginx configuration..."
sudo nginx -t

if [ $? -eq 0 ]; then
    print_status "Nginx configuration is valid"
else
    print_error "Nginx configuration test failed"
    exit 1
fi

print_status "Enabling Nginx site..."
sudo ln -sf $NGINX_SITE /etc/nginx/sites-enabled/

print_status "Setting up SSL certificate with Let's Encrypt..."
if ! command -v certbot &> /dev/null; then
    print_warning "Certbot not found. Installing..."
    sudo apt update
    sudo apt install -y certbot python3-certbot-nginx
fi

# Check if certificate already exists
if [ ! -d "/etc/letsencrypt/live/$DOMAIN" ]; then
    print_status "Obtaining SSL certificate..."
    sudo certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL>
else
    print_status "SSL certificate already exists"
fi

print_status "Starting application with PM2..."
pm2 stop $SERVICE_NAME 2>/dev/null || true
pm2 delete $SERVICE_NAME 2>/dev/null || true
pm2 start ecosystem.config.js
pm2 save

print_status "Setting up PM2 startup script..."
pm2 startup | grep -E '^sudo' | bash || true

print_status "Reloading Nginx..."
sudo systemctl reload nginx

print_status "Setting up firewall rules..."
sudo ufw allow 'Nginx Full' 2>/dev/null || true

print_status "Creating backup script..."
cat > backup.sh << 'EOF'
#!/bin/bash
# Backup script for Vendy Admin Dashboard
BACKUP_DIR="/var/backups/vendy-admin"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/vendy-admin-$DATE.tar.gz \
    --exclude=node_modules \
    --exclude=logs \
    --exclude=.git \
    /var/www/vendy-admin

# Keep only last 7 backups
find $BACKUP_DIR -name "vendy-admin-*.tar.gz" -mtime +7 -delete

echo "Backup completed: vendy-admin-$DATE.tar.gz"
EOF

chmod +x backup.sh

print_status "Setting up log rotation..."
sudo tee /etc/logrotate.d/vendy-admin > /dev/null << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        pm2 reloadLogs
    endscript
}
EOF

print_status "Deployment completed successfully! 🎉"
echo ""
echo "📊 Dashboard URL: https://$DOMAIN"
echo "🔐 Login with your admin credentials"
echo ""
echo "📋 Useful commands:"
echo "  pm2 status                 - Check application status"
echo "  pm2 logs $SERVICE_NAME     - View application logs"
echo "  pm2 restart $SERVICE_NAME  - Restart application"
echo "  sudo nginx -t              - Test Nginx configuration"
echo "  sudo systemctl reload nginx - Reload Nginx"
echo ""
echo "🔧 Configuration files:"
echo "  Application: $APP_DIR"
echo "  Nginx: $NGINX_SITE"
echo "  Environment: $APP_DIR/.env"
echo ""

# Final health check
print_status "Performing health check..."
sleep 5

if curl -f -s https://$DOMAIN/health > /dev/null; then
    print_status "✅ Health check passed - Dashboard is running!"
else
    print_warning "⚠️ Health check failed - Please check the logs"
    echo "Debug commands:"
    echo "  pm2 logs $SERVICE_NAME"
    echo "  sudo tail -f /var/log/nginx/error.log"
fi

echo ""
print_status "🎯 Next steps:"
echo "1. Update the .env file with your actual API configuration"
echo "2. Test the dashboard by visiting https://$DOMAIN"
echo "3. Set up monitoring and alerts"
echo "4. Configure regular backups"

exit 0
