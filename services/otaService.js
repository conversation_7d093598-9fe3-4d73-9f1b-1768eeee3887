const OTAUpdate = require('../models/OTAUpdate');
const OTAAnalytics = require('../models/OTAAnalytics');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const logger = require('../utils/logger');

class OTAService {
  constructor() {
    this.bundlesPath = path.join(__dirname, '../storage/app-bundles');
  }

  /**
   * Store analytics event in database
   */
  async storeAnalyticsEvent(eventData) {
    try {
      const analytics = new OTAAnalytics(eventData);
      await analytics.save();
      
      // Update cached statistics
      if (eventData.updateId) {
        await this.updateCachedStats(eventData.updateId, eventData.event);
      }
      
      logger.info('📊 [OTA SERVICE] Analytics event stored', {
        event: eventData.event,
        updateId: eventData.updateId,
        platform: eventData.platform
      });
      
      return analytics;
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to store analytics event', error);
      throw error;
    }
  }

  /**
   * Update cached statistics for an update
   */
  async updateCachedStats(updateId, event) {
    try {
      const update = await OTAUpdate.findOne({ id: updateId });
      if (!update) return;

      switch (event) {
        case 'download_completed':
          update.stats.totalDownloads += 1;
          break;
        case 'install_completed':
          update.stats.successfulInstalls += 1;
          break;
        case 'install_failed':
          update.stats.failedInstalls += 1;
          break;
        case 'rollback_completed':
          update.stats.rollbacks += 1;
          break;
      }

      update.stats.lastUpdated = new Date();
      await update.save();
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to update cached stats', error);
    }
  }

  /**
   * Find applicable update for user
   */
  async findApplicableUpdate(currentVersion, currentBuildNumber, platform, userId = null, deviceId = null) {
    try {
      const update = await OTAUpdate.findApplicableUpdate(
        currentVersion,
        currentBuildNumber,
        platform,
        userId,
        deviceId
      );

      if (update) {
        // Track update check
        await this.storeAnalyticsEvent({
          event: 'update_check',
          updateId: update.id,
          version: currentVersion,
          buildNumber: currentBuildNumber,
          platform,
          userId,
          deviceId,
          timestamp: new Date()
        });
      }

      return update;
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to find applicable update', error);
      throw error;
    }
  }

  /**
   * Create new OTA update
   */
  async createUpdate(updateData, bundleFilePath, adminId) {
    try {
      // Calculate checksum and size
      const fileBuffer = await fs.readFile(bundleFilePath);
      const checksum = 'sha256:' + crypto.createHash('sha256').update(fileBuffer).digest('hex');
      const stats = await fs.stat(bundleFilePath);

      // Generate update ID
      const updateId = `vendy-v${updateData.version}-${updateData.platform}`;

      // Create update document
      const update = new OTAUpdate({
        id: updateId,
        version: updateData.version,
        buildNumber: updateData.buildNumber,
        platform: updateData.platform,
        filename: path.basename(bundleFilePath),
        downloadUrl: `/api/v1/app/updates/download/${updateId}`,
        checksum,
        size: stats.size,
        isMandatory: updateData.isMandatory || false,
        description: updateData.description,
        releaseNotes: updateData.releaseNotes || '',
        minAppVersion: updateData.minAppVersion || null,
        maxAppVersion: updateData.maxAppVersion || null,
        deployedAt: new Date(),
        createdBy: adminId,
        rollout: {
          strategy: updateData.rolloutStrategy || 'immediate',
          percentage: updateData.rolloutPercentage || 100,
          targetUsers: updateData.targetUsers || [],
          excludedUsers: updateData.excludedUsers || [],
          rolloutStartDate: updateData.rolloutStartDate || new Date(),
          rolloutEndDate: updateData.rolloutEndDate || null
        }
      });

      await update.save();

      logger.info('✅ [OTA SERVICE] New update created', {
        updateId,
        version: updateData.version,
        platform: updateData.platform,
        adminId
      });

      return update;
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to create update', error);
      throw error;
    }
  }

  /**
   * Update existing OTA update
   */
  async updateUpdate(updateId, updateData, adminId) {
    try {
      const update = await OTAUpdate.findOne({ id: updateId });
      if (!update) {
        throw new Error('Update not found');
      }

      // Update fields
      if (updateData.isActive !== undefined) update.isActive = updateData.isActive;
      if (updateData.isMandatory !== undefined) update.isMandatory = updateData.isMandatory;
      if (updateData.description) update.description = updateData.description;
      if (updateData.releaseNotes) update.releaseNotes = updateData.releaseNotes;
      
      // Update rollout configuration
      if (updateData.rolloutStrategy) update.rollout.strategy = updateData.rolloutStrategy;
      if (updateData.rolloutPercentage !== undefined) update.rollout.percentage = updateData.rolloutPercentage;
      if (updateData.targetUsers) update.rollout.targetUsers = updateData.targetUsers;
      if (updateData.excludedUsers) update.rollout.excludedUsers = updateData.excludedUsers;
      if (updateData.rolloutStartDate) update.rollout.rolloutStartDate = updateData.rolloutStartDate;
      if (updateData.rolloutEndDate) update.rollout.rolloutEndDate = updateData.rolloutEndDate;

      update.modifiedBy = adminId;
      update.modifiedAt = new Date();

      await update.save();

      logger.info('✅ [OTA SERVICE] Update modified', {
        updateId,
        adminId,
        changes: Object.keys(updateData)
      });

      return update;
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to update update', error);
      throw error;
    }
  }

  /**
   * Delete OTA update and cleanup files
   */
  async deleteUpdate(updateId, adminId) {
    try {
      const update = await OTAUpdate.findOne({ id: updateId });
      if (!update) {
        throw new Error('Update not found');
      }

      // Delete the bundle file
      const filePath = path.join(this.bundlesPath, update.filename);
      try {
        await fs.unlink(filePath);
        logger.info('🗑️ [OTA SERVICE] Bundle file deleted', { filePath });
      } catch (fileError) {
        logger.warn('⚠️ [OTA SERVICE] Failed to delete bundle file', { filePath, error: fileError.message });
      }

      // Delete from database
      await OTAUpdate.deleteOne({ id: updateId });

      logger.info('✅ [OTA SERVICE] Update deleted', {
        updateId,
        adminId
      });

      return update;
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to delete update', error);
      throw error;
    }
  }

  /**
   * Get comprehensive analytics
   */
  async getAnalytics(timeRange = 7) {
    try {
      const [updateStats, platformStats, successRates] = await Promise.all([
        OTAAnalytics.aggregate([
          {
            $match: {
              timestamp: { $gte: new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000) }
            }
          },
          {
            $group: {
              _id: '$event',
              count: { $sum: 1 }
            }
          }
        ]),
        OTAAnalytics.getPlatformStats(timeRange),
        OTAAnalytics.getSuccessRates(timeRange)
      ]);

      return {
        eventStats: updateStats,
        platformStats,
        successRates,
        timeRange
      };
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to get analytics', error);
      throw error;
    }
  }

  /**
   * Get update statistics
   */
  async getUpdateStatistics(updateId) {
    try {
      const [update, analytics] = await Promise.all([
        OTAUpdate.findOne({ id: updateId }),
        OTAAnalytics.getUpdateStats(updateId, 30)
      ]);

      if (!update) {
        throw new Error('Update not found');
      }

      return {
        update: {
          id: update.id,
          version: update.version,
          platform: update.platform,
          isActive: update.isActive,
          isMandatory: update.isMandatory,
          createdAt: update.createdAt,
          stats: update.stats
        },
        analytics
      };
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to get update statistics', error);
      throw error;
    }
  }

  /**
   * Check for auto-rollback conditions
   */
  async checkAutoRollback() {
    try {
      const activeUpdates = await OTAUpdate.find({
        isActive: true,
        'rollback.enabled': true,
        'rollback.autoRollback': true
      });

      const rollbackCandidates = [];

      for (const update of activeUpdates) {
        const shouldRollback = await update.shouldAutoRollback();
        if (shouldRollback) {
          rollbackCandidates.push(update);
        }
      }

      return rollbackCandidates;
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to check auto-rollback', error);
      throw error;
    }
  }

  /**
   * Perform rollback
   */
  async performRollback(updateId, adminId, reason = 'Manual rollback') {
    try {
      const update = await OTAUpdate.findOne({ id: updateId });
      if (!update) {
        throw new Error('Update not found');
      }

      // Deactivate the update
      update.isActive = false;
      update.deactivatedAt = new Date();
      update.modifiedBy = adminId;
      update.modifiedAt = new Date();

      await update.save();

      // Log rollback analytics
      await this.storeAnalyticsEvent({
        event: 'rollback_started',
        updateId,
        platform: update.platform,
        metadata: { reason, adminId },
        timestamp: new Date()
      });

      logger.info('🔄 [OTA SERVICE] Rollback performed', {
        updateId,
        adminId,
        reason
      });

      return update;
    } catch (error) {
      logger.error('❌ [OTA SERVICE] Failed to perform rollback', error);
      throw error;
    }
  }
}

module.exports = new OTAService();
