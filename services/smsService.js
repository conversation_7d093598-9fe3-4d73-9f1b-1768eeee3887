const axios = require('axios');
const logger = require('../utils/logger');
const { getSupabase } = require('../config/database');

class SMSService {
  constructor() {
    this.apiKey = process.env.TERMII_API_KEY;
    this.senderId = process.env.TERMII_SENDER_ID || 'talert';
    this.baseURL = process.env.TERMII_BASE_URL || 'https://v3.api.termii.com';

    if (!this.apiKey) {
      throw new Error('Termii API key is required');
    }
  }

  /**
   * Send SMS using Termii API
   * @param {string} phoneNumber - Recipient phone number
   * @param {string} message - SMS message content
   * @param {string} type - Message type (plain, numeric)
   * @returns {Promise<Object>} - API response
   */
  async sendSMS(phoneNumber, message, type = 'plain') {
    try {
      console.log('🚀 [SMS] SendSMS function called');
      console.log('📞 Original phone:', phoneNumber);
      console.log('💬 Message:', message);
      console.log('📋 Type:', type);

      // Format phone number to international format
      const formattedPhone = this.formatPhoneNumber(phoneNumber);
      console.log('📞 Formatted phone:', formattedPhone);

      const payload = {
        to: formattedPhone,
        from: this.senderId,
        sms: message,
        type: type,
        api_key: this.apiKey,
        channel: 'generic'
      };

      console.log('🔧 [SMS] Configuration:');
      console.log('🔑 API Key:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');
      console.log('📤 Sender ID:', this.senderId);
      console.log('🌐 Base URL:', this.baseURL);
      console.log('📦 Full payload:', JSON.stringify(payload, null, 2));

      logger.info(`Sending SMS to ${formattedPhone}`);

      console.log('📡 [SMS] Making API request to Termii...');
      const response = await axios.post(`${this.baseURL}/api/sms/send`, payload, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10 seconds timeout
      });

      console.log('📡 [SMS] Termii API Response:');
      console.log('📊 Status:', response.status);
      console.log('📋 Headers:', JSON.stringify(response.headers, null, 2));
      console.log('📄 Data:', JSON.stringify(response.data, null, 2));

      if (response.data && response.data.message_id) {
        console.log('✅ [SMS] SMS sent successfully!');
        console.log('📧 Message ID:', response.data.message_id);

        logger.info(`SMS sent successfully. Message ID: ${response.data.message_id}`);

        // Store SMS log for tracking
        console.log('💾 [SMS] Logging SMS delivery...');
        await this.logSMSDelivery(formattedPhone, response.data.message_id, 'sent');

        return {
          success: true,
          messageId: response.data.message_id,
          message: 'SMS sent successfully'
        };
      } else {
        console.log('❌ [SMS] Invalid response from Termii');
        console.log('📄 Response data:', JSON.stringify(response.data, null, 2));
        throw new Error('Invalid response from SMS provider');
      }

    } catch (error) {
      console.log('💥 [SMS] Error occurred:');
      console.log('❌ Error message:', error.message);
      console.log('📊 Error status:', error.response?.status);
      console.log('📋 Error headers:', JSON.stringify(error.response?.headers, null, 2));
      console.log('📄 Error data:', JSON.stringify(error.response?.data, null, 2));
      console.log('🔍 Full error:', error);

      logger.error('SMS sending failed:', {
        error: error.message,
        phoneNumber: phoneNumber,
        response: error.response?.data
      });

      // Log failed attempt
      console.log('💾 [SMS] Logging failed attempt...');
      await this.logSMSDelivery(phoneNumber, null, 'failed', error.message);

      return {
        success: false,
        error: error.response?.data?.message || error.message || 'SMS sending failed'
      };
    }
  }

  /**
   * Send OTP SMS
   * @param {string} phoneNumber - Recipient phone number
   * @param {string} otp - OTP code
   * @param {string} purpose - Purpose of OTP (verification, reset, etc.)
   * @returns {Promise<Object>} - API response
   */
  async sendOTP(phoneNumber, otp, purpose = 'verification') {
    console.log('📨 [SMS] SendOTP called');
    console.log('📞 Phone:', phoneNumber);
    console.log('🔢 OTP:', otp);
    console.log('🎯 Purpose:', purpose);

    const messages = {
      verification: `Your Vendy verification code is: ${otp}. This code expires in 5 minutes. Do not share this code with anyone.`,
      reset: `Your Vendy password reset code is: ${otp}. This code expires in 5 minutes. If you didn't request this, please ignore.`,
      transaction: `Your Vendy transaction PIN is: ${otp}. Use this to complete your transaction. Expires in 5 minutes.`
    };

    const message = messages[purpose] || messages.verification;
    console.log('💬 Message:', message);

    // Check rate limiting for OTP
    console.log('⏱️ [SMS] Checking rate limit...');
    const isRateLimited = await this.checkOTPRateLimit(phoneNumber);
    console.log('⏱️ [SMS] Rate limited:', isRateLimited);

    if (isRateLimited) {
      console.log('🚫 [SMS] Rate limit exceeded');
      return {
        success: false,
        error: 'Too many OTP requests. Please wait before requesting another code.'
      };
    }

    console.log('📤 [SMS] Calling sendSMS...');
    const result = await this.sendSMS(phoneNumber, message, 'numeric');
    console.log('📤 [SMS] SendSMS result:', JSON.stringify(result, null, 2));

    return result;
  }

  /**
   * Check SMS delivery status
   * @param {string} messageId - Message ID from Termii
   * @returns {Promise<Object>} - Delivery status
   */
  async checkDeliveryStatus(messageId) {
    try {
      const response = await axios.get(`${this.baseURL}/api/sms/inbox`, {
        params: {
          api_key: this.apiKey,
          message_id: messageId
        },
        timeout: 5000
      });

      return {
        success: true,
        status: response.data.status,
        data: response.data
      };

    } catch (error) {
      logger.error('Failed to check delivery status:', error.message);
      return {
        success: false,
        error: 'Failed to check delivery status'
      };
    }
  }

  /**
   * Get account balance from Termii
   * @returns {Promise<Object>} - Account balance
   */
  async getBalance() {
    try {
      const response = await axios.get(`${this.baseURL}/api/get-balance`, {
        params: {
          api_key: this.apiKey
        },
        timeout: 5000
      });

      return {
        success: true,
        balance: response.data.balance,
        currency: response.data.currency
      };

    } catch (error) {
      logger.error('Failed to get SMS balance:', error.message);
      return {
        success: false,
        error: 'Failed to get account balance'
      };
    }
  }

  /**
   * Format phone number to international format
   * @param {string} phoneNumber - Phone number to format
   * @returns {string} - Formatted phone number
   */
  formatPhoneNumber(phoneNumber) {
    // Remove all non-digits
    let cleaned = phoneNumber.replace(/\D/g, '');

    // Handle Nigerian numbers
    if (cleaned.startsWith('234')) {
      return cleaned; // Already in international format
    } else if (cleaned.startsWith('0')) {
      return '234' + cleaned.substring(1); // Convert from local to international
    } else if (cleaned.length === 10) {
      return '234' + cleaned; // Add country code
    }

    return cleaned;
  }

  /**
   * Check OTP rate limiting using Supabase
   * @param {string} phoneNumber - Phone number to check
   * @returns {Promise<boolean>} - Whether rate limited
   */
  async checkOTPRateLimit(phoneNumber) {
    try {
      const supabase = getSupabase();

      // Check if there's an SMS sent in the last minute
      const oneMinuteAgo = new Date(Date.now() - 60000).toISOString();

      const { data, error } = await supabase
        .from('sms_logs')
        .select('id')
        .eq('phone_number', phoneNumber)
        .gte('created_at', oneMinuteAgo)
        .limit(1);

      if (error) {
        if (error.code === 'PGRST116') {
          // Table doesn't exist - skip rate limiting
          logger.warn('sms_logs table not found - rate limiting disabled');
          return false;
        }
        logger.error('Rate limit check failed:', error.message);
        return false;
      }

      return data && data.length > 0;
    } catch (error) {
      logger.error('Rate limit check failed:', error.message);
      return false;
    }
  }

  /**
   * Log SMS delivery for tracking
   * @param {string} phoneNumber - Phone number
   * @param {string} messageId - Message ID
   * @param {string} status - Delivery status
   * @param {string} error - Error message if any
   * @returns {Promise<void>}
   */
  async logSMSDelivery(phoneNumber, messageId, status, error = null) {
    try {
      const supabase = getSupabase();

      const { error: insertError } = await supabase
        .from('sms_logs')
        .insert([{
          phone_number: phoneNumber,
          provider_message_id: messageId,
          status: status,
          error_message: error,
          message: 'OTP verification code',
          message_type: 'numeric'
        }]);

      if (insertError) {
        if (insertError.code === 'PGRST116') {
          logger.warn('sms_logs table not found - SMS logging disabled');
        } else {
          logger.error('Failed to log SMS delivery:', insertError.message);
        }
      }

    } catch (error) {
      logger.error('Failed to log SMS delivery:', error.message);
    }
  }
}

module.exports = new SMSService();
