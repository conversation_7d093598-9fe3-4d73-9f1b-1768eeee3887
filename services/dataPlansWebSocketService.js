/**
 * Data Plans WebSocket Service
 * 
 * Provides real-time updates for data plans management
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const WebSocket = require('ws');
const logger = require('../utils/logger');

class DataPlansWebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map();
    this.isRunning = false;
  }

  /**
   * Initialize WebSocket server
   */
  initialize(server) {
    try {
      logger.info('🔌 [WEBSOCKET] Initializing Data Plans WebSocket service...');

      this.wss = new WebSocket.Server({ 
        server,
        path: '/ws/data-plans',
        verifyClient: (info) => {
          // Basic origin verification
          const origin = info.origin;
          const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:3001', 
            'http://localhost:3002',
            'https://admin.vendy.com'
          ];
          
          if (process.env.NODE_ENV === 'development') {
            return true; // Allow all origins in development
          }
          
          return allowedOrigins.includes(origin);
        }
      });

      this.wss.on('connection', (ws, req) => {
        this.handleConnection(ws, req);
      });

      this.wss.on('error', (error) => {
        logger.error('❌ [WEBSOCKET] Server error:', error);
      });

      this.isRunning = true;
      logger.info('✅ [WEBSOCKET] Data Plans WebSocket service initialized successfully');
      
    } catch (error) {
      logger.error('❌ [WEBSOCKET] Failed to initialize WebSocket service:', error);
      throw error;
    }
  }

  /**
   * Handle new WebSocket connection
   */
  handleConnection(ws, req) {
    const clientId = this.generateClientId();
    const clientInfo = {
      id: clientId,
      ws: ws,
      type: 'unknown',
      connectedAt: new Date(),
      lastPing: new Date(),
      ip: req.socket.remoteAddress
    };

    this.clients.set(clientId, clientInfo);
    
    logger.info(`🔗 [WEBSOCKET] New client connected: ${clientId} from ${clientInfo.ip}`);

    // Send connection confirmation
    this.sendToClient(clientId, {
      type: 'connection_established',
      clientId: clientId,
      timestamp: new Date().toISOString()
    });

    // Handle messages
    ws.on('message', (data) => {
      this.handleMessage(clientId, data);
    });

    // Handle disconnection
    ws.on('close', (code, reason) => {
      logger.info(`🔌 [WEBSOCKET] Client disconnected: ${clientId} (${code}: ${reason})`);
      this.clients.delete(clientId);
    });

    // Handle errors
    ws.on('error', (error) => {
      logger.error(`❌ [WEBSOCKET] Client error for ${clientId}:`, error);
      this.clients.delete(clientId);
    });

    // Setup ping/pong for connection health
    ws.on('pong', () => {
      if (this.clients.has(clientId)) {
        this.clients.get(clientId).lastPing = new Date();
      }
    });
  }

  /**
   * Handle incoming messages
   */
  handleMessage(clientId, data) {
    try {
      const message = JSON.parse(data.toString());
      const client = this.clients.get(clientId);
      
      if (!client) return;

      logger.debug(`📨 [WEBSOCKET] Message from ${clientId}:`, message.type);

      switch (message.type) {
        case 'set_client_type':
          client.type = message.clientType || 'unknown';
          this.clients.set(clientId, client);
          logger.info(`👤 [WEBSOCKET] Client ${clientId} set type: ${client.type}`);
          break;

        case 'request_current_plans':
          this.sendCurrentPlans(clientId);
          break;

        case 'subscribe_vendor':
          this.subscribeToVendor(clientId, message.vendorId);
          break;

        case 'unsubscribe_vendor':
          this.unsubscribeFromVendor(clientId, message.vendorId);
          break;

        case 'ping':
          this.sendToClient(clientId, { type: 'pong', timestamp: new Date().toISOString() });
          break;

        default:
          logger.warn(`⚠️ [WEBSOCKET] Unknown message type from ${clientId}: ${message.type}`);
      }
    } catch (error) {
      logger.error(`❌ [WEBSOCKET] Failed to handle message from ${clientId}:`, error);
    }
  }

  /**
   * Send current plans to client
   */
  async sendCurrentPlans(clientId) {
    try {
      // This would integrate with your data plan manager
      const message = {
        type: 'current_plans',
        data: {
          message: 'Plans data would be here',
          timestamp: new Date().toISOString()
        }
      };
      
      this.sendToClient(clientId, message);
    } catch (error) {
      logger.error(`❌ [WEBSOCKET] Failed to send current plans to ${clientId}:`, error);
    }
  }

  /**
   * Subscribe client to vendor updates
   */
  subscribeToVendor(clientId, vendorId) {
    const client = this.clients.get(clientId);
    if (!client) return;

    if (!client.subscriptions) {
      client.subscriptions = new Set();
    }
    
    client.subscriptions.add(vendorId);
    this.clients.set(clientId, client);
    
    logger.info(`📡 [WEBSOCKET] Client ${clientId} subscribed to vendor: ${vendorId}`);
  }

  /**
   * Unsubscribe client from vendor updates
   */
  unsubscribeFromVendor(clientId, vendorId) {
    const client = this.clients.get(clientId);
    if (!client || !client.subscriptions) return;

    client.subscriptions.delete(vendorId);
    this.clients.set(clientId, client);
    
    logger.info(`📡 [WEBSOCKET] Client ${clientId} unsubscribed from vendor: ${vendorId}`);
  }

  /**
   * Broadcast plans update to all clients
   */
  broadcastPlansUpdate(vendorId, data) {
    const message = {
      type: 'plans_updated',
      vendor: vendorId,
      data: data,
      timestamp: new Date().toISOString()
    };

    this.broadcast(message, (client) => {
      return client.type === 'admin' || 
             (client.subscriptions && client.subscriptions.has(vendorId));
    });
  }

  /**
   * Broadcast price changes
   */
  broadcastPriceChanges(vendorId, changes) {
    const message = {
      type: 'price_changes',
      vendor: vendorId,
      changes: changes,
      timestamp: new Date().toISOString()
    };

    this.broadcast(message, (client) => {
      return client.type === 'admin';
    });
  }

  /**
   * Broadcast vendor error
   */
  broadcastVendorError(vendorId, error) {
    const message = {
      type: 'vendor_error',
      vendor: vendorId,
      error: error,
      timestamp: new Date().toISOString()
    };

    this.broadcast(message, (client) => {
      return client.type === 'admin';
    });
  }

  /**
   * Send message to specific client
   */
  sendToClient(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      client.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      logger.error(`❌ [WEBSOCKET] Failed to send message to ${clientId}:`, error);
      this.clients.delete(clientId);
      return false;
    }
  }

  /**
   * Broadcast message to all clients matching filter
   */
  broadcast(message, filter = null) {
    let sentCount = 0;
    
    for (const [clientId, client] of this.clients.entries()) {
      if (client.ws.readyState !== WebSocket.OPEN) {
        this.clients.delete(clientId);
        continue;
      }

      if (filter && !filter(client)) {
        continue;
      }

      if (this.sendToClient(clientId, message)) {
        sentCount++;
      }
    }

    logger.debug(`📡 [WEBSOCKET] Broadcasted message to ${sentCount} clients`);
    return sentCount;
  }

  /**
   * Get connected clients info
   */
  getClientsInfo() {
    const clients = [];
    for (const [clientId, client] of this.clients.entries()) {
      clients.push({
        id: clientId,
        type: client.type,
        connectedAt: client.connectedAt,
        lastPing: client.lastPing,
        subscriptions: client.subscriptions ? Array.from(client.subscriptions) : []
      });
    }
    return clients;
  }

  /**
   * Generate unique client ID
   */
  generateClientId() {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Cleanup and shutdown
   */
  shutdown() {
    if (this.wss) {
      logger.info('🔌 [WEBSOCKET] Shutting down WebSocket service...');
      this.wss.close();
      this.isRunning = false;
    }
  }
}

module.exports = new DataPlansWebSocketService();
