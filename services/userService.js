const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

class UserService {
  constructor() {
    this.supabase = null;
    // Simple in-memory cache for user data (5 minute TTL)
    this.userCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    
    // Clean cache every 10 minutes
    setInterval(() => {
      this.cleanExpiredCache();
    }, 10 * 60 * 1000);
  }

  getClient() {
    if (!this.supabase) {
      this.supabase = getSupabase();
    }
    return this.supabase;
  }

  /**
   * Clean expired cache entries
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, entry] of this.userCache.entries()) {
      if (now > entry.expiresAt) {
        this.userCache.delete(key);
      }
    }
  }

  /**
   * Get user from cache
   */
  getUserFromCache(userId) {
    const entry = this.userCache.get(userId);
    if (!entry) return null;
    
    if (Date.now() > entry.expiresAt) {
      this.userCache.delete(userId);
      return null;
    }
    
    return entry.data;
  }

  /**
   * Set user in cache
   */
  setUserInCache(userId, userData) {
    // Limit cache size to prevent memory issues
    if (this.userCache.size >= 1000) {
      const firstKey = this.userCache.keys().next().value;
      if (firstKey) {
        this.userCache.delete(firstKey);
      }
    }
    
    this.userCache.set(userId, {
      data: userData,
      expiresAt: Date.now() + this.cacheTimeout
    });
  }

  /**
   * Clear user from cache
   */
  clearUserFromCache(userId) {
    this.userCache.delete(userId);
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} - Created user
   */
  async createUser(userData) {
    try {
      const supabase = this.getClient();

      const { data, error } = await supabase
        .from('users')
        .insert([{
          phone_number: userData.phoneNumber,
          pin: null, // New users have no PIN initially
          is_phone_verified: false,
          balance: 0,
          is_active: true,
          role: 'user',
          login_attempts: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        throw error;
      }

      return this.formatUser(data);
    } catch (error) {
      logger.error('Create user error:', error);
      throw error;
    }
  }

  /**
   * Find user by phone number
   * @param {string} phoneNumber - Phone number
   * @returns {Promise<Object|null>} - User or null
   */
  async findByPhoneNumber(phoneNumber) {
    try {
      const supabase = this.getClient();

      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          is_whatsapp_blocked,
          whatsapp_block_reason,
          whatsapp_blocked_at,
          whatsapp_blocked_by,
          token_version
        `)
        .eq('phone_number', phoneNumber)
        .single(); // Remove is_active filter to check all users

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data ? this.formatUser(data) : null;
    } catch (error) {
      logger.error('Find user by phone error:', error);
      return null;
    }
  }

  /**
   * Find user by email
   * @param {string} email - Email address
   * @returns {Promise<Object|null>} - User or null
   */
  async findByEmail(email) {
    const maxRetries = 3;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 [USER-SERVICE] Finding user by email (attempt ${attempt}/${maxRetries}):`, email);
        const supabase = this.getClient();

        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('email', email)
          .eq('is_active', true)
          .single();

        if (error && error.code !== 'PGRST116') {
          throw error;
        }

        console.log(`✅ [USER-SERVICE] User found successfully on attempt ${attempt}`);
        return data ? this.formatUser(data) : null;

      } catch (error) {
        lastError = error;
        console.log(`❌ [USER-SERVICE] Attempt ${attempt} failed:`, error.message);

        if (attempt === maxRetries) {
          console.log(`💥 [USER-SERVICE] All ${maxRetries} attempts failed for email:`, email);
          logger.error('Find user by email error after retries:', error);
          break;
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s
        console.log(`⏳ [USER-SERVICE] Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return null;
  }

  /**
   * Find user by Google UID
   * @param {string} googleUid - Google UID
   * @returns {Promise<Object|null>} - User or null
   */
  async findByGoogleUid(googleUid) {
    try {
      const supabase = this.getClient();

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('google_uid', googleUid)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data ? this.formatUser(data) : null;
    } catch (error) {
      logger.error('Find user by Google UID error:', error);
      return null;
    }
  }

  /**
   * Create user with email (for email verification flow)
   * @param {string} email - Email address
   * @returns {Promise<Object>} - Created user
   */
  async createUserWithEmail(email) {
    try {
      const supabase = this.getClient();

      console.log('👤 [USER-SERVICE] Creating user with email:', email);

      console.log('🔐 [USER-SERVICE] Creating email user without PIN');

      const userData = {
        email: email,
        phone_number: null, // Explicitly set to null for email-only users
        pin: null, // New users have no PIN initially
        is_phone_verified: false,
        is_email_verified: false,
        balance: 0,
        is_active: true,
        role: 'user',
        login_attempts: 0,
        ip_addresses: [],
        devices: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📝 [USER-SERVICE] User data prepared:', {
        email: userData.email,
        phone_number: userData.phone_number,
        role: userData.role,
        is_active: userData.is_active
      });

      const { data, error } = await supabase
        .from('users')
        .insert([userData])
        .select()
        .single();

      if (error) {
        console.log('❌ [USER-SERVICE] Database error:', error);
        throw error;
      }

      console.log('✅ [USER-SERVICE] User created successfully:', data.id);
      return this.formatUser(data);
    } catch (error) {
      console.log('💥 [USER-SERVICE] Create user with email error:', error);
      logger.error('Create user with email error:', error);
      throw error;
    }
  }

  /**
   * Create user with Google authentication
   * @param {Object} googleData - Google user data
   * @returns {Promise<Object>} - Created user
   */
  async createUserWithGoogle(googleData) {
    try {
      const supabase = this.getClient();

      console.log('👤 [USER-SERVICE] Creating user with Google:', googleData.email);

      console.log('🔐 [USER-SERVICE] Creating Google user without PIN');

      // Parse name into first and last name
      const nameParts = googleData.name ? googleData.name.split(' ') : [];
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      const userData = {
        email: googleData.email,
        first_name: firstName,
        last_name: lastName,
        picture: googleData.picture,
        phone_number: null,
        pin: null, // New users have no PIN initially
        is_phone_verified: false,
        is_email_verified: googleData.emailVerified || false,
        google_uid: googleData.uid,
        provider_id: googleData.providerId,
        auth_provider: 'google',
        profile_source: 'google', // Track that profile data came from Google
        balance: 0,
        is_active: true,
        role: 'user',
        login_attempts: 0,
        ip_addresses: [],
        devices: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📝 [USER-SERVICE] Google user data prepared:', {
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        google_uid: userData.google_uid,
        is_email_verified: userData.is_email_verified
      });

      const { data, error } = await supabase
        .from('users')
        .insert([userData])
        .select()
        .single();

      if (error) {
        console.log('❌ [USER-SERVICE] Database error:', error);
        throw error;
      }

      console.log('✅ [USER-SERVICE] Google user created successfully:', data.id);
      return this.formatUser(data);
    } catch (error) {
      console.log('💥 [USER-SERVICE] Create user with Google error:', error);
      logger.error('Create user with Google error:', error);
      throw error;
    }
  }

  /**
   * Find user by ID
   * @param {string} userId - User ID
   * @returns {Promise<Object|null>} - User or null
   */
  async findById(userId) {
    try {
      // Check cache first
      const cachedUser = this.getUserFromCache(userId);
      if (cachedUser) {
        console.log(`💾 [USER-SERVICE] Cache hit for user ${userId}`);
        return cachedUser;
      }

      console.log(`🔍 [USER-SERVICE] Cache miss for user ${userId}, fetching from database`);
      const supabase = this.getClient();

      // Add timeout to the database operation
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Find user by ID timeout')), 5000); // 5 second timeout
      });

      const findOperation = async () => {
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .eq('is_active', true)
          .single();

        if (error && error.code !== 'PGRST116') {
          throw error;
        }

        return data;
      };

      // Race between the operation and timeout
      const data = await Promise.race([findOperation(), timeoutPromise]);

      const user = data ? this.formatUser(data) : null;

      // Cache the result if user found
      if (user) {
        this.setUserInCache(userId, user);
        console.log(`💾 [USER-SERVICE] Cached user ${userId}`);
      }

      return user;
    } catch (error) {
      if (error.message === 'Find user by ID timeout') {
        logger.warn(`Find user by ID operation timed out for user ${userId}`);
      } else {
        logger.error('Find user by ID error:', error);
      }
      return null;
    }
  }

  /**
   * Update user
   * @param {string} userId - User ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} - Updated user
   */
  async updateUser(userId, updateData) {
    try {
      const supabase = this.getClient();

      // Note: PIN should already be hashed before calling this method
      // We don't hash it here to avoid double hashing

      updateData.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      const updatedUser = this.formatUser(data);
      
      // Clear cache and update with new data
      this.clearUserFromCache(userId);
      this.setUserInCache(userId, updatedUser);
      console.log(`🔄 [USER-SERVICE] Updated cache for user ${userId}`);

      return updatedUser;
    } catch (error) {
      logger.error('Update user error:', error);
      throw error;
    }
  }

  /**
   * Hash PIN
   * @param {string} pin - PIN to hash
   * @returns {Promise<string>} - Hashed PIN
   */
  async hashPin(pin) {
    if (!pin) throw new Error('PIN is required');
    const saltRounds = 12;
    return await bcrypt.hash(pin, saltRounds);
  }

  /**
   * Compare PIN
   * @param {string} candidatePin - PIN to compare
   * @param {string} hashedPin - Hashed PIN from database
   * @returns {Promise<boolean>} - Whether PIN matches
   */
  async comparePin(candidatePin, hashedPin) {
    if (!candidatePin || !hashedPin) return false;
    return await bcrypt.compare(candidatePin, hashedPin);
  }

  /**
   * Update user's PIN
   * @param {string} userId - User ID
   * @param {string} newPin - New PIN to set
   * @returns {Promise<boolean>} - Success status
   */
  async updatePin(userId, newPin) {
    try {
      console.log('🔐 [USER-SERVICE] Updating PIN for user:', userId);

      if (!newPin || newPin.length !== 4 || !/^\d{4}$/.test(newPin)) {
        throw new Error('PIN must be a 4-digit number');
      }

      const supabase = this.getClient();

      // Hash the new PIN
      const hashedPin = await this.hashPin(newPin);
      console.log('🔐 [USER-SERVICE] PIN hashed successfully');

      // Update PIN in database
      const { error } = await supabase
        .from('users')
        .update({
          pin: hashedPin,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.log('❌ [USER-SERVICE] Database error updating PIN:', error);
        throw error;
      }

      // Clear user from cache to force refresh
      this.clearUserFromCache(userId);

      console.log('✅ [USER-SERVICE] PIN updated successfully');
      return true;
    } catch (error) {
      console.log('💥 [USER-SERVICE] Update PIN error:', error);
      logger.error('Update PIN error:', error);
      return false;
    }
  }

  /**
   * Increment login attempts
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async incLoginAttempts(userId) {
    try {
      const supabase = this.getClient();
      const user = await this.findById(userId);

      if (!user) return;

      const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5;
      const lockTime = parseInt(process.env.LOCKOUT_TIME) || 15 * 60 * 1000; // 15 minutes

      let updateData = {
        login_attempts: user.loginAttempts + 1,
        updated_at: new Date().toISOString()
      };

      // If max attempts reached, lock the account
      if (user.loginAttempts + 1 >= maxAttempts) {
        updateData.lock_until = new Date(Date.now() + lockTime).toISOString();
        console.log(`🔒 [USER-SERVICE] Account will be locked after ${user.loginAttempts + 1} attempts`);
      }

      console.log(`📊 [USER-SERVICE] Incrementing login attempts for user ${userId}: ${user.loginAttempts} -> ${user.loginAttempts + 1}`);

      const { error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId);

      if (error) {
        throw error;
      }

      // Clear cache to ensure fresh data on next fetch
      this.clearUserFromCache(userId);
      console.log(`🗑️ [USER-SERVICE] Cleared cache for user ${userId} after incrementing attempts`);

    } catch (error) {
      logger.error('Increment login attempts error:', error);
    }
  }

  /**
   * Reset login attempts
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async resetLoginAttempts(userId) {
    try {
      const supabase = this.getClient();

      console.log(`🔄 [USER-SERVICE] Resetting login attempts for user ${userId}`);

      const { error } = await supabase
        .from('users')
        .update({
          login_attempts: 0,
          lock_until: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      // Clear cache to ensure fresh data on next fetch
      this.clearUserFromCache(userId);
      console.log(`🗑️ [USER-SERVICE] Cleared cache for user ${userId} after resetting attempts`);

    } catch (error) {
      logger.error('Reset login attempts error:', error);
    }
  }

  /**
   * Clear expired account locks
   * @returns {Promise<number>} - Number of accounts unlocked
   */
  async clearExpiredLocks() {
    try {
      const supabase = this.getClient();
      const now = new Date().toISOString();

      // Add timeout to the database operation
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Clear expired locks timeout')), 5000); // 5 second timeout
      });

      const clearOperation = async () => {
        // Find and update accounts where lock_until has passed
        const { data, error } = await supabase
          .from('users')
          .update({
            login_attempts: 0,
            lock_until: null,
            updated_at: now
          })
          .lt('lock_until', now)
          .not('lock_until', 'is', null)
          .select('id');

        if (error) {
          throw error;
        }

        return data;
      };

      // Race between the operation and timeout
      const data = await Promise.race([clearOperation(), timeoutPromise]);

      const unlockedCount = data?.length || 0;
      if (unlockedCount > 0) {
        logger.info(`Cleared expired locks for ${unlockedCount} accounts`);
      }

      return unlockedCount;
    } catch (error) {
      if (error.message === 'Clear expired locks timeout') {
        logger.warn('Clear expired locks operation timed out');
      } else {
        logger.error('Clear expired locks error:', error);
      }
      return 0;
    }
  }

  /**
   * Manually unlock a user account (admin function)
   * @param {string} userId - User ID
   * @param {string} adminId - Admin user ID performing the unlock
   * @returns {Promise<boolean>} - Success status
   */
  async unlockAccount(userId, adminId = null) {
    try {
      const supabase = this.getClient();

      // Verify user exists
      const user = await this.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Check if account is actually locked
      const isLocked = user.lockUntil && new Date(user.lockUntil) > new Date();
      if (!isLocked) {
        logger.info('Account unlock requested but account is not locked', { userId });
        return true; // Already unlocked
      }

      // Unlock the account
      const { error } = await supabase
        .from('users')
        .update({
          login_attempts: 0,
          lock_until: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      // Clear cache to ensure fresh data on next fetch
      this.clearUserFromCache(userId);

      logger.info('Account manually unlocked', {
        userId,
        adminId: adminId || 'system',
        previousLockUntil: user.lockUntil
      });

      return true;
    } catch (error) {
      logger.error('Manual unlock account error:', error);
      return false;
    }
  }

  /**
   * Get account lock status
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Lock status information
   */
  async getAccountLockStatus(userId) {
    try {
      const user = await this.findById(userId);
      if (!user) {
        return { exists: false };
      }

      const isLocked = user.lockUntil && new Date(user.lockUntil) > new Date();
      const lockTimeRemaining = isLocked ? new Date(user.lockUntil) - new Date() : 0;
      const minutesRemaining = isLocked ? Math.ceil(lockTimeRemaining / (60 * 1000)) : 0;

      return {
        exists: true,
        isLocked,
        lockUntil: user.lockUntil,
        lockTimeRemaining,
        minutesRemaining,
        loginAttempts: user.loginAttempts || 0,
        maxAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS) || 5
      };
    } catch (error) {
      logger.error('Get account lock status error:', error);
      return { exists: false, error: error.message };
    }
  }

  /**
   * Create phone verification token
   * @param {string} userId - User ID
   * @returns {Promise<string>} - OTP code
   */
  async createPhoneVerificationToken(userId) {
    try {
      // Generate 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      // Hash the OTP before storing
      const hashedOTP = crypto.createHash('sha256').update(otp).digest('hex');

      // Set expiry time (5 minutes)
      const expiryTime = parseInt(process.env.OTP_EXPIRY_TIME) || 5 * 60 * 1000;
      const expiresAt = new Date(Date.now() + expiryTime).toISOString();

      const supabase = this.getClient();
      await supabase
        .from('users')
        .update({
          phone_verification_token: hashedOTP,
          phone_verification_expires: expiresAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      return otp; // Return unhashed OTP for SMS
    } catch (error) {
      logger.error('Create phone verification token error:', error);
      throw error;
    }
  }

  /**
   * Verify phone verification token
   * @param {string} userId - User ID
   * @param {string} otp - OTP to verify
   * @returns {Promise<boolean>} - Whether OTP is valid
   */
  async verifyPhoneToken(userId, otp) {
    try {
      const user = await this.findById(userId);
      if (!user || !user.phoneVerificationToken || !user.phoneVerificationExpires) {
        return false;
      }

      // Check if token has expired
      if (new Date(user.phoneVerificationExpires) < new Date()) {
        return false;
      }

      // Hash the provided OTP and compare
      const hashedOTP = crypto.createHash('sha256').update(otp).digest('hex');

      if (hashedOTP !== user.phoneVerificationToken) {
        return false;
      }

      // Mark phone as verified and clear verification token
      await this.updateUser(userId, {
        is_phone_verified: true,
        phone_verification_token: null,
        phone_verification_expires: null,
        last_login: new Date().toISOString()
      });

      return true;
    } catch (error) {
      logger.error('Verify phone token error:', error);
      return false;
    }
  }

  /**
   * Create email verification token
   * @param {string} userId - User ID
   * @returns {Promise<string>} - OTP code
   */
  async createEmailVerificationToken(userId) {
    try {
      // Generate 6-digit OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      console.log('🔢 [USER-SERVICE] Generated OTP:', otp);

      // Hash the OTP before storing
      const hashedOTP = crypto.createHash('sha256').update(otp).digest('hex');
      console.log('🔐 [USER-SERVICE] Hashed OTP:', hashedOTP);

      // Set expiry time (5 minutes)
      const expiryTime = parseInt(process.env.OTP_EXPIRY_TIME) || 5 * 60 * 1000;
      const expiresAt = new Date(Date.now() + expiryTime).toISOString();
      console.log('⏰ [USER-SERVICE] OTP expires at:', expiresAt);

      const supabase = this.getClient();
      const { error } = await supabase
        .from('users')
        .update({
          email_verification_token: hashedOTP,
          email_verification_expires: expiresAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.log('❌ [USER-SERVICE] Error storing OTP:', error);
        throw error;
      }

      console.log('✅ [USER-SERVICE] OTP stored successfully for user:', userId);

      // Clear user from cache to ensure fresh data on next fetch
      this.clearUserFromCache(userId);

      return otp; // Return unhashed OTP for email
    } catch (error) {
      console.log('💥 [USER-SERVICE] Create email verification token error:', error);
      logger.error('Create email verification token error:', error);
      throw error;
    }
  }

  /**
   * Verify email OTP
   * @param {string} userId - User ID
   * @param {string} otp - OTP to verify
   * @returns {Promise<boolean>} - Whether OTP is valid
   */
  async verifyEmailOTP(userId, otp) {
    try {
      console.log('🔍 [USER-SERVICE] Verifying email OTP for user:', userId);
      console.log('🔢 [USER-SERVICE] Provided OTP:', otp);

      const user = await this.findById(userId);
      if (!user) {
        console.log('❌ [USER-SERVICE] User not found');
        return false;
      }

      console.log('👤 [USER-SERVICE] User found, checking OTP data...');
      console.log('🔑 [USER-SERVICE] Stored token exists:', !!user.emailVerificationToken);
      console.log('⏰ [USER-SERVICE] Expiry exists:', !!user.emailVerificationExpires);

      if (!user.emailVerificationToken || !user.emailVerificationExpires) {
        console.log('❌ [USER-SERVICE] Missing OTP token or expiry');
        return false;
      }

      // Check if token has expired
      const now = new Date();
      const expiryTime = new Date(user.emailVerificationExpires);
      console.log('⏰ [USER-SERVICE] Current time:', now.toISOString());
      console.log('⏰ [USER-SERVICE] Expiry time:', expiryTime.toISOString());
      console.log('⏰ [USER-SERVICE] Is expired:', expiryTime < now);

      if (expiryTime < now) {
        console.log('❌ [USER-SERVICE] OTP has expired');
        return false;
      }

      // Hash the provided OTP and compare
      const hashedOTP = crypto.createHash('sha256').update(otp).digest('hex');
      console.log('🔐 [USER-SERVICE] Provided OTP hash:', hashedOTP);
      console.log('🔐 [USER-SERVICE] Stored OTP hash:', user.emailVerificationToken);
      console.log('✅ [USER-SERVICE] Hashes match:', hashedOTP === user.emailVerificationToken);

      if (hashedOTP !== user.emailVerificationToken) {
        console.log('❌ [USER-SERVICE] OTP hash mismatch');
        return false;
      }

      console.log('✅ [USER-SERVICE] OTP verified successfully, clearing token...');

      // Clear verification token (but don't mark as verified yet - that's done separately)
      await this.updateUser(userId, {
        email_verification_token: null,
        email_verification_expires: null,
        last_login: new Date().toISOString()
      });

      return true;
    } catch (error) {
      console.log('💥 [USER-SERVICE] Verify email OTP error:', error);
      logger.error('Verify email OTP error:', error);
      return false;
    }
  }

  /**
   * Mark email as verified
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async markEmailAsVerified(userId) {
    try {
      await this.updateUser(userId, {
        is_email_verified: true
      });
    } catch (error) {
      logger.error('Mark email as verified error:', error);
      throw error;
    }
  }

  /**
   * Increment login attempts (alias for backward compatibility)
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async incrementLoginAttempts(userId) {
    return this.incLoginAttempts(userId);
  }

  /**
   * Add IP address to user's history
   * @param {string} userId - User ID
   * @param {string} ip - IP address
   * @returns {Promise<void>}
   */
  async addIpAddress(userId, ip) {
    try {
      const supabase = this.getClient();

      // Get current IP addresses
      const user = await this.findById(userId);
      let ipAddresses = user.ipAddresses || [];

      // Keep only last 10 IP addresses
      if (ipAddresses.length >= 10) {
        ipAddresses = ipAddresses.slice(-9);
      }

      ipAddresses.push({
        ip: ip,
        timestamp: new Date().toISOString()
      });

      await supabase
        .from('users')
        .update({
          ip_addresses: ipAddresses,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

    } catch (error) {
      logger.error('Add IP address error:', error);
    }
  }

  /**
   * Add device to user's history
   * @param {string} userId - User ID
   * @param {Object} deviceInfo - Device information
   * @returns {Promise<void>}
   */
  async addDevice(userId, deviceInfo) {
    try {
      const supabase = this.getClient();

      // Get current devices
      const user = await this.findById(userId);
      let devices = user.devices || [];

      // Create device entry
      const deviceEntry = {
        ...deviceInfo,
        timestamp: new Date().toISOString(),
        id: crypto.randomUUID()
      };

      // Check if device already exists (by device ID or unique identifier)
      const existingDeviceIndex = devices.findIndex(device => 
        device.deviceId === deviceInfo.deviceId || 
        device.id === deviceInfo.id ||
        (device.platform === deviceInfo.platform && 
         device.model === deviceInfo.model && 
         device.brand === deviceInfo.brand)
      );

      if (existingDeviceIndex !== -1) {
        // Update existing device
        devices[existingDeviceIndex] = {
          ...devices[existingDeviceIndex],
          ...deviceEntry,
          lastSeen: new Date().toISOString()
        };
      } else {
        // Keep only last 5 devices
        if (devices.length >= 5) {
          devices = devices.slice(-4);
        }
        devices.push(deviceEntry);
      }

      await supabase
        .from('users')
        .update({
          devices: devices,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

    } catch (error) {
      logger.error('Add device error:', error);
    }
  }

  /**
   * Format user data for API response
   * @param {Object} userData - Raw user data from database
   * @returns {Object} - Formatted user data
   */
  formatUser(userData) {
    if (!userData) return null;

    return {
      id: userData.id,
      phoneNumber: userData.phone_number,
      firstName: userData.first_name,
      lastName: userData.last_name,
      email: userData.email,
      picture: userData.picture || userData.avatar,
      avatar: userData.avatar || userData.picture,
      dateOfBirth: userData.date_of_birth,
      isPhoneVerified: userData.is_phone_verified,
      isEmailVerified: userData.is_email_verified,
      balance: userData.balance,
      isActive: userData.is_active,
      role: userData.role,
      loginAttempts: userData.login_attempts,
      lockUntil: userData.lock_until,
      lastLogin: userData.last_login,
      phoneVerificationToken: userData.phone_verification_token,
      phoneVerificationExpires: userData.phone_verification_expires,
      emailVerificationToken: userData.email_verification_token,
      emailVerificationExpires: userData.email_verification_expires,
      googleUid: userData.google_uid,
      providerId: userData.provider_id,
      authProvider: userData.auth_provider,
      profileSource: userData.profile_source || 'manual',
      ipAddresses: userData.ip_addresses || [],
      devices: userData.devices || [],
      // Setup related fields
      biometricEnabled: userData.biometric_enabled || false,
      biometricType: userData.biometric_type,
      biometricDeviceInfo: userData.biometric_device_info,
      setupCompleted: userData.setup_completed || false,
      pinSetupAt: userData.pin_setup_at,
      biometricSetupAt: userData.biometric_setup_at,
      profileSetupAt: userData.profile_setup_at,
      setupCompletedAt: userData.setup_completed_at,
      createdAt: userData.created_at,
      updatedAt: userData.updated_at,
      // WhatsApp related fields
      isWhatsappBlocked: userData.is_whatsapp_blocked || false,
      whatsappBlockReason: userData.whatsapp_block_reason,
      whatsappBlockedAt: userData.whatsapp_blocked_at,
      whatsappBlockedBy: userData.whatsapp_blocked_by,
      whatsappUnblockedAt: userData.whatsapp_unblocked_at,
      whatsappUnblockedBy: userData.whatsapp_unblocked_by,
      tokenVersion: userData.token_version || 0,
      // Virtual properties
      isLocked: userData.lock_until && new Date(userData.lock_until) > new Date(),
      isBlocked: userData.is_blocked || false,
      requiresAdditionalVerification: userData.requires_additional_verification || false,
      verificationType: userData.verification_type,
      flaggedForReview: userData.flagged_for_review || false,
      pin: userData.pin // Only include when specifically needed
    };
  }
}

module.exports = new UserService();
