/**
 * International Airtime Service Layer
 * 
 * Comprehensive service for VTpass International Airtime/Data/Pin API integration
 * with enhanced security, country/operator management, and robust error handling.
 * 
 * Features:
 * - Country and operator management
 * - Variation code handling
 * - Enhanced security for international transactions
 * - Comprehensive error handling and logging
 * - Transaction state management
 * - Currency conversion and validation
 * - Compliance with international regulations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const axios = require('axios');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const vtpassConfig = require('../config/vtpass');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

/**
 * International Airtime Service Class
 * Handles all international airtime operations with enhanced security
 */
class InternationalAirtimeService {
  constructor() {
    this.config = vtpassConfig.getRawConfig();
    this.endpoints = vtpassConfig.getEndpoints();
    this.supabase = getSupabase();
    
    // International-specific endpoints
    this.internationalEndpoints = {
      countries: `${this.config.baseUrl}/get-international-airtime-countries`,
      productTypes: `${this.config.baseUrl}/get-international-airtime-product-types`,
      operators: `${this.config.baseUrl}/get-international-airtime-operators`,
      variations: `${this.config.baseUrl}/service-variations`,
      purchase: `${this.config.baseUrl}/pay`,
      query: `${this.config.baseUrl}/requery`
    };
    
    // Initialize axios instance with enhanced configuration
    this.apiClient = axios.create({
      timeout: this.config.timeout * 1.5, // Longer timeout for international
      headers: vtpassConfig.getAuthHeaders()
    });
    
    // Setup interceptors
    this.setupInterceptors();
    
    // Cache for countries, operators, and variations
    this.cache = {
      countries: { data: null, expiry: null },
      operators: new Map(),
      variations: new Map(),
      productTypes: new Map()
    };
    
    logger.info('✅ [INTERNATIONAL_AIRTIME_SERVICE] Service initialized successfully');
  }

  /**
   * Setup axios interceptors for enhanced logging
   */
  setupInterceptors() {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        const requestId = uuidv4();
        config.metadata = { requestId, startTime: Date.now() };
        
        logger.info('🌍 [INTERNATIONAL_AIRTIME_SERVICE] API Request:', {
          requestId,
          method: config.method?.toUpperCase(),
          url: config.url,
          timeout: config.timeout
        });
        
        return config;
      },
      (error) => {
        logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response) => {
        const { requestId, startTime } = response.config.metadata || {};
        const duration = Date.now() - startTime;
        
        logger.info('✅ [INTERNATIONAL_AIRTIME_SERVICE] API Response:', {
          requestId,
          status: response.status,
          duration: `${duration}ms`,
          dataSize: JSON.stringify(response.data).length
        });
        
        return response;
      },
      (error) => {
        const { requestId, startTime } = error.config?.metadata || {};
        const duration = startTime ? Date.now() - startTime : 0;
        
        logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] API Error:', {
          requestId,
          status: error.response?.status,
          duration: `${duration}ms`,
          message: error.message,
          data: error.response?.data
        });
        
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get all available countries for international airtime
   * 
   * @param {boolean} useCache - Whether to use cached data
   * @returns {Array} List of countries
   */
  async getCountries(useCache = true) {
    try {
      // Check cache first
      if (useCache && this.cache.countries.data && this.cache.countries.expiry > Date.now()) {
        logger.debug('📋 [INTERNATIONAL_AIRTIME_SERVICE] Returning cached countries');
        return this.cache.countries.data;
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_SERVICE] Fetching countries from VTpass');
      
      const response = await this.apiClient.get(this.internationalEndpoints.countries);
      
      if (response.data.response_description === '000' && response.data.content?.countries) {
        const countries = response.data.content.countries;
        
        // Cache for 1 hour
        this.cache.countries = {
          data: countries,
          expiry: Date.now() + (60 * 60 * 1000)
        };
        
        logger.info('✅ [INTERNATIONAL_AIRTIME_SERVICE] Countries fetched successfully:', {
          count: countries.length
        });
        
        return countries;
      } else {
        throw new Error('Invalid response from VTpass countries API');
      }
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Get countries error:', error);
      throw error;
    }
  }

  /**
   * Get product types for a specific country
   * 
   * @param {string} countryCode - Country code
   * @param {boolean} useCache - Whether to use cached data
   * @returns {Array} List of product types
   */
  async getProductTypes(countryCode, useCache = true) {
    try {
      const cacheKey = `productTypes:${countryCode}`;
      
      // Check cache first
      if (useCache && this.cache.productTypes.has(cacheKey)) {
        const cached = this.cache.productTypes.get(cacheKey);
        if (cached.expiry > Date.now()) {
          logger.debug('📋 [INTERNATIONAL_AIRTIME_SERVICE] Returning cached product types');
          return cached.data;
        }
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_SERVICE] Fetching product types:', { countryCode });
      
      const response = await this.apiClient.get(this.internationalEndpoints.productTypes, {
        params: { code: countryCode }
      });
      
      if (response.data.response_description === '000' && response.data.content) {
        const productTypes = response.data.content;
        
        // Cache for 1 hour
        this.cache.productTypes.set(cacheKey, {
          data: productTypes,
          expiry: Date.now() + (60 * 60 * 1000)
        });
        
        logger.info('✅ [INTERNATIONAL_AIRTIME_SERVICE] Product types fetched:', {
          countryCode,
          count: productTypes.length
        });
        
        return productTypes;
      } else {
        throw new Error(`Invalid response from VTpass product types API for ${countryCode}`);
      }
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Get product types error:', error);
      throw error;
    }
  }

  /**
   * Get operators for a specific country and product type
   * 
   * @param {string} countryCode - Country code
   * @param {string} productTypeId - Product type ID
   * @param {boolean} useCache - Whether to use cached data
   * @returns {Array} List of operators
   */
  async getOperators(countryCode, productTypeId, useCache = true) {
    try {
      const cacheKey = `operators:${countryCode}:${productTypeId}`;
      
      // Check cache first
      if (useCache && this.cache.operators.has(cacheKey)) {
        const cached = this.cache.operators.get(cacheKey);
        if (cached.expiry > Date.now()) {
          logger.debug('📋 [INTERNATIONAL_AIRTIME_SERVICE] Returning cached operators');
          return cached.data;
        }
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_SERVICE] Fetching operators:', {
        countryCode,
        productTypeId
      });
      
      const response = await this.apiClient.get(this.internationalEndpoints.operators, {
        params: {
          code: countryCode,
          product_type_id: productTypeId
        }
      });
      
      if (response.data.response_description === '000' && response.data.content) {
        const operators = response.data.content;
        
        // Cache for 2 hours
        this.cache.operators.set(cacheKey, {
          data: operators,
          expiry: Date.now() + (2 * 60 * 60 * 1000)
        });
        
        logger.info('✅ [INTERNATIONAL_AIRTIME_SERVICE] Operators fetched:', {
          countryCode,
          productTypeId,
          count: operators.length
        });
        
        return operators;
      } else {
        throw new Error(`Invalid response from VTpass operators API for ${countryCode}:${productTypeId}`);
      }
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Get operators error:', error);
      throw error;
    }
  }

  /**
   * Get variation codes for a specific operator and product type
   * 
   * @param {string} operatorId - Operator ID
   * @param {string} productTypeId - Product type ID
   * @param {boolean} useCache - Whether to use cached data
   * @returns {Object} Variations data
   */
  async getVariations(operatorId, productTypeId, useCache = true) {
    try {
      const cacheKey = `variations:${operatorId}:${productTypeId}`;
      
      // Check cache first
      if (useCache && this.cache.variations.has(cacheKey)) {
        const cached = this.cache.variations.get(cacheKey);
        if (cached.expiry > Date.now()) {
          logger.debug('📋 [INTERNATIONAL_AIRTIME_SERVICE] Returning cached variations');
          return cached.data;
        }
      }

      logger.info('🌍 [INTERNATIONAL_AIRTIME_SERVICE] Fetching variations:', {
        operatorId,
        productTypeId
      });
      
      const response = await this.apiClient.get(this.internationalEndpoints.variations, {
        params: {
          serviceID: 'foreign-airtime',
          operator_id: operatorId,
          product_type_id: productTypeId
        }
      });
      
      if (response.data.response_description === '000' && response.data.content) {
        const variations = response.data.content;
        
        // Cache for 4 hours
        this.cache.variations.set(cacheKey, {
          data: variations,
          expiry: Date.now() + (4 * 60 * 60 * 1000)
        });
        
        logger.info('✅ [INTERNATIONAL_AIRTIME_SERVICE] Variations fetched:', {
          operatorId,
          productTypeId,
          count: variations.variations?.length || 0
        });
        
        return variations;
      } else {
        throw new Error(`Invalid response from VTpass variations API for operator ${operatorId}`);
      }
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Get variations error:', error);
      throw error;
    }
  }

  /**
   * Generate unique request ID for international transactions
   * 
   * @returns {string} Unique request ID
   */
  generateRequestId() {
    const now = new Date();
    const timestamp = now.toISOString()
      .replace(/[-:T]/g, '')
      .substring(0, 14); // YYYYMMDDHHMMSS
    
    const random = Math.floor(Math.random() * 100000000)
      .toString()
      .padStart(8, '0');
    
    return `INTL${timestamp}${random}`;
  }

  /**
   * Validate international transaction data
   * 
   * @param {Object} transactionData - Transaction data to validate
   * @returns {Object} Validation result
   */
  validateTransactionData(transactionData) {
    const {
      billersCode,
      variation_code,
      operator_id,
      country_code,
      product_type_id,
      email,
      phone
    } = transactionData;

    const errors = [];

    if (!billersCode) errors.push('Billers code (recipient phone) is required');
    if (!variation_code) errors.push('Variation code is required');
    if (!operator_id) errors.push('Operator ID is required');
    if (!country_code) errors.push('Country code is required');
    if (!product_type_id) errors.push('Product type ID is required');
    if (!email) errors.push('Email is required');
    if (!phone) errors.push('Customer phone is required');

    // Validate email format
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.push('Invalid email format');
    }

    // Validate country code format
    if (country_code && !/^[A-Z]{2}$/.test(country_code)) {
      errors.push('Country code must be 2 uppercase letters');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Create international transaction record in database
   *
   * @param {Object} transactionData - Transaction data
   * @returns {Object} Created transaction record
   */
  async createInternationalTransaction(transactionData) {
    try {
      const transaction = {
        id: uuidv4(),
        user_id: transactionData.userId,
        type: 'international_airtime',
        amount: transactionData.amount,
        recipient: transactionData.billersCode,
        provider: transactionData.operatorName || 'International Provider',
        status: 'pending',
        reference: transactionData.requestId,
        external_reference: null,
        description: `International ${transactionData.productTypeName || 'Airtime'} to ${transactionData.billersCode} (${transactionData.countryName})`,
        provider_type: 'vtpass',
        ip_address: transactionData.ipAddress,
        user_agent: transactionData.userAgent || 'PayVendy-API',
        metadata: {
          service_id: 'foreign-airtime',
          country_code: transactionData.country_code,
          country_name: transactionData.countryName,
          operator_id: transactionData.operator_id,
          operator_name: transactionData.operatorName,
          product_type_id: transactionData.product_type_id,
          product_type_name: transactionData.productTypeName,
          variation_code: transactionData.variation_code,
          variation_name: transactionData.variationName,
          customer_email: transactionData.email,
          customer_phone: transactionData.phone,
          provider: 'vtpass',
          request_timestamp: new Date().toISOString(),
          user_agent: transactionData.userAgent || 'PayVendy-API',
          ip_address: transactionData.ipAddress,
          international_risk_score: transactionData.riskScore || 0,
          international_risk_factors: transactionData.riskFactors || []
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await this.supabase
        .from('transactions')
        .insert([transaction])
        .select()
        .single();

      if (error) {
        logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Transaction creation failed:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      logger.info('✅ [INTERNATIONAL_AIRTIME_SERVICE] International transaction created:', {
        transactionId: data.id,
        reference: data.reference,
        amount: data.amount,
        recipient: data.recipient,
        country: transactionData.country_code
      });

      return data;
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Create transaction error:', error);
      throw error;
    }
  }

  /**
   * Purchase international airtime/data/pin
   *
   * @param {Object} purchaseData - Purchase request data
   * @returns {Object} Purchase result
   */
  async purchaseInternationalAirtime(purchaseData) {
    const requestId = this.generateRequestId();

    logger.info('🌍 [INTERNATIONAL_AIRTIME_SERVICE] Starting international purchase:', {
      requestId,
      userId: purchaseData.userId,
      country: purchaseData.country_code,
      operator: purchaseData.operator_id,
      amount: purchaseData.amount,
      recipient: purchaseData.billersCode?.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3')
    });

    // Validate transaction data
    const validation = this.validateTransactionData(purchaseData);
    if (!validation.valid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Create transaction record
    const transaction = await this.createInternationalTransaction({
      ...purchaseData,
      requestId
    });

    try {
      // Prepare VTpass API payload
      const payload = {
        request_id: requestId,
        serviceID: 'foreign-airtime',
        billersCode: purchaseData.billersCode,
        variation_code: purchaseData.variation_code,
        amount: purchaseData.amount || 0, // Amount might be ignored for fixed price variations
        phone: purchaseData.phone,
        operator_id: purchaseData.operator_id,
        country_code: purchaseData.country_code,
        product_type_id: purchaseData.product_type_id,
        email: purchaseData.email
      };

      logger.info('📤 [INTERNATIONAL_AIRTIME_SERVICE] Sending purchase request:', {
        requestId,
        serviceID: payload.serviceID,
        country: payload.country_code,
        operator: payload.operator_id,
        variation: payload.variation_code,
        recipient: payload.billersCode?.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3')
      });

      // Make API request to VTpass
      const response = await this.apiClient.post(this.internationalEndpoints.purchase, payload);

      // Process VTpass response
      const result = await this.processInternationalPurchaseResponse(transaction.id, response.data);

      logger.info('✅ [INTERNATIONAL_AIRTIME_SERVICE] International purchase completed:', {
        requestId,
        transactionId: transaction.id,
        status: result.status,
        vtpassTransactionId: result.vtpassTransactionId,
        country: purchaseData.country_code
      });

      return result;
    } catch (error) {
      // Update transaction status to failed
      await this.updateInternationalTransaction(transaction.id, {
        status: 'failed',
        metadata: {
          ...transaction.metadata,
          error_message: error.message,
          error_timestamp: new Date().toISOString(),
          failure_reason: this.categorizeInternationalError(error)
        }
      });

      logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] International purchase failed:', {
        requestId,
        transactionId: transaction.id,
        country: purchaseData.country_code,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Process VTpass international purchase response
   *
   * @param {string} transactionId - Internal transaction ID
   * @param {Object} response - VTpass API response
   * @returns {Object} Processed result
   */
  async processInternationalPurchaseResponse(transactionId, response) {
    try {
      const { code, content, response_description, requestId, transaction_date } = response;

      // Determine transaction status
      let status = 'failed';
      let vtpassTransactionId = null;

      if (code === '000' && content?.transactions) {
        const transaction = content.transactions;
        vtpassTransactionId = transaction.transactionId;

        // Map VTpass status to our status
        switch (transaction.status) {
          case 'delivered':
            status = 'completed';
            break;
          case 'pending':
            status = 'pending';
            break;
          case 'failed':
          default:
            status = 'failed';
            break;
        }
      }

      // Update transaction in database
      const updatedTransaction = await this.updateInternationalTransaction(transactionId, {
        status,
        external_reference: vtpassTransactionId,
        metadata: {
          vtpass_response: response,
          vtpass_code: code,
          vtpass_description: response_description,
          vtpass_transaction_date: transaction_date,
          country_name: response.CountryName,
          country_code: response.CountryCode,
          operator_amount: response.OperatorAmount,
          processed_at: new Date().toISOString()
        }
      });

      return {
        success: status === 'completed',
        status,
        transactionId,
        vtpassTransactionId,
        requestId,
        message: response_description,
        transaction: updatedTransaction,
        countryInfo: {
          name: response.CountryName,
          code: response.CountryCode,
          operatorAmount: response.OperatorAmount
        }
      };
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Response processing failed:', error);
      throw new Error('Failed to process international purchase response');
    }
  }

  /**
   * Update international transaction
   *
   * @param {string} transactionId - Transaction ID
   * @param {Object} updates - Updates to apply
   * @returns {Object} Updated transaction
   */
  async updateInternationalTransaction(transactionId, updates) {
    try {
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await this.supabase
        .from('transactions')
        .update(updateData)
        .eq('id', transactionId)
        .select()
        .single();

      if (error) {
        logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Transaction update failed:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      return data;
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] Update transaction error:', error);
      throw error;
    }
  }

  /**
   * Query international transaction status
   *
   * @param {string} requestId - Original request ID
   * @returns {Object} Transaction status result
   */
  async queryInternationalTransactionStatus(requestId) {
    logger.info('🔍 [INTERNATIONAL_AIRTIME_SERVICE] Querying international transaction status:', { requestId });

    try {
      const payload = { request_id: requestId };

      const response = await this.apiClient.post(this.internationalEndpoints.query, payload);

      logger.info('✅ [INTERNATIONAL_AIRTIME_SERVICE] International status query completed:', {
        requestId,
        code: response.data.code,
        status: response.data.content?.transactions?.status
      });

      return {
        success: response.data.code === '000',
        requestId,
        vtpassResponse: response.data,
        status: response.data.content?.transactions?.status,
        transactionId: response.data.content?.transactions?.transactionId,
        message: response.data.response_description,
        countryInfo: {
          name: response.data.CountryName,
          code: response.data.CountryCode,
          operatorAmount: response.data.OperatorAmount
        }
      };
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_AIRTIME_SERVICE] International status query failed:', {
        requestId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Categorize international error for better handling
   *
   * @param {Error} error - Error object
   * @returns {string} Error category
   */
  categorizeInternationalError(error) {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      if (status === 400) return 'invalid_international_request';
      if (status === 401) return 'international_authentication_failed';
      if (status === 403) return 'international_access_denied';
      if (status === 404) return 'international_service_not_found';
      if (status === 429) return 'international_rate_limit_exceeded';
      if (status >= 500) return 'international_server_error';

      // Check VTpass specific error codes
      if (data?.code) {
        switch (data.code) {
          case '001': return 'international_insufficient_balance';
          case '002': return 'international_invalid_service';
          case '003': return 'international_service_unavailable';
          default: return 'international_vtpass_error';
        }
      }
    }

    if (error.code === 'ECONNABORTED') return 'international_timeout';
    if (error.code === 'ENOTFOUND') return 'international_network_error';
    if (error.code === 'ECONNREFUSED') return 'international_connection_refused';

    return 'international_unknown_error';
  }

  /**
   * Clear cache for specific data type
   *
   * @param {string} type - Cache type to clear
   */
  clearCache(type = 'all') {
    switch (type) {
      case 'countries':
        this.cache.countries = { data: null, expiry: null };
        break;
      case 'operators':
        this.cache.operators.clear();
        break;
      case 'variations':
        this.cache.variations.clear();
        break;
      case 'productTypes':
        this.cache.productTypes.clear();
        break;
      case 'all':
      default:
        this.cache.countries = { data: null, expiry: null };
        this.cache.operators.clear();
        this.cache.variations.clear();
        this.cache.productTypes.clear();
        break;
    }

    logger.info('🗑️ [INTERNATIONAL_AIRTIME_SERVICE] Cache cleared:', { type });
  }
}

// Export singleton instance
module.exports = new InternationalAirtimeService();
