/**
 * VTU Provider Manager
 * 
 * Enterprise-grade provider management system with manual admin control
 * for multiple VTU service providers.
 *
 * Features:
 * - Multiple provider support (PluginNG primary, VTpass backup)
 * - Manual provider switching (admin controlled)
 * - Provider health monitoring
 * - Circuit breaker pattern implementation
 * - Provider performance analytics
 * - Real-time provider status tracking
 * - No automatic failover - admin must manually switch providers
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const vtpassService = require('./vtpassService');
const pluginNGService = require('./pluginngService');
const { vtpassErrorHandler, ERROR_CATEGORIES } = require('../utils/vtpassErrorHandler');
const logger = require('../utils/logger');
const { getSupabase } = require('../config/database');

/**
 * Provider Status Enum
 */
const PROVIDER_STATUS = {
  ACTIVE: 'active',
  DEGRADED: 'degraded',
  FAILED: 'failed',
  MAINTENANCE: 'maintenance',
  DISABLED: 'disabled'
};

/**
 * Provider Priority Levels
 */
const PROVIDER_PRIORITY = {
  PRIMARY: 1,
  SECONDARY: 2,
  TERTIARY: 3,
  BACKUP: 4
};

/**
 * Circuit Breaker States
 */
const CIRCUIT_BREAKER_STATES = {
  CLOSED: 'closed',     // Normal operation
  OPEN: 'open',         // Failing, requests blocked
  HALF_OPEN: 'half_open' // Testing if service recovered
};

/**
 * VTU Provider Manager Class
 * Manages multiple VTU providers with intelligent routing and failover
 */
class VTUProviderManager {
  constructor() {
    this.providers = new Map();
    this.circuitBreakers = new Map();
    this.providerStats = new Map();
    this.supabase = getSupabase();
    this.currentProvider = 'pluginng'; // Default to PluginNG as primary provider
    this.manualControlEnabled = true; // Enable manual control by default

    // Configuration
    this.config = {
      failureThreshold: 5,           // Failures before circuit opens
      recoveryTimeout: 60000,        // 1 minute before trying half-open
      successThreshold: 3,           // Successes needed to close circuit
      healthCheckInterval: 30000,    // 30 seconds health check
      maxRetryAttempts: 3,          // Max retries per provider
      loadBalancingEnabled: false,   // Disable load balancing for manual control
      costOptimizationEnabled: false, // Disable cost optimization for manual control
      autoFailoverEnabled: false     // Disable automatic failover
    };

    this.initializeProviders();
    this.startHealthMonitoring();

    // Load provider status from database asynchronously (delayed)
    setTimeout(() => {
      this.loadProviderStatusFromDatabase().catch(error => {
        logger.error('❌ [VTU_PROVIDER_MANAGER] Failed to load provider status from database:', error);
        // Continue with default provider setup
      });
    }, 2000); // Wait 2 seconds for database to be fully ready

    logger.info('✅ [VTU_PROVIDER_MANAGER] Provider manager initialized with manual control');
  }

  /**
   * Initialize available VTU providers
   */
  initializeProviders() {
    // Primary provider: PluginNG
    this.registerProvider({
      id: 'pluginng',
      name: 'PluginNG',
      priority: PROVIDER_PRIORITY.PRIMARY,
      status: PROVIDER_STATUS.ACTIVE,
      service: pluginNGService,
      config: {
        baseUrl: process.env.PLUGINNG_BASE_URL || 'https://pluginng.com/api',
        timeout: 30000,
        retryDelay: 5000,
        costPerTransaction: 0.7, // ₦0.70 per transaction
        supportedServices: ['airtime'],
        maxConcurrentRequests: 50
      },
      capabilities: {
        airtime: true,
        data: false,
        electricity: false,
        cable: false,
        realTimeStatus: true,
        bulkTransactions: false,
        supportedNetworks: ['mtn', 'glo', 'airtel', 'etisalat']
      }
    });

    // Backup provider: VTpass
    this.registerProvider({
      id: 'vtpass',
      name: 'VTpass',
      priority: PROVIDER_PRIORITY.BACKUP,
      status: PROVIDER_STATUS.ACTIVE, // Active backup provider
      service: vtpassService,
      config: {
        baseUrl: process.env.VTPASS_BASE_URL || 'https://vtpass.com/api',
        timeout: 30000,
        retryDelay: 5000,
        costPerTransaction: 0.5, // ₦0.50 per transaction
        supportedServices: ['airtime', 'data'],
        maxConcurrentRequests: 100
      },
      capabilities: {
        airtime: true,
        data: true,
        electricity: false,
        cable: false,
        realTimeStatus: true,
        bulkTransactions: false,
        supportedNetworks: ['mtn', 'glo', 'airtel', 'etisalat']
      }
    });

    logger.info('🔧 [VTU_PROVIDER_MANAGER] Providers initialized:', {
      totalProviders: this.providers.size,
      activeProviders: this.getActiveProviders().length
    });
  }

  /**
   * Load provider status from database
   */
  async loadProviderStatusFromDatabase() {
    try {
      const { data: providerStatuses, error } = await this.supabase
        .from('provider_status')
        .select('*')
        .order('priority', { ascending: true });

      if (error) {
        logger.error('❌ [VTU_PROVIDER_MANAGER] Failed to load provider status:', error);
        return;
      }

      // Update provider status based on database
      for (const status of providerStatuses) {
        const provider = this.providers.get(status.provider_id);
        if (provider) {
          provider.status = status.is_enabled ? PROVIDER_STATUS.ACTIVE : PROVIDER_STATUS.DISABLED;
          provider.priority = status.is_primary ? PROVIDER_PRIORITY.PRIMARY : PROVIDER_PRIORITY.BACKUP;
          provider.config = { ...provider.config, ...status.config };

          if (status.is_primary && status.is_enabled) {
            this.currentProvider = status.provider_id;
          }
        }
      }

      logger.info('✅ [VTU_PROVIDER_MANAGER] Provider status loaded from database:', {
        currentProvider: this.currentProvider,
        enabledProviders: providerStatuses.filter(p => p.is_enabled).length
      });

    } catch (error) {
      logger.error('❌ [VTU_PROVIDER_MANAGER] Error loading provider status:', error);
    }
  }

  /**
   * Refresh provider status from database
   */
  async refreshProviderStatus() {
    await this.loadProviderStatusFromDatabase();
  }

  /**
   * Get current active provider (manual control)
   */
  getCurrentProvider() {
    if (!this.manualControlEnabled) {
      return this.getActiveProviders()[0]; // Fallback to automatic selection
    }

    if (!this.currentProvider) {
      // Try to get from database asynchronously
      this.loadProviderStatusFromDatabase().catch(error => {
        logger.warn('⚠️ [VTU_PROVIDER_MANAGER] Could not load provider status from database:', error.message);
      });
    }

    const provider = this.providers.get(this.currentProvider);
    if (provider && provider.status === PROVIDER_STATUS.ACTIVE) {
      return provider;
    }

    // No fallback in manual control mode - admin must enable a provider
    logger.warn('⚠️ [VTU_PROVIDER_MANAGER] Current provider not available:', {
      currentProvider: this.currentProvider,
      providerExists: !!provider,
      providerStatus: provider?.status
    });

    return null;
  }

  /**
   * Set current provider manually (admin control)
   */
  async setCurrentProvider(providerId) {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    if (provider.status !== PROVIDER_STATUS.ACTIVE) {
      throw new Error(`Provider ${providerId} is not active`);
    }

    this.currentProvider = providerId;

    logger.info('🔄 [VTU_PROVIDER_MANAGER] Current provider set manually:', {
      providerId,
      providerName: provider.name
    });

    return provider;
  }

  /**
   * Register a new VTU provider
   */
  registerProvider(providerConfig) {
    const provider = {
      ...providerConfig,
      registeredAt: new Date().toISOString(),
      lastHealthCheck: null,
      consecutiveFailures: 0,
      consecutiveSuccesses: 0,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      lastError: null
    };

    this.providers.set(provider.id, provider);
    
    // Initialize circuit breaker
    this.circuitBreakers.set(provider.id, {
      state: CIRCUIT_BREAKER_STATES.CLOSED,
      failureCount: 0,
      lastFailureTime: null,
      nextAttemptTime: null
    });

    // Initialize stats
    this.providerStats.set(provider.id, {
      hourlyStats: new Map(),
      dailyStats: new Map(),
      responseTimeHistory: []
    });

    logger.info('✅ [VTU_PROVIDER_MANAGER] Provider registered:', {
      providerId: provider.id,
      name: provider.name,
      priority: provider.priority,
      status: provider.status
    });
  }

  /**
   * Purchase airtime with manual provider selection (admin controlled)
   */
  async purchaseAirtime(purchaseData) {
    const requestId = purchaseData.requestId || `mgr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    logger.info('🎯 [VTU_PROVIDER_MANAGER] Airtime purchase request (manual control):', {
      requestId,
      userId: purchaseData.userId,
      amount: purchaseData.amount,
      phone: purchaseData.phone?.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
      network: purchaseData.network,
      networkName: purchaseData.networkName,
      manualControl: this.manualControlEnabled
    });

    const startTime = Date.now();

    if (this.manualControlEnabled) {
      // Use manually selected provider
      const currentProvider = this.getCurrentProvider();

      if (!currentProvider) {
        throw new Error('No VTU provider is currently enabled. Please enable PluginNG or VTpass in the admin dashboard. No automatic fallback will occur - manual provider control is required.');
      }

      logger.info(`🔄 [VTU_PROVIDER_MANAGER] Using manually selected provider:`, {
        requestId,
        providerId: currentProvider.id,
        providerName: currentProvider.name
      });

      try {
        // Check if provider is available
        if (!this.isProviderAvailable(currentProvider.id)) {
          throw new Error(`Provider ${currentProvider.name} is currently unavailable. Please enable the provider in the admin dashboard or switch to another provider manually.`);
        }

        // Execute purchase with selected provider
        const result = await this.executeProviderPurchase(currentProvider, {
          ...purchaseData,
          requestId
        });

        // Record success
        this.recordProviderSuccess(currentProvider.id, Date.now() - startTime);

        logger.info('✅ [VTU_PROVIDER_MANAGER] Purchase successful with manual provider:', {
          requestId,
          providerId: currentProvider.id,
          transactionId: result.transactionId,
          duration: Date.now() - startTime
        });

        return {
          ...result,
          providerId: currentProvider.id,
          providerName: currentProvider.name,
          responseTime: Date.now() - startTime
        };

      } catch (error) {
        const duration = Date.now() - startTime;

        // Record failure
        this.recordProviderFailure(currentProvider.id, error);

        logger.error(`❌ [VTU_PROVIDER_MANAGER] Manual provider ${currentProvider.id} failed:`, {
          requestId,
          error: error.message,
          duration,
          suggestion: 'Consider switching to another provider in the admin dashboard'
        });

        // Don't try other providers in manual mode - admin must switch manually
        throw new Error(`Transaction failed with ${currentProvider.name}: ${error.message}. Please check provider status or switch to another provider in the admin dashboard.`);
      }
    } else {
      // Manual control is disabled - this should not happen in current setup
      throw new Error('Manual control is disabled. Please enable manual control in the admin dashboard.');
    }
  }

  /**
   * Purchase airtime with automatic provider selection (fallback mode)
   */
  async purchaseAirtimeAutomatic(purchaseData, requestId, startTime) {
    const availableProviders = this.selectProvidersForService('airtime', purchaseData.network);

    if (availableProviders.length === 0) {
      const networkInfo = purchaseData.network ? ` for ${purchaseData.networkName}` : '';
      throw new Error(`No available providers for airtime service${networkInfo}`);
    }

    let lastError;
    let attemptCount = 0;

    // Try providers in order of priority/availability
    for (const provider of availableProviders) {
      attemptCount++;

      try {
        logger.info('🔄 [VTU_PROVIDER_MANAGER] Attempting with provider:', {
          requestId,
          providerId: provider.id,
          attempt: attemptCount,
          totalProviders: availableProviders.length
        });

        // Check circuit breaker
        if (!this.isProviderAvailable(provider.id)) {
          logger.warn('⚠️ [VTU_PROVIDER_MANAGER] Provider circuit breaker open:', {
            requestId,
            providerId: provider.id
          });
          continue;
        }

        // Execute purchase through provider
        const result = await this.executeProviderPurchase(provider, purchaseData);

        const responseTime = Date.now() - startTime;

        // Record success
        this.recordProviderSuccess(provider.id, responseTime);

        logger.info('✅ [VTU_PROVIDER_MANAGER] Purchase successful:', {
          requestId,
          providerId: provider.id,
          transactionId: result.transactionId,
          responseTime: `${responseTime}ms`
        });

        return {
          ...result,
          providerId: provider.id,
          providerName: provider.name,
          attemptCount,
          responseTime
        };

      } catch (error) {
        lastError = error;

        // Record failure
        this.recordProviderFailure(provider.id, error);

        logger.warn('⚠️ [VTU_PROVIDER_MANAGER] Provider attempt failed:', {
          requestId,
          providerId: provider.id,
          attempt: attemptCount,
          error: error.message
        });

        // Check if we should continue to next provider
        const errorCategory = this.categorizeProviderError(error);
        if (errorCategory === 'fatal' || attemptCount >= availableProviders.length) {
          break;
        }

        // Add delay before trying next provider
        if (attemptCount < availableProviders.length) {
          await this.delay(2000); // 2 second delay
        }
      }
    }

    // All providers failed
    logger.error('❌ [VTU_PROVIDER_MANAGER] All providers failed:', {
      requestId,
      attemptCount,
      providersAttempted: availableProviders.map(p => p.id),
      finalError: lastError?.message
    });

    throw lastError || new Error('All VTU providers are currently unavailable');
  }

  /**
   * Execute purchase through specific provider
   */
  async executeProviderPurchase(provider, purchaseData) {
    if (!provider.service) {
      throw new Error(`Provider ${provider.id} service not implemented`);
    }

    // Add provider-specific metadata
    const enhancedPurchaseData = {
      ...purchaseData,
      providerId: provider.id,
      providerName: provider.name
    };

    // Execute purchase based on provider type
    switch (provider.id) {
      case 'vtpass':
        return await provider.service.purchaseAirtime(enhancedPurchaseData);

      case 'pluginng':
        return await provider.service.purchaseAirtime(enhancedPurchaseData);

      default:
        throw new Error(`Unknown provider: ${provider.id}`);
    }
  }

  /**
   * Select providers for a specific service and network with intelligent routing
   */
  selectProvidersForService(serviceType, network = null) {
    const availableProviders = Array.from(this.providers.values())
      .filter(provider => {
        // Basic availability checks
        if (provider.status !== PROVIDER_STATUS.ACTIVE ||
            provider.capabilities[serviceType] !== true ||
            !this.isProviderAvailable(provider.id)) {
          return false;
        }

        // Network support check (if network is specified)
        if (network && provider.capabilities.supportedNetworks) {
          return provider.capabilities.supportedNetworks.includes(network);
        }

        return true;
      });

    if (availableProviders.length === 0) {
      return [];
    }

    // Sort by priority and performance
    return availableProviders.sort((a, b) => {
      // Primary sort: Priority (lower number = higher priority)
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }

      // Secondary sort: Success rate
      const aSuccessRate = a.totalRequests > 0 ? a.successfulRequests / a.totalRequests : 0;
      const bSuccessRate = b.totalRequests > 0 ? b.successfulRequests / b.totalRequests : 0;

      if (aSuccessRate !== bSuccessRate) {
        return bSuccessRate - aSuccessRate; // Higher success rate first
      }

      // Tertiary sort: Average response time
      return a.averageResponseTime - b.averageResponseTime; // Faster response first
    });
  }

  /**
   * Check if provider is available (circuit breaker check)
   */
  isProviderAvailable(providerId) {
    const circuitBreaker = this.circuitBreakers.get(providerId);
    if (!circuitBreaker) return false;

    const now = Date.now();

    switch (circuitBreaker.state) {
      case CIRCUIT_BREAKER_STATES.CLOSED:
        return true;

      case CIRCUIT_BREAKER_STATES.OPEN:
        // Check if recovery timeout has passed
        if (circuitBreaker.nextAttemptTime && now >= circuitBreaker.nextAttemptTime) {
          // Move to half-open state
          circuitBreaker.state = CIRCUIT_BREAKER_STATES.HALF_OPEN;
          logger.info('🔄 [VTU_PROVIDER_MANAGER] Circuit breaker half-open:', { providerId });
          return true;
        }
        return false;

      case CIRCUIT_BREAKER_STATES.HALF_OPEN:
        return true;

      default:
        return false;
    }
  }

  /**
   * Record provider success
   */
  recordProviderSuccess(providerId, responseTime) {
    const provider = this.providers.get(providerId);
    const circuitBreaker = this.circuitBreakers.get(providerId);
    
    if (!provider || !circuitBreaker) return;

    // Update provider stats
    provider.totalRequests++;
    provider.successfulRequests++;
    provider.consecutiveFailures = 0;
    provider.consecutiveSuccesses++;
    
    // Update average response time
    provider.averageResponseTime = provider.averageResponseTime === 0
      ? responseTime
      : (provider.averageResponseTime + responseTime) / 2;

    // Update circuit breaker
    if (circuitBreaker.state === CIRCUIT_BREAKER_STATES.HALF_OPEN) {
      if (provider.consecutiveSuccesses >= this.config.successThreshold) {
        circuitBreaker.state = CIRCUIT_BREAKER_STATES.CLOSED;
        circuitBreaker.failureCount = 0;
        logger.info('✅ [VTU_PROVIDER_MANAGER] Circuit breaker closed:', { providerId });
      }
    }

    this.updateProviderStats(providerId, 'success', responseTime);
  }

  /**
   * Record provider failure
   */
  recordProviderFailure(providerId, error) {
    const provider = this.providers.get(providerId);
    const circuitBreaker = this.circuitBreakers.get(providerId);
    
    if (!provider || !circuitBreaker) return;

    // Update provider stats
    provider.totalRequests++;
    provider.failedRequests++;
    provider.consecutiveFailures++;
    provider.consecutiveSuccesses = 0;
    provider.lastError = error.message;

    // Update circuit breaker
    circuitBreaker.failureCount++;
    circuitBreaker.lastFailureTime = Date.now();

    if (circuitBreaker.failureCount >= this.config.failureThreshold) {
      circuitBreaker.state = CIRCUIT_BREAKER_STATES.OPEN;
      circuitBreaker.nextAttemptTime = Date.now() + this.config.recoveryTimeout;
      
      logger.warn('🚫 [VTU_PROVIDER_MANAGER] Circuit breaker opened:', {
        providerId,
        failureCount: circuitBreaker.failureCount,
        nextAttemptTime: new Date(circuitBreaker.nextAttemptTime).toISOString()
      });
    }

    this.updateProviderStats(providerId, 'failure', 0);
  }

  /**
   * Update provider statistics
   */
  updateProviderStats(providerId, type, responseTime) {
    const stats = this.providerStats.get(providerId);
    if (!stats) return;

    const now = new Date();
    const hourKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}`;
    const dayKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}`;

    // Update hourly stats
    if (!stats.hourlyStats.has(hourKey)) {
      stats.hourlyStats.set(hourKey, { success: 0, failure: 0, totalResponseTime: 0, count: 0 });
    }
    const hourlyStats = stats.hourlyStats.get(hourKey);
    hourlyStats[type]++;
    if (type === 'success') {
      hourlyStats.totalResponseTime += responseTime;
      hourlyStats.count++;
    }

    // Update daily stats
    if (!stats.dailyStats.has(dayKey)) {
      stats.dailyStats.set(dayKey, { success: 0, failure: 0, totalResponseTime: 0, count: 0 });
    }
    const dailyStats = stats.dailyStats.get(dayKey);
    dailyStats[type]++;
    if (type === 'success') {
      dailyStats.totalResponseTime += responseTime;
      dailyStats.count++;
    }

    // Update response time history (keep last 100 entries)
    if (type === 'success') {
      stats.responseTimeHistory.push({ time: now.toISOString(), responseTime });
      if (stats.responseTimeHistory.length > 100) {
        stats.responseTimeHistory.shift();
      }
    }
  }

  /**
   * Categorize provider error for routing decisions
   */
  categorizeProviderError(error) {
    const errorCategory = vtpassErrorHandler.categorizeError(error);
    
    // Map error categories to routing decisions
    switch (errorCategory) {
      case 'authentication_failed':
      case 'invalid_request':
        return 'fatal'; // Don't retry with other providers
      
      case 'insufficient_balance':
        return 'provider_specific'; // Try other providers
      
      case 'service_unavailable':
      case 'network_error':
      case 'timeout':
        return 'retryable'; // Try other providers
      
      default:
        return 'retryable';
    }
  }

  /**
   * Get active providers
   */
  getActiveProviders() {
    return Array.from(this.providers.values())
      .filter(provider => provider.status === PROVIDER_STATUS.ACTIVE);
  }

  /**
   * Start health monitoring for all providers
   */
  startHealthMonitoring() {
    setInterval(() => {
      this.performHealthChecks();
    }, this.config.healthCheckInterval);

    logger.info('🏥 [VTU_PROVIDER_MANAGER] Health monitoring started');
  }

  /**
   * Perform health checks on all providers
   */
  async performHealthChecks() {
    const providers = Array.from(this.providers.values());
    
    for (const provider of providers) {
      if (provider.status === PROVIDER_STATUS.ACTIVE) {
        await this.checkProviderHealth(provider);
      }
    }
  }

  /**
   * Check individual provider health
   */
  async checkProviderHealth(provider) {
    try {
      // Implement provider-specific health check
      // For now, just update last health check time
      provider.lastHealthCheck = new Date().toISOString();
      
      logger.debug('💓 [VTU_PROVIDER_MANAGER] Health check completed:', {
        providerId: provider.id,
        status: provider.status
      });
    } catch (error) {
      logger.warn('⚠️ [VTU_PROVIDER_MANAGER] Health check failed:', {
        providerId: provider.id,
        error: error.message
      });
    }
  }

  /**
   * Utility function for delays
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get provider statistics
   */
  getProviderStats() {
    const stats = {};

    for (const [providerId, provider] of this.providers.entries()) {
      const circuitBreaker = this.circuitBreakers.get(providerId);

      stats[providerId] = {
        name: provider.name,
        status: provider.status,
        priority: provider.priority,
        circuitBreakerState: circuitBreaker?.state,
        totalRequests: provider.totalRequests,
        successfulRequests: provider.successfulRequests,
        failedRequests: provider.failedRequests,
        successRate: provider.totalRequests > 0
          ? ((provider.successfulRequests / provider.totalRequests) * 100).toFixed(2)
          : 0,
        averageResponseTime: Math.round(provider.averageResponseTime),
        consecutiveFailures: provider.consecutiveFailures,
        lastHealthCheck: provider.lastHealthCheck,
        lastError: provider.lastError
      };
    }

    return stats;
  }

  /**
   * Enable manual control mode
   */
  enableManualControl() {
    this.manualControlEnabled = true;
    logger.info('🎛️ [VTU_PROVIDER_MANAGER] Manual control enabled');
  }

  /**
   * Disable manual control mode
   */
  disableManualControl() {
    this.manualControlEnabled = false;
    logger.info('🤖 [VTU_PROVIDER_MANAGER] Manual control disabled - automatic mode enabled');
  }

  /**
   * Set provider status manually
   */
  setProviderStatus(providerId, status) {
    const provider = this.providers.get(providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    const oldStatus = provider.status;
    provider.status = status;

    logger.info('🔄 [VTU_PROVIDER_MANAGER] Provider status changed:', {
      providerId,
      oldStatus,
      newStatus: status
    });

    // Reset circuit breaker if provider is being activated
    if (status === PROVIDER_STATUS.ACTIVE) {
      const circuitBreaker = this.circuitBreakers.get(providerId);
      if (circuitBreaker) {
        circuitBreaker.state = CIRCUIT_BREAKER_STATES.CLOSED;
        circuitBreaker.failureCount = 0;
        circuitBreaker.lastFailureTime = null;
        circuitBreaker.nextAttemptTime = null;
      }
    }

    return provider;
  }

  /**
   * Save provider status to database for persistence
   */
  async saveProviderStatusToDatabase() {
    try {
      const providers = Array.from(this.providers.values());

      for (const provider of providers) {
        const { error } = await this.supabase
          .from('provider_status')
          .upsert({
            provider_id: provider.id,
            provider_name: provider.name,
            is_enabled: provider.status === PROVIDER_STATUS.ACTIVE,
            is_primary: provider.priority === PROVIDER_PRIORITY.PRIMARY,
            priority: provider.priority,
            config: provider.config,
            last_updated: new Date().toISOString()
          }, {
            onConflict: 'provider_id'
          });

        if (error) {
          logger.error('❌ [VTU_PROVIDER_MANAGER] Failed to save provider status:', {
            providerId: provider.id,
            error: error.message
          });
        }
      }

      logger.info('✅ [VTU_PROVIDER_MANAGER] Provider status saved to database');
    } catch (error) {
      logger.error('❌ [VTU_PROVIDER_MANAGER] Error saving provider status to database:', error);
      throw error;
    }
  }
}

// Create singleton instance
const vtuProviderManager = new VTUProviderManager();

module.exports = vtuProviderManager;
