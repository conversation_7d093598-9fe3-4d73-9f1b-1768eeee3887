const { createClient } = require('@supabase/supabase-js');
const logger = require('../utils/logger');

let supabase = null;

const connectDB = async () => {
  try {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Supabase URL and Service Role Key are required in environment variables');
    }

    // Create Supabase client with service role key for admin operations
    supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Test the connection
    const { data, error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });

    if (error && error.code !== 'PGRST116') { // PGRST116 is "table not found" which is ok for initial setup
      throw error;
    }

    logger.info('Supabase connected successfully');

    // Create tables if they don't exist
    await createTables();

  } catch (error) {
    logger.error('Supabase connection failed:', error.message);
    process.exit(1);
  }
};

const createTables = async () => {
  try {
    // Check if essential tables exist by trying to query them
    const tablesToCheck = ['users', 'sessions', 'transactions', 'user_fcm_tokens'];
    const missingTables = [];

    for (const tableName of tablesToCheck) {
      try {
        const { error } = await supabase
          .from(tableName)
          .select('count', { count: 'exact', head: true });

        if (error && error.code === 'PGRST116') {
          // Table doesn't exist
          missingTables.push(tableName);
        }
      } catch (err) {
        // Table doesn't exist or other error
        missingTables.push(tableName);
      }
    }

    if (missingTables.length > 0) {
      logger.warn(`Missing database tables: ${missingTables.join(', ')}`);
      logger.warn('Please run the SQL setup script in your Supabase dashboard:');
      logger.warn('1. Go to your Supabase project dashboard');
      logger.warn('2. Navigate to SQL Editor');
      logger.warn('3. Run the script from: backend/database/schema.sql');
      logger.warn('4. If user_fcm_tokens is missing, also run: backend/migrations/create_fcm_tokens_table.sql');
      logger.warn('5. Restart the server after running the scripts');
    } else {
      logger.info('All database tables initialized successfully');

      // Try to refresh schema cache by making a simple query to transactions table
      try {
        await supabase
          .from('transactions')
          .select('id, description')
          .limit(1);
        logger.info('✅ [DATABASE] Schema cache refreshed successfully');
      } catch (schemaError) {
        logger.warn('⚠️ [DATABASE] Schema cache refresh failed:', schemaError.message);
        logger.warn('This might cause issues with transactions. Consider restarting Supabase or refreshing schema.');
      }
    }
  } catch (error) {
    logger.warn('Table verification skipped:', error.message);
    logger.warn('Please ensure your database is properly set up using the SQL scripts');
  }
};

const getSupabase = () => {
  if (!supabase) {
    throw new Error('Supabase not initialized. Call connectDB first.');
  }
  return supabase;
};

/**
 * Refresh Supabase schema cache by making queries to all essential tables
 * This helps resolve "column not found in schema cache" errors
 */
const refreshSchemaCache = async () => {
  try {
    logger.info('🔄 [DATABASE] Refreshing Supabase schema cache...');

    const tablesToRefresh = [
      { table: 'users', columns: 'id, first_name, last_name, phone_number' },
      { table: 'transactions', columns: 'id, description, status, amount' },
      { table: 'sessions', columns: 'id, user_id, expires_at' },
      { table: 'user_fcm_tokens', columns: 'id, user_id, fcm_token' }
    ];

    for (const { table, columns } of tablesToRefresh) {
      try {
        await supabase
          .from(table)
          .select(columns)
          .limit(1);
        logger.info(`✅ [DATABASE] Schema refreshed for table: ${table}`);
      } catch (error) {
        logger.warn(`⚠️ [DATABASE] Failed to refresh schema for ${table}:`, error.message);
      }
    }

    logger.info('✅ [DATABASE] Schema cache refresh completed');
    return true;
  } catch (error) {
    logger.error('❌ [DATABASE] Schema cache refresh failed:', error);
    return false;
  }
};

module.exports = { connectDB, getSupabase, refreshSchemaCache };
