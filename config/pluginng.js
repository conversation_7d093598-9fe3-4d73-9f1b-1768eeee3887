/**
 * PluginNG API Configuration Module
 * 
 * Comprehensive configuration management for PluginNG airtime API integration
 * with enterprise-grade security, validation, and network support.
 * 
 * Features:
 * - Secure credential management with environment variables
 * - Multi-network support (MTN, GLO, Airtel, 9mobile)
 * - Phone number validation and normalization
 * - Request authentication and headers
 * - Production/sandbox environment handling
 * - Comprehensive error handling
 * - Network detection and subcategory mapping
 * 
 * Security Features:
 * - Bearer token authentication
 * - Request timeout configuration
 * - Rate limiting support
 * - Input validation and sanitization
 * - Secure credential handling
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const logger = require('../utils/logger');

/**
 * PluginNG Configuration Class
 * Manages all PluginNG API configuration, validation, and network operations
 */
class PluginNGConfig {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.validateEnvironmentVariables();
    this.initializeConfiguration();
    
    logger.info('🔧 [PLUGINNG_CONFIG] Configuration initialized successfully', {
      environment: this.isProduction ? 'production' : 'development',
      baseUrl: this.config.baseUrl,
      supportedNetworks: Object.keys(this.config.services)
    });
  }

  /**
   * Validate required environment variables
   */
  validateEnvironmentVariables() {
    const requiredVars = [
      'PLUGINNG_EMAIL',
      'PLUGINNG_PASSWORD'
    ];

    const missingVars = requiredVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      const errorMsg = `Missing required PluginNG environment variables: ${missingVars.join(', ')}`;
      logger.error('❌ [PLUGINNG_CONFIG] Configuration error:', { missingVars });
      throw new Error(errorMsg);
    }

    logger.info('✅ [PLUGINNG_CONFIG] Environment variables validated successfully');
  }

  /**
   * Initialize PluginNG configuration
   */
  initializeConfiguration() {
    // API Configuration
    this.config = {
      // Authentication
      apiToken: process.env.PLUGINNG_API_TOKEN,
      
      // Endpoints
      baseUrl: process.env.PLUGINNG_BASE_URL || 'https://pluginng.com/api',
      
      // Service Configuration - PluginNG subcategory IDs for Nigerian networks
      services: {
        mtn: {
          subcategoryId: '8',
          name: 'MTN Nigeria',
          prefixes: ['0803', '0806', '0703', '0706', '0813', '0816', '0810', '0814', '0903', '0906', '0913', '0916']
        },
        glo: {
          subcategoryId: '10',
          name: 'Glo Nigeria',
          prefixes: ['0805', '0807', '0705', '0815', '0811', '0905', '0915']
        },
        airtel: {
          subcategoryId: '9',
          name: 'Airtel Nigeria',
          prefixes: ['0802', '0808', '0708', '0812', '0701', '0902', '0907', '0901', '0904', '0912']
        }
        // 9mobile disabled - network currently not stable
        // etisalat: {
        //   subcategoryId: '',
        //   name: '9mobile Nigeria',
        //   prefixes: ['0809', '0818', '0817', '0909', '0908']
        // }
      },
      
      // Security Settings
      timeout: parseInt(process.env.PLUGINNG_TIMEOUT) || 30000, // 30 seconds
      maxRetries: parseInt(process.env.PLUGINNG_MAX_RETRIES) || 3,
      
      // Rate Limiting
      rateLimitPerMinute: parseInt(process.env.PLUGINNG_RATE_LIMIT_PER_MINUTE) || 60,
      
      // Transaction Limits
      minAmount: parseInt(process.env.PLUGINNG_MIN_AMOUNT) || 50, // ₦50
      maxAmount: parseInt(process.env.PLUGINNG_MAX_AMOUNT) || 50000, // ₦50,000
      
      // Request Headers
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'User-Agent': 'PayVendy-VTU/1.0.0',
        'X-API-Version': 'v1'
      }
    };

    // Endpoints configuration
    this.endpoints = {
      purchase: `${this.config.baseUrl}/purchase/airtime`,
      balance: `${this.config.baseUrl}/balance`,
      query: `${this.config.baseUrl}/query/transaction`
    };

    // Network prefixes for quick lookup
    this.networkPrefixes = this.buildNetworkPrefixMap();
    
    logger.info('🔧 [PLUGINNG_CONFIG] Configuration initialized:', {
      baseUrl: this.config.baseUrl,
      timeout: this.config.timeout,
      maxRetries: this.config.maxRetries,
      supportedNetworks: Object.keys(this.config.services).length,
      totalPrefixes: this.networkPrefixes.size,
      samplePrefixes: Array.from(this.networkPrefixes.keys()).slice(0, 10)
    });
  }

  /**
   * Build network prefix mapping for fast lookup
   */
  buildNetworkPrefixMap() {
    const prefixMap = new Map();
    
    Object.entries(this.config.services).forEach(([network, config]) => {
      config.prefixes.forEach(prefix => {
        prefixMap.set(prefix, {
          network,
          subcategoryId: config.subcategoryId,
          name: config.name
        });
      });
    });
    
    return prefixMap;
  }

  /**
   * Get authentication headers for PluginNG API
   */
  getAuthHeaders() {
    return {
      ...this.config.headers,
      'Authorization': `Bearer ${this.config.apiToken}`
    };
  }

  /**
   * Get endpoints configuration
   */
  getEndpoints() {
    return this.endpoints;
  }

  /**
   * Validate and normalize phone number for any Nigerian network
   *
   * @param {string} phone - Phone number to validate
   * @param {string} expectedNetwork - Expected network (optional)
   * @returns {Object} Validation result with network detection
   */
  validatePhone(phone, expectedNetwork = null) {
    try {
      logger.info('🔍 [PLUGINNG_CONFIG] Starting phone validation:', {
        originalPhone: phone,
        expectedNetwork
      });

      // Remove all non-digit characters
      const cleanPhone = phone.replace(/\D/g, '');

      logger.info('🔍 [PLUGINNG_CONFIG] Cleaned phone:', {
        cleanPhone,
        length: cleanPhone.length
      });
      
      // Handle different phone number formats
      let normalizedPhone;
      if (cleanPhone.startsWith('234')) {
        // International format: +234XXXXXXXXXX
        normalizedPhone = cleanPhone;
      } else if (cleanPhone.startsWith('0')) {
        // Local format: 0XXXXXXXXXX
        normalizedPhone = '234' + cleanPhone.substring(1);
      } else if (cleanPhone.length === 10) {
        // Without country code: XXXXXXXXXX
        normalizedPhone = '234' + cleanPhone;
      } else {
        throw new Error('Invalid phone number format');
      }

      // Validate length (should be 13 digits: 234 + 10 digits)
      if (normalizedPhone.length !== 13) {
        throw new Error('Phone number must be 10 digits after country code');
      }

      // Extract prefix for network detection (convert back to 0XXX format)
      const prefix = '0' + normalizedPhone.substring(3, 6); // Get 0XXX format (4 digits total)

      logger.info('🔍 [PLUGINNG_CONFIG] Phone validation debug:', {
        originalPhone: phone,
        cleanPhone,
        normalizedPhone,
        extractedPrefix: prefix,
        prefixLength: prefix.length,
        shouldMatch: '0906',
        availablePrefixes: Array.from(this.networkPrefixes.keys()).slice(0, 10) // Show first 10 for debug
      });

      // Detect network
      const networkInfo = this.detectNetwork(prefix);

      logger.info('🔍 [PLUGINNG_CONFIG] Network detection result:', {
        prefix,
        networkInfo,
        foundNetwork: !!networkInfo
      });

      if (!networkInfo) {
        logger.error('❌ [PLUGINNG_CONFIG] No network found for prefix:', {
          prefix,
          availablePrefixes: Array.from(this.networkPrefixes.keys())
        });
        throw new Error('Unsupported network or invalid phone number');
      }

      // Validate against expected network if provided
      if (expectedNetwork && networkInfo.network !== expectedNetwork) {
        throw new Error(`Phone number belongs to ${networkInfo.name}, expected ${expectedNetwork.toUpperCase()}`);
      }

      // Format for display (0XXXXXXXXXX)
      const displayPhone = '0' + normalizedPhone.substring(3);

      return {
        isValid: true,
        normalized: normalizedPhone,
        display: displayPhone,
        network: networkInfo.network,
        networkName: networkInfo.name,
        subcategoryId: networkInfo.subcategoryId,
        prefix: prefix
      };

    } catch (error) {
      logger.warn('📱 [PLUGINNG_CONFIG] Phone validation failed:', {
        phone: phone?.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'),
        error: error.message
      });

      return {
        isValid: false,
        error: error.message,
        normalized: null,
        display: null,
        network: null,
        networkName: null,
        subcategoryId: null
      };
    }
  }

  /**
   * Detect network from phone number prefix
   *
   * @param {string} prefix - 4-digit prefix
   * @returns {Object|null} Network information or null if not found
   */
  detectNetwork(prefix) {
    return this.networkPrefixes.get(prefix) || null;
  }

  /**
   * Get supported networks
   */
  getSupportedNetworks() {
    return Object.keys(this.config.services).map(network => ({
      id: network,
      name: this.config.services[network].name,
      subcategoryId: this.config.services[network].subcategoryId,
      prefixes: this.config.services[network].prefixes
    }));
  }

  /**
   * Validate transaction amount
   *
   * @param {number} amount - Amount to validate
   * @returns {Object} Validation result
   */
  validateAmount(amount) {
    const numAmount = parseFloat(amount);
    
    if (isNaN(numAmount) || numAmount <= 0) {
      return {
        isValid: false,
        error: 'Amount must be a positive number'
      };
    }

    if (numAmount < this.config.minAmount) {
      return {
        isValid: false,
        error: `Minimum amount is ₦${this.config.minAmount}`
      };
    }

    if (numAmount > this.config.maxAmount) {
      return {
        isValid: false,
        error: `Maximum amount is ₦${this.config.maxAmount}`
      };
    }

    return {
      isValid: true,
      amount: numAmount
    };
  }

  /**
   * Get configuration object (without sensitive data for logging)
   */
  getConfig() {
    return {
      ...this.config,
      // Remove sensitive data
      apiToken: '***HIDDEN***'
    };
  }

  /**
   * Get raw configuration (with sensitive data)
   * Use only when actually making API calls
   */
  getRawConfig() {
    return this.config;
  }
}

// Create singleton instance
const pluginNGConfig = new PluginNGConfig();

module.exports = pluginNGConfig;
