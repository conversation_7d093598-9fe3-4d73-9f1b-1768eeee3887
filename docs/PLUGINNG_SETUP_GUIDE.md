# PluginNG Integration Setup Guide

## Overview

This guide provides comprehensive instructions for setting up PluginNG as a backup airtime provider in the PayVendy VTU application. PluginNG serves as a reliable backup when VTpass is unavailable, ensuring continuous service availability.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Database Setup](#database-setup)
4. [Service Configuration](#service-configuration)
5. [Testing Integration](#testing-integration)
6. [Monitoring and Logging](#monitoring-and-logging)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

### 1. PluginNG Account Setup

- Create an account at [PluginNG](https://pluginng.com)
- Complete KYC verification
- Fund your PluginNG wallet
- Note your login credentials (email and password)
- **Important**: PluginNG uses login-based authentication with tokens that expire every 5-10 minutes

### 2. System Requirements

- Node.js 16+ 
- PostgreSQL 13+
- Supabase account (for database)
- Redis (for caching - optional)

### 3. Required Permissions

- Database admin access for schema updates
- Environment variable configuration access
- Server restart permissions

## Environment Configuration

### 1. Add PluginNG Credentials

Add the following environment variables to your `.env` file:

```bash
# =====================================================
# PLUGINNG API CONFIGURATION (BACKUP PROVIDER)
# =====================================================
# Login credentials for PluginNG API (tokens expire every 5-10 minutes)
PLUGINNG_EMAIL=<EMAIL>
PLUGINNG_PASSWORD=your-pluginng-password
PLUGINNG_BASE_URL=https://pluginng.com/api

# PluginNG security settings
PLUGINNG_TIMEOUT=30000
PLUGINNG_MAX_RETRIES=3
PLUGINNG_RATE_LIMIT_PER_MINUTE=60
PLUGINNG_MIN_AMOUNT=50
PLUGINNG_MAX_AMOUNT=50000
```

### 2. Environment Variables Explanation

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PLUGINNG_EMAIL` | Email for PluginNG account login | - | Yes |
| `PLUGINNG_PASSWORD` | Password for PluginNG account login | - | Yes |
| `PLUGINNG_BASE_URL` | PluginNG API base URL | https://pluginng.com/api | No |
| `PLUGINNG_TIMEOUT` | Request timeout in milliseconds | 30000 | No |
| `PLUGINNG_MAX_RETRIES` | Maximum retry attempts | 3 | No |
| `PLUGINNG_RATE_LIMIT_PER_MINUTE` | Rate limit per minute | 60 | No |
| `PLUGINNG_MIN_AMOUNT` | Minimum transaction amount | 50 | No |
| `PLUGINNG_MAX_AMOUNT` | Maximum transaction amount | 50000 | No |

## Database Setup

### 1. Run PluginNG Schema Extensions

Execute the PluginNG schema extensions in your Supabase SQL Editor:

```sql
-- Run this file: backend/database/pluginng-schema-extensions.sql
```

This will create:
- `pluginng_transaction_logs` table for API logging
- Performance indexes for PluginNG transactions
- Helper functions for statistics and monitoring
- Updated constraints to support PluginNG provider type

### 2. Verify Database Changes

Check that the following were created successfully:

```sql
-- Verify table creation
SELECT table_name FROM information_schema.tables 
WHERE table_name = 'pluginng_transaction_logs';

-- Verify indexes
SELECT indexname FROM pg_indexes 
WHERE indexname LIKE '%pluginng%';

-- Verify functions
SELECT routine_name FROM information_schema.routines 
WHERE routine_name LIKE '%pluginng%';
```

## Service Configuration

### 1. Provider Registration

The PluginNG provider is automatically registered in the VTU Provider Manager with:

- **Priority**: Backup (used when VTpass fails)
- **Status**: Active
- **Capabilities**: Airtime for all Nigerian networks
- **Networks**: MTN, GLO, Airtel, 9mobile

### 2. Network Mapping

PluginNG uses subcategory IDs for different networks:

| Network | Subcategory ID | Name |
|---------|----------------|------|
| MTN | 10 | MTN Nigeria |
| GLO | 11 | Glo Nigeria |
| Airtel | 12 | Airtel Nigeria |
| 9mobile | 13 | 9mobile Nigeria |

### 3. Failover Logic

The system automatically fails over to PluginNG when:
- VTpass API is unavailable
- VTpass returns server errors (5xx)
- VTpass request times out
- VTpass circuit breaker is open

## Testing Integration

### 1. Test Environment Setup

```bash
# Set test environment
NODE_ENV=development

# Use test credentials
PLUGINNG_API_TOKEN=test-token-here
```

### 2. Basic Connectivity Test

```javascript
// Test PluginNG configuration
const pluginNGConfig = require('./config/pluginng');
console.log('PluginNG Config:', pluginNGConfig.getConfig());

// Test service initialization
const pluginNGService = require('./services/pluginngService');
```

### 3. Test Airtime Purchase

```bash
# Test PluginNG login first
node scripts/test-pluginng-login.js

# Then test with curl
curl -X POST http://localhost:3000/api/v1/airtime/purchase \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "08012345678",
    "amount": 100,
    "network": "mtn"
  }'
```

### 4. Test Failover Scenario

To test failover from VTpass to PluginNG:

1. Temporarily disable VTpass by setting invalid credentials
2. Make an airtime purchase request
3. Verify that PluginNG is used as backup
4. Check logs for failover messages

## Monitoring and Logging

### 1. Log Monitoring

Monitor PluginNG operations in logs:

```bash
# Filter PluginNG logs
tail -f logs/app.log | grep "PLUGINNG"

# Monitor errors
tail -f logs/app.log | grep "PLUGINNG.*ERROR"
```

### 2. Database Monitoring

Check PluginNG transaction statistics:

```sql
-- Get PluginNG stats for last 24 hours
SELECT * FROM get_pluginng_stats();

-- Compare provider performance
SELECT * FROM compare_provider_performance();

-- Check pending transactions
SELECT * FROM get_pending_pluginng_transactions();
```

### 3. Performance Metrics

Monitor key metrics:
- Success rate
- Average response time
- Error rate by category
- Transaction volume
- Failover frequency

## Troubleshooting

### Common Issues

#### 1. "Missing required PluginNG environment variables"

**Solution:**
- Ensure `PLUGINNG_API_TOKEN` is set in `.env`
- Verify no typos in environment variable names
- Restart the application after adding variables

#### 2. "Invalid API token" or 401 errors

**Solution:**
- Verify token is correct and not expired
- Check PluginNG account status
- Ensure sufficient balance in PluginNG account

#### 3. "Network error" or timeout issues

**Solution:**
- Check internet connectivity
- Verify PluginNG API status
- Increase timeout value if needed
- Check firewall settings

#### 4. "Invalid phone number format"

**Solution:**
- Verify phone number is in Nigerian format
- Check network prefix validation
- Ensure phone number is 11 digits (starting with 0)

#### 5. "Provider circuit breaker open"

**Solution:**
- Check PluginNG service status
- Review error logs for provider failures
- Wait for circuit breaker to reset (usually 5 minutes)

#### 6. "Database connection error"

**Solution:**
- Verify Supabase credentials
- Check database schema migrations
- Ensure PluginNG schema extensions are applied

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
# Set debug level
LOG_LEVEL=debug

# Enable PluginNG debug logs
DEBUG=pluginng:*
```

### Health Checks

Implement health checks for PluginNG:

```javascript
// Check PluginNG service health
app.get('/health/pluginng', async (req, res) => {
  try {
    await pluginNGService.getBalance();
    res.json({ status: 'healthy', provider: 'pluginng' });
  } catch (error) {
    res.status(503).json({ 
      status: 'unhealthy', 
      provider: 'pluginng',
      error: error.message 
    });
  }
});
```

## Security Considerations

### 1. API Token Security

- Store tokens securely in environment variables
- Never commit tokens to version control
- Rotate tokens regularly
- Use different tokens for different environments

### 2. Request Validation

- Validate all input parameters
- Sanitize phone numbers
- Verify transaction amounts
- Check user permissions

### 3. Error Handling

- Don't expose sensitive information in error messages
- Log security events
- Implement rate limiting
- Monitor for suspicious activity

## Support

For additional support:

1. Check PluginNG API documentation
2. Review application logs
3. Contact PluginNG support team
4. Consult PayVendy development team

## Version History

- **v1.0.0** - Initial PluginNG integration
- Added backup provider functionality
- Implemented failover logic
- Added comprehensive error handling
