# PIN Setup & Verification Flow

## Overview
The PIN setup flow has been updated to handle both new PIN setup and existing PIN verification scenarios.

## API Endpoints

### 1. Check Setup Status
**GET** `/api/v1/setup/status`

**Response:**
```json
{
  "status": "success",
  "data": {
    "setupStatus": {
      "hasPinSetup": true,
      "hasBiometricSetup": false,
      "hasProfileSetup": true,
      "isEmailVerified": false,
      "isPhoneVerified": true,
      "setupComplete": true
    },
    "user": {
      "id": "user-id",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phoneNumber": "+1234567890",
      "picture": "https://...",
      "isEmailVerified": false,
      "isPhoneVerified": true
    }
  }
}
```

### 2. Set New PIN (First Time)
**POST** `/api/v1/setup/pin`

**Request:**
```json
{
  "pin": "1234",
  "confirmPin": "1234"
}
```

**Success Response:**
```json
{
  "status": "success",
  "message": "Transaction PIN set successfully",
  "data": {
    "pinSetup": true,
    "setupAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Response (PIN Already Set):**
```json
{
  "status": "error",
  "message": "PIN is already set. Please verify your existing PIN to continue.",
  "data": {
    "pinAlreadySet": true,
    "nextStep": "verify-pin",
    "user": {
      "id": "user-id",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phoneNumber": "+1234567890",
      "picture": "https://..."
    }
  }
}
```

### 3. Verify Existing PIN
**POST** `/api/v1/setup/verify-pin`

**Request:**
```json
{
  "pin": "1234"
}
```

**Success Response:**
```json
{
  "status": "success",
  "message": "PIN verified successfully",
  "data": {
    "pinVerified": true,
    "setupStatus": {
      "hasPinSetup": true,
      "hasBiometricSetup": false,
      "hasProfileSetup": true,
      "isEmailVerified": false,
      "isPhoneVerified": true,
      "setupComplete": true
    },
    "user": {
      "id": "user-id",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phoneNumber": "+1234567890",
      "picture": "https://...",
      "balance": "0.00"
    },
    "welcomeMessage": "Welcome back, John!"
  }
}
```

### 4. Change Existing PIN
**POST** `/api/v1/setup/change-pin`

**Request:**
```json
{
  "currentPin": "1234",
  "newPin": "5678",
  "confirmPin": "5678"
}
```

**Success Response:**
```json
{
  "status": "success",
  "message": "PIN changed successfully",
  "data": {
    "pinChanged": true,
    "changedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

## Frontend Flow Logic

### Step 1: Check Setup Status
```javascript
const setupStatus = await api.get('/setup/status');

if (setupStatus.data.setupStatus.hasPinSetup) {
  // PIN is already set - show PIN verification screen
  navigateToVerifyPin();
} else {
  // PIN not set - show PIN setup screen
  navigateToPinSetup();
}
```

### Step 2A: PIN Setup (New Users)
```javascript
try {
  const response = await api.post('/setup/pin', {
    pin: '1234',
    confirmPin: '1234'
  });
  
  // Success - PIN set
  showSuccess('PIN set successfully!');
  navigateToNextStep();
  
} catch (error) {
  if (error.response?.data?.data?.pinAlreadySet) {
    // PIN already exists - redirect to verification
    const userData = error.response.data.data.user;
    navigateToVerifyPin(userData);
  } else {
    showError(error.response?.data?.message || 'Failed to set PIN');
  }
}
```

### Step 2B: PIN Verification (Existing Users)
```javascript
try {
  const response = await api.post('/setup/verify-pin', {
    pin: '1234'
  });
  
  // Success - PIN verified
  const { user, welcomeMessage, setupStatus } = response.data.data;
  
  showWelcomeMessage(welcomeMessage);
  
  if (setupStatus.setupComplete) {
    // Setup is complete - go to main app
    navigateToMainApp(user);
  } else {
    // Continue with remaining setup steps
    navigateToNextSetupStep(setupStatus);
  }
  
} catch (error) {
  showError('Invalid PIN. Please try again.');
}
```

## UI/UX Recommendations

### PIN Setup Screen
- Title: "Set Up Your Transaction PIN"
- Subtitle: "Create a 4-digit PIN to secure your transactions"
- Show PIN input fields
- Show confirm PIN fields
- "Set PIN" button

### PIN Verification Screen
- Title: "Welcome Back!"
- Subtitle: "Enter your 4-digit PIN to continue"
- Show user's name: "Hello, [FirstName]!"
- Show PIN input field
- "Verify PIN" button
- "Forgot PIN?" link (optional)

### Success States
- PIN Setup: "PIN set successfully! ✅"
- PIN Verification: "Welcome back, [Name]! ✅"

### Error Handling
- Invalid PIN: "Invalid PIN. Please try again."
- PIN Mismatch: "PINs don't match. Please try again."
- Network Error: "Connection error. Please try again."

## Security Notes
- PINs are hashed using bcrypt before storage
- Failed attempts should be rate-limited
- Consider implementing PIN lockout after multiple failed attempts
- Always validate PIN format (4 digits) on both frontend and backend