# Complete Manual Provider Control Setup Guide

## Overview

This guide provides step-by-step instructions for setting up the complete manual provider control system for PayVendy VTU services. The system includes PluginNG as a backup provider with comprehensive admin dashboard controls.

## 🚀 Quick Start Checklist

- [ ] Database schema setup
- [ ] Environment variables configuration
- [ ] PluginNG API credentials
- [ ] Admin dashboard deployment
- [ ] Provider testing
- [ ] Admin user training

## 📋 Prerequisites

### 1. System Requirements
- Node.js 16+ 
- PostgreSQL 13+ (Supabase)
- Redis (optional, for caching)
- Admin user with `read_to_write` permissions

### 2. API Credentials
- **VTpass**: Username and password
- **PluginNG**: Bearer token from PluginNG dashboard

### 3. Access Requirements
- Supabase admin access
- Server deployment access
- Admin dashboard access

## 🗄️ Database Setup

### Step 1: Run Provider Management Schema

Execute the following SQL files in your Supabase SQL Editor:

```sql
-- 1. First, run the provider management schema
-- File: backend/database/provider-management-schema.sql

-- 2. Then run the PluginNG extensions
-- File: backend/database/pluginng-schema-extensions.sql
```

### Step 2: Verify Database Setup

```sql
-- Check tables were created
SELECT table_name FROM information_schema.tables 
WHERE table_name IN (
  'provider_status', 
  'provider_admin_actions', 
  'provider_performance_metrics',
  'provider_alerts',
  'pluginng_transaction_logs'
);

-- Check initial provider data
SELECT * FROM provider_status;

-- Check functions were created
SELECT routine_name FROM information_schema.routines 
WHERE routine_name LIKE '%provider%';
```

## ⚙️ Environment Configuration

### Step 1: Backend Environment Variables

Add to your `.env` file:

```bash
# =====================================================
# EXISTING VTPASS CONFIGURATION
# =====================================================
VTPASS_API_USERNAME=your-vtpass-username
VTPASS_API_PASSWORD=your-vtpass-password
VTPASS_BASE_URL=https://vtpass.com/api
VTPASS_TIMEOUT=30000
VTPASS_MAX_RETRIES=3
VTPASS_RATE_LIMIT_PER_MINUTE=60
VTPASS_MIN_AMOUNT=50
VTPASS_MAX_AMOUNT=50000

# =====================================================
# NEW PLUGINNG CONFIGURATION (BACKUP PROVIDER)
# =====================================================
PLUGINNG_API_TOKEN=your-pluginng-bearer-token-here
PLUGINNG_BASE_URL=https://pluginng.com/api
PLUGINNG_TIMEOUT=30000
PLUGINNG_MAX_RETRIES=3
PLUGINNG_RATE_LIMIT_PER_MINUTE=60
PLUGINNG_MIN_AMOUNT=50
PLUGINNG_MAX_AMOUNT=50000

# =====================================================
# DATABASE CONFIGURATION
# =====================================================
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# =====================================================
# APPLICATION CONFIGURATION
# =====================================================
NODE_ENV=production
PORT=3000
JWT_SECRET=your-jwt-secret
```

### Step 2: Verify Environment Variables

```bash
# Run the verification script
node backend/scripts/verify-pluginng-setup.js
```

## 🔧 Backend Deployment

### Step 1: Install Dependencies

```bash
cd backend
npm install
```

### Step 2: Start the Application

```bash
# Development
npm run dev

# Production
npm start
```

### Step 3: Verify API Endpoints

Test the new provider management endpoints:

```bash
# Get providers status
curl -X GET "http://localhost:3000/api/v1/admin/providers" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN"

# Switch provider (example)
curl -X POST "http://localhost:3000/api/v1/admin/providers/switch" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"provider_id": "pluginng", "reason": "Testing switch"}'
```

## 🎨 Frontend Deployment

### Step 1: Install Dependencies

```bash
cd admin-web
npm install
```

### Step 2: Build and Deploy

```bash
# Development
npm run dev

# Production build
npm run build
npm start
```

### Step 3: Verify Admin Dashboard

1. Navigate to `/providers` in admin dashboard
2. Verify provider cards are displayed
3. Test provider switching functionality
4. Check monitoring dashboard at `/providers/monitoring`

## 🧪 Testing the Setup

### Step 1: Provider Status Test

```bash
# Run comprehensive tests
node backend/scripts/test-pluginng-integration.js
```

### Step 2: Manual Testing Checklist

#### Provider Management
- [ ] View provider status cards
- [ ] Switch primary provider (VTpass ↔ PluginNG)
- [ ] Enable/disable providers
- [ ] View provider configuration
- [ ] Check admin actions log

#### Transaction Testing
- [ ] Test airtime purchase with VTpass
- [ ] Switch to PluginNG and test purchase
- [ ] Disable all providers and verify error message
- [ ] Re-enable provider and test again

#### Monitoring Dashboard
- [ ] View real-time provider statistics
- [ ] Check success rate monitoring
- [ ] Verify response time tracking
- [ ] Test provider comparison table

#### Alert System
- [ ] View active alerts (if any)
- [ ] Test alert acknowledgment
- [ ] Test alert resolution

## 👥 Admin User Training

### Key Concepts

1. **Manual Control**: No automatic failover - admins control everything
2. **Primary Provider**: Only one provider can be primary at a time
3. **Backup Provider**: PluginNG serves as backup when VTpass is unavailable
4. **Audit Trail**: All actions are logged with timestamps and reasons

### Common Operations

#### Switching Providers
```
1. Go to Admin Dashboard → Providers
2. Find the provider you want to make primary
3. Click "Make Primary" button
4. Enter reason (optional but recommended)
5. Confirm the switch
6. Verify new primary provider is active
```

#### Handling Provider Issues
```
1. If transactions start failing:
   - Check provider status in dashboard
   - Look for alerts or error messages
   - Consider switching to backup provider
   
2. If provider is down:
   - Disable the problematic provider
   - Switch to working provider
   - Monitor for recovery
   
3. For maintenance:
   - Switch to backup provider first
   - Disable provider under maintenance
   - Re-enable after maintenance complete
```

#### Monitoring Best Practices
```
1. Check dashboard daily for:
   - Provider health status
   - Success rates
   - Active alerts
   - Transaction volumes
   
2. Weekly review:
   - Provider performance comparison
   - Error rate trends
   - Admin action logs
   
3. Monthly analysis:
   - Cost optimization opportunities
   - Performance improvements
   - Capacity planning
```

## 🚨 Troubleshooting

### Common Issues

#### "No provider is currently enabled"
**Solution:**
1. Go to Admin Dashboard → Providers
2. Enable at least one provider
3. Make sure one provider is set as primary

#### "Provider circuit breaker open"
**Solution:**
1. Check provider health status
2. Wait for circuit breaker to reset (5 minutes)
3. Or manually switch to another provider

#### "Invalid API token" errors
**Solution:**
1. Verify environment variables are set correctly
2. Check API credentials with provider
3. Restart application after updating credentials

#### Database connection errors
**Solution:**
1. Verify Supabase credentials
2. Check database schema is properly set up
3. Ensure all required tables exist

### Debug Mode

Enable detailed logging:

```bash
# Set environment variables
LOG_LEVEL=debug
DEBUG=provider:*

# Restart application
npm restart
```

### Health Checks

Monitor application health:

```bash
# Check provider health
curl http://localhost:3000/health/providers

# Check database connectivity
curl http://localhost:3000/health/database
```

## 📊 Monitoring and Alerts

### Key Metrics to Monitor

1. **Provider Health**
   - Response times
   - Success rates
   - Error rates
   - Uptime percentage

2. **Transaction Metrics**
   - Volume trends
   - Failure patterns
   - Peak usage times
   - Revenue impact

3. **System Health**
   - API response times
   - Database performance
   - Error logs
   - Resource usage

### Setting Up Alerts

Configure alerts for:
- Provider failures
- High error rates
- Slow response times
- System downtime

## 🔒 Security Considerations

### API Security
- Use HTTPS for all communications
- Rotate API tokens regularly
- Monitor for suspicious activity
- Implement rate limiting

### Admin Access
- Use strong authentication
- Limit admin permissions
- Log all admin actions
- Regular access reviews

### Data Protection
- Encrypt sensitive data
- Secure API credentials
- Regular security audits
- Backup procedures

## 📈 Performance Optimization

### Database Optimization
- Regular index maintenance
- Query performance monitoring
- Connection pooling
- Backup strategies

### API Performance
- Response time monitoring
- Caching strategies
- Load balancing
- Error handling

### Monitoring Tools
- Application performance monitoring
- Database monitoring
- Infrastructure monitoring
- Business metrics tracking

## 🆘 Support and Maintenance

### Regular Maintenance Tasks

**Daily:**
- Check provider status
- Review error logs
- Monitor transaction volumes

**Weekly:**
- Review performance metrics
- Check for system updates
- Backup verification

**Monthly:**
- Security review
- Performance analysis
- Capacity planning
- Documentation updates

### Getting Help

1. **Documentation**: Check `/docs/` folder for detailed guides
2. **Logs**: Review application logs for error details
3. **Monitoring**: Use dashboard metrics for troubleshooting
4. **Support**: Contact development team for complex issues

### Emergency Procedures

**Provider Outage:**
1. Switch to backup provider immediately
2. Notify stakeholders
3. Monitor backup provider performance
4. Document incident for review

**System Outage:**
1. Check system health endpoints
2. Review error logs
3. Restart services if needed
4. Escalate to technical team

## ✅ Success Criteria

The setup is successful when:

- [ ] Both providers (VTpass and PluginNG) are configured and active
- [ ] Admin dashboard shows real-time provider status
- [ ] Provider switching works without issues
- [ ] Transactions process successfully with selected provider
- [ ] Monitoring dashboard displays accurate metrics
- [ ] Admin actions are properly logged
- [ ] Alert system functions correctly
- [ ] All tests pass successfully

## 🎯 Next Steps

After successful setup:

1. **Train Admin Users**: Ensure all admin users understand the new system
2. **Monitor Performance**: Track system performance for the first week
3. **Optimize Settings**: Adjust timeouts and limits based on usage
4. **Plan Maintenance**: Schedule regular maintenance windows
5. **Review Security**: Conduct security review of the new system

## 📞 Support Contacts

- **Technical Issues**: Development Team
- **Provider Issues**: VTpass/PluginNG Support
- **Database Issues**: Database Administrator
- **Security Issues**: Security Team

---

**Version**: 1.0.0  
**Last Updated**: 2025-07-11  
**Next Review**: 2025-08-11
