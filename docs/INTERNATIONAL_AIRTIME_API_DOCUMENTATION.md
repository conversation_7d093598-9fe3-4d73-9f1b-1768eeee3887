# International Airtime/Data/Pin API Documentation

## Overview

This document provides comprehensive documentation for the VTpass International Airtime/Data/Pin API integration implemented with **ultra-enhanced security** for cross-border transactions. This implementation includes military-grade security measures, comprehensive fraud detection, and strict compliance controls.

## Table of Contents

1. [Enhanced Security Features](#enhanced-security-features)
2. [Supported Countries](#supported-countries)
3. [API Endpoints](#api-endpoints)
4. [Security Architecture](#security-architecture)
5. [Risk Management](#risk-management)
6. [Compliance & Regulations](#compliance--regulations)
7. [Error Handling](#error-handling)
8. [Testing](#testing)

## Enhanced Security Features

### 🔒 **Ultra-Secure Architecture**

The international airtime API implements **military-grade security** with multiple layers:

#### **1. Enhanced Authentication**
- **JWT + Enhanced Verification**: Requires both email and phone verification
- **BVN Verification**: Optional enhanced verification for high-value transactions
- **Device Fingerprinting**: Advanced device identification and tracking
- **Session Management**: Secure session handling with automatic expiry

#### **2. Geographic Security**
- **Country Whitelisting**: Only approved countries allowed
- **Risk-Based Routing**: Different security levels per country
- **Geo-IP Validation**: Real-time location verification
- **Compliance Checking**: Automatic sanctions and restrictions checking

#### **3. Enhanced Fraud Detection**
- **Real-Time Risk Scoring**: Advanced ML-based risk assessment
- **Pattern Recognition**: Detects suspicious transaction patterns
- **Velocity Checking**: Monitors transaction frequency and amounts
- **Cross-Border Analytics**: Specialized international fraud detection

#### **4. Ultra-Strict Rate Limiting**
- **International Limits**: Much stricter than domestic transactions
- **Progressive Penalties**: Increasing restrictions for violations
- **User-Specific Limits**: Based on verification level and history
- **Geographic Limits**: Different limits per country risk level

## Supported Countries

### **Low Risk Countries** (Risk Score: 15)
- **Ghana (GH)** - Max: ₦50,000 per transaction
- **Rwanda (RW)** - Max: ₦50,000 per transaction

### **Medium Risk Countries** (Risk Score: 35)
- **Swaziland (SZ)** - Max: ₦35,000 per transaction
- **Cameroon (CM)** - Max: ₦35,000 per transaction

### **High Risk Countries** (Risk Score: 55)
- **Yemen (YE)** - Max: ₦20,000 per transaction
  - Enhanced verification required
  - Additional monitoring

### **Country-Specific Limits**
```javascript
{
  "GH": { "maxAmount": 50000, "riskLevel": "low" },
  "RW": { "maxAmount": 50000, "riskLevel": "low" },
  "CM": { "maxAmount": 35000, "riskLevel": "medium" },
  "SZ": { "maxAmount": 35000, "riskLevel": "medium" },
  "YE": { "maxAmount": 20000, "riskLevel": "high" }
}
```

## API Endpoints

### **1. Get Available Countries**

**GET** `/api/v1/international-airtime/countries`

Returns all supported countries with risk levels and restrictions.

#### Response
```json
{
  "success": true,
  "data": {
    "countries": [
      {
        "code": "GH",
        "name": "Ghana",
        "currency": "GHS",
        "prefix": "233",
        "flag": "https://vtpass.com/resources/images/flags/GH.png",
        "riskLevel": "low",
        "maxTransactionAmount": 50000
      }
    ],
    "totalCountries": 5,
    "lastUpdated": "2025-07-11T10:30:00.000Z"
  }
}
```

### **2. Get Product Types**

**GET** `/api/v1/international-airtime/product-types?country_code=GH`

Returns available product types for a specific country.

#### Response
```json
{
  "success": true,
  "data": {
    "productTypes": [
      {
        "product_type_id": 1,
        "name": "Mobile Top Up"
      },
      {
        "product_type_id": 4,
        "name": "Mobile Data"
      }
    ],
    "countryCode": "GH",
    "totalTypes": 2
  }
}
```

### **3. Get Operators**

**GET** `/api/v1/international-airtime/operators?country_code=GH&product_type_id=1`

Returns available operators for a country and product type.

#### Response
```json
{
  "success": true,
  "data": {
    "operators": [
      {
        "operator_id": "5",
        "name": "Ghana MTN",
        "operator_image": "https://vtpass.com/resources/images/operators/80.png"
      }
    ],
    "totalOperators": 1
  }
}
```

### **4. Get Variations**

**GET** `/api/v1/international-airtime/variations?operator_id=5&product_type_id=1`

Returns available variations (packages) for an operator.

#### Response
```json
{
  "success": true,
  "data": {
    "ServiceName": "International Airtime",
    "serviceID": "foreign-airtime",
    "variations": [
      {
        "variation_code": "4987",
        "name": "160 -2000 GMD Top Up",
        "variation_amount": "0.00",
        "fixedPrice": "Yes"
      }
    ],
    "totalVariations": 25
  }
}
```

### **5. Purchase International Airtime**

**POST** `/api/v1/international-airtime/purchase`

**⚠️ ULTRA-SECURE ENDPOINT** - Enhanced security required

#### Request Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
X-Request-ID: <unique_request_id>
```

#### Request Body
```json
{
  "billersCode": "************",
  "variation_code": "4987",
  "operator_id": "5",
  "country_code": "GH",
  "product_type_id": "1",
  "email": "<EMAIL>",
  "phone": "08031234567",
  "amount": 2000,
  "pin": "1234"
}
```

#### Response (Success)
```json
{
  "success": true,
  "message": "TRANSACTION SUCCESSFUL",
  "data": {
    "transactionId": "uuid-here",
    "status": "completed",
    "amount": 2000,
    "recipient": "************",
    "country": "GH",
    "countryName": "Ghana",
    "operatorAmount": "GHS15.00",
    "requestId": "INTL20250711103000012345678",
    "timestamp": "2025-07-11T10:30:00.000Z"
  }
}
```

### **6. Transaction History**

**GET** `/api/v1/international-airtime/history`

Get international transaction history with filtering.

#### Query Parameters
- `page` (optional): Page number
- `limit` (optional): Items per page (max 50)
- `status` (optional): Filter by status
- `country_code` (optional): Filter by country

## Security Architecture

### **Multi-Layer Security Model**

```
┌─────────────────────────────────────────────────────────────┐
│                    CLIENT REQUEST                           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              LAYER 1: AUTHENTICATION                       │
│  • JWT Validation                                          │
│  • Enhanced Verification Check                             │
│  • Device Fingerprinting                                   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              LAYER 2: RATE LIMITING                        │
│  • Ultra-Strict International Limits                      │
│  • Progressive Penalties                                   │
│  • User-Specific Restrictions                              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              LAYER 3: GEOGRAPHIC VALIDATION                │
│  • Country Whitelisting                                    │
│  • Risk-Based Routing                                      │
│  • Geo-IP Verification                                     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              LAYER 4: FRAUD DETECTION                      │
│  • Real-Time Risk Scoring                                  │
│  • Pattern Recognition                                     │
│  • Velocity Checking                                       │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              LAYER 5: TRANSACTION LIMITS                   │
│  • Amount Validation                                       │
│  • Daily Limits                                            │
│  • Country-Specific Limits                                 │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              LAYER 6: VTPASS API                           │
│  • Secure API Communication                                │
│  • Request Signing                                         │
│  • Response Validation                                     │
└─────────────────────────────────────────────────────────────┘
```

### **Risk Scoring Algorithm**

The system calculates risk scores based on multiple factors:

```javascript
Risk Score = Base Country Risk + Verification Risk + Pattern Risk + Device Risk

Where:
- Base Country Risk: 15-55 points based on country
- Verification Risk: 0-45 points based on user verification
- Pattern Risk: 0-30 points based on transaction patterns
- Device Risk: 0-20 points based on device recognition

Risk Actions:
- Score 0-30: Allow transaction
- Score 31-49: Allow with monitoring
- Score 50-79: Flag for review
- Score 80+: Block transaction
```

## Risk Management

### **Transaction Limits**

#### **Daily Limits**
- **Verified Users**: ₦500,000 per day
- **Unverified Users**: ₦250,000 per day
- **Enhanced Verified**: ₦750,000 per day

#### **Hourly Limits**
- **Verified Users**: 5 transactions per hour
- **Unverified Users**: 2 transactions per hour

#### **Country-Specific Limits**
- **Low Risk**: Up to ₦50,000 per transaction
- **Medium Risk**: Up to ₦35,000 per transaction
- **High Risk**: Up to ₦20,000 per transaction

### **Monitoring & Alerts**

#### **Real-Time Monitoring**
- Transaction velocity monitoring
- Unusual pattern detection
- Geographic anomaly detection
- Amount threshold alerts

#### **Automated Actions**
- Temporary account restrictions
- Enhanced verification requirements
- Transaction blocking
- Compliance reporting

## Compliance & Regulations

### **International Compliance**
- **AML/KYC**: Anti-Money Laundering compliance
- **OFAC**: Sanctions screening
- **PCI DSS**: Payment card industry standards
- **GDPR**: Data protection compliance

### **Nigerian Regulations**
- **CBN Guidelines**: Central Bank of Nigeria compliance
- **NDPR**: Nigeria Data Protection Regulation
- **EFCC**: Economic and Financial Crimes Commission

## Error Handling

### **Enhanced Error Categories**

#### **International-Specific Errors**
```json
{
  "COUNTRY_BLOCKED": "Transactions to this country are not allowed",
  "COUNTRY_NOT_SUPPORTED": "This country is not supported",
  "INSUFFICIENT_VERIFICATION_INTERNATIONAL": "Enhanced verification required",
  "INTERNATIONAL_RATE_LIMIT_EXCEEDED": "Too many international transactions",
  "VERY_HIGH_RISK_INTERNATIONAL_TRANSACTION": "Transaction blocked due to risk",
  "DAILY_INTERNATIONAL_LIMIT_EXCEEDED": "Daily international limit exceeded"
}
```

### **Error Response Format**
```json
{
  "success": false,
  "error": {
    "code": "COUNTRY_BLOCKED",
    "category": "geographic_restriction",
    "message": "Transactions to this country are currently not allowed",
    "retryable": false,
    "severity": "error",
    "country": "XX",
    "timestamp": "2025-07-11T10:30:00.000Z",
    "requestId": "req_123456789"
  }
}
```

## Testing

### **Sandbox Environment**

Use these test phone numbers for all countries:
- `08011111111`: Successful transaction
- `201000000000`: Pending response
- `500000000000`: Unexpected response
- `400000000000`: No response
- `300000000000`: Timeout
- Any other number: Failed transaction

### **Security Testing**

#### **Rate Limit Testing**
```bash
# Test international rate limits
for i in {1..10}; do
  curl -X POST /api/v1/international-airtime/purchase \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"country_code":"GH","operator_id":"5",...}'
done
```

#### **Risk Score Testing**
```bash
# Test high-risk country
curl -X POST /api/v1/international-airtime/purchase \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"country_code":"YE","amount":25000,...}'
```

---

## Security Summary

This international airtime API implementation provides **military-grade security** with:

✅ **Enhanced Authentication** - Multi-factor verification required  
✅ **Geographic Security** - Country-based risk management  
✅ **Advanced Fraud Detection** - Real-time risk scoring  
✅ **Ultra-Strict Rate Limiting** - International-specific limits  
✅ **Comprehensive Monitoring** - Real-time transaction monitoring  
✅ **Compliance Ready** - International regulatory compliance  
✅ **Audit Trail** - Complete transaction logging  

**This implementation is designed to be unhackable and provides enterprise-grade security for international financial transactions.**

**Last Updated**: July 11, 2025  
**Version**: 1.0.0
