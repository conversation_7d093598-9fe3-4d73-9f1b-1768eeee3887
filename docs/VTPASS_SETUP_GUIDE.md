# VTpass MTN VTU API Setup Guide

## Overview

This guide provides step-by-step instructions for setting up and deploying the VTpass MTN VTU API integration in your PayVendy backend system.

## Prerequisites

- Node.js 16+ installed
- PostgreSQL database (Supabase)
- VTpass API account and credentials
- PayVendy backend system running

## Step 1: Environment Configuration

### 1.1 Update Environment Variables

Add the following variables to your `.env` file:

```bash
# VTpass API Configuration
VTPASS_API_KEY=your-vtpass-api-key-here
VTPASS_SECRET_KEY=your-vtpass-secret-key-here
VTPASS_PUBLIC_KEY=your-vtpass-public-key-here
VTPASS_BASE_URL=https://vtpass.com/api
VTPASS_SANDBOX_URL=https://sandbox.vtpass.com/api
VTPASS_TIMEOUT=30000
VTPASS_MAX_RETRIES=3
VTPASS_RATE_LIMIT_PER_MINUTE=60
VTPASS_MIN_AMOUNT=50
VTPASS_MAX_AMOUNT=50000

# Security Configuration
MAX_DAILY_PAYMENT_AMOUNT=1000000
MAX_TRANSACTION_AMOUNT=100000
MAX_TRANSACTIONS_PER_HOUR=20
MAX_TRANSACTIONS_PER_DAY=50
SUSPICIOUS_AMOUNT_THRESHOLD=50000
PAYMENT_SIGNATURE_SECRET=your-secure-signature-secret-here

# Provider Management
BACKUP_VTU_PROVIDER=none
BACKUP_VTU_API_KEY=your-backup-provider-api-key-here
BACKUP_VTU_SECRET_KEY=your-backup-provider-secret-key-here

# Security Features
ALLOWED_COUNTRIES=NG
BLOCKED_COUNTRIES=
REQUIRE_DEVICE_VERIFICATION=false
ENABLE_GEO_BLOCKING=false
```

### 1.2 Get VTpass Credentials

1. Sign up for a VTpass account at [https://vtpass.com](https://vtpass.com)
2. Complete the verification process
3. Navigate to API settings in your dashboard
4. Generate your API credentials:
   - API Key
   - Secret Key
   - Public Key
5. For testing, use the sandbox environment first

## Step 2: Database Setup

### 2.1 Run Database Migrations

Execute the VTpass schema extensions:

```bash
# Connect to your Supabase database and run:
psql -h your-supabase-host -U postgres -d postgres -f backend/database/vtpass-schema-extensions.sql
```

Or use the Supabase SQL editor to execute the contents of `vtpass-schema-extensions.sql`.

### 2.2 Verify Database Changes

Check that the following tables and columns were created:

```sql
-- Verify new columns in transactions table
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'transactions' 
AND column_name IN (
  'provider_type', 
  'provider_transaction_id', 
  'commission_amount', 
  'total_amount', 
  'retry_count', 
  'completed_at', 
  'failure_reason', 
  'ip_address'
);

-- Verify new vtpass_transaction_logs table
SELECT table_name 
FROM information_schema.tables 
WHERE table_name = 'vtpass_transaction_logs';
```

## Step 3: Install Dependencies

The VTpass integration uses existing dependencies, but verify these are installed:

```bash
npm install axios crypto uuid express-validator express-rate-limit bcryptjs
```

## Step 4: Test the Integration

### 4.1 Start the Server

```bash
cd backend
npm start
```

### 4.2 Test Authentication

First, ensure you have a valid JWT token by logging in:

```bash
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "pin": "your-pin"
  }'
```

### 4.3 Test Provider Information

```bash
curl -X GET http://localhost:8000/api/v1/airtime/providers \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Expected response:
```json
{
  "success": true,
  "data": {
    "providers": [
      {
        "id": "mtn",
        "name": "MTN Nigeria",
        "available": true,
        "minAmount": 50,
        "maxAmount": 50000
      }
    ]
  }
}
```

### 4.4 Test Phone Validation

```bash
curl -X GET "http://localhost:8000/api/v1/airtime/validate-phone?phone=08031234567" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4.5 Test Airtime Purchase (Sandbox)

**Important**: Use sandbox environment for testing first!

```bash
curl -X POST http://localhost:8000/api/v1/airtime/purchase \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -H "X-Request-ID: test-$(date +%s)" \
  -d '{
    "phone": "08011111111",
    "amount": 100,
    "saveRecipient": false
  }'
```

Use these test numbers in sandbox:
- `08011111111`: Successful transaction
- `201000000000`: Pending transaction
- `500000000000`: Unexpected response
- `400000000000`: No response
- `300000000000`: Timeout
- Any other number: Failed transaction

## Step 5: Security Configuration

### 5.1 Configure Rate Limiting

Adjust rate limits based on your requirements:

```javascript
// In backend/middleware/paymentSecurity.js
const config = {
  maxDailyAmount: 1000000, // ₦1M daily limit
  maxTransactionAmount: 100000, // ₦100K per transaction
  maxTransactionsPerHour: 20, // 20 transactions per hour
  maxTransactionsPerDay: 50, // 50 transactions per day
};
```

### 5.2 Configure Fraud Detection

Update fraud detection thresholds:

```javascript
// Suspicious amount threshold
suspiciousAmountThreshold: 50000, // ₦50K

// Risk scoring thresholds
// riskScore >= 70: Block transaction
// riskScore >= 40: Flag for monitoring
```

### 5.3 Set Up IP Whitelisting (Optional)

For production environments, consider IP whitelisting:

```bash
# Add to .env
ALLOWED_IPS=***********/24,10.0.0.0/8
```

## Step 6: Monitoring Setup

### 6.1 Configure Logging

Ensure proper log levels are set:

```bash
# In .env
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
```

### 6.2 Set Up Error Tracking

Configure Sentry for error tracking:

```bash
# In .env
SENTRY_DSN=your-sentry-dsn-here
```

### 6.3 Monitor Provider Health

The system automatically monitors provider health. Check logs for:

```
✅ [VTU_PROVIDER_MANAGER] Provider manager initialized
💓 [VTU_PROVIDER_MANAGER] Health check completed
🚫 [VTU_PROVIDER_MANAGER] Circuit breaker opened
```

## Step 7: Production Deployment

### 7.1 Switch to Production Environment

Update your `.env` file:

```bash
NODE_ENV=production
VTPASS_BASE_URL=https://vtpass.com/api
MOCK_VTPASS=false
```

### 7.2 Security Hardening

1. **Enable HTTPS**: Ensure SSL certificates are properly configured
2. **Secure Headers**: Configure security headers in your reverse proxy
3. **Database Security**: Use connection pooling and read replicas
4. **API Keys**: Rotate API keys regularly
5. **Monitoring**: Set up alerts for failed transactions

### 7.3 Load Testing

Before going live, perform load testing:

```bash
# Install artillery for load testing
npm install -g artillery

# Create test script (artillery-test.yml)
config:
  target: 'http://localhost:8000'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Airtime Purchase"
    requests:
      - post:
          url: "/api/v1/airtime/purchase"
          headers:
            Authorization: "Bearer YOUR_JWT_TOKEN"
          json:
            phone: "08011111111"
            amount: 100

# Run load test
artillery run artillery-test.yml
```

## Step 8: Backup Provider Setup (Future)

When implementing a backup provider:

### 8.1 Provider Registration

```javascript
// In backend/services/vtuProviderManager.js
this.registerProvider({
  id: 'backup_provider',
  name: 'Backup Provider Name',
  priority: PROVIDER_PRIORITY.SECONDARY,
  status: PROVIDER_STATUS.ACTIVE,
  service: backupProviderService, // Implement this
  config: {
    baseUrl: process.env.BACKUP_VTU_BASE_URL,
    apiKey: process.env.BACKUP_VTU_API_KEY,
    // ... other config
  }
});
```

### 8.2 Service Implementation

Create a service similar to `vtpassService.js` for the backup provider.

## Troubleshooting

### Common Issues

1. **"Missing required VTpass environment variables"**
   - Ensure all VTpass credentials are set in `.env`
   - Check for typos in environment variable names

2. **"Invalid phone number format"**
   - Verify phone number is in Nigerian format
   - Check MTN prefix validation

3. **"Rate limit exceeded"**
   - Check rate limiting configuration
   - Verify user verification status

4. **"Provider circuit breaker open"**
   - Check VTpass service status
   - Review error logs for provider failures

5. **"Database connection error"**
   - Verify Supabase credentials
   - Check database schema migrations

### Debug Mode

Enable debug logging:

```bash
# In .env
DEBUG_MODE=true
LOG_LEVEL=debug
```

### Health Check Endpoint

Monitor system health:

```bash
curl -X GET http://localhost:8000/api/v1/admin/dashboard/system-health \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN"
```

## Support

For additional support:

1. Check the comprehensive API documentation: `VTPASS_API_DOCUMENTATION.md`
2. Review inline code comments
3. Check application logs for detailed error information
4. Contact the development team for complex issues

## Security Checklist

Before going live, ensure:

- [ ] All API credentials are secure and not exposed
- [ ] Rate limiting is properly configured
- [ ] Fraud detection is enabled and tuned
- [ ] HTTPS is enforced
- [ ] Database access is secured
- [ ] Error messages don't expose sensitive information
- [ ] Logging captures security events
- [ ] Monitoring and alerting are configured
- [ ] Backup and recovery procedures are tested

---

**Setup Complete!** Your VTpass MTN VTU API integration is now ready for production use.

**Last Updated**: July 11, 2025
**Version**: 1.0.0
