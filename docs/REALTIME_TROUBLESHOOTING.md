# Supabase Realtime Troubleshooting Guide

## Issue: CHANNEL_ERROR when subscribing to user deletion events

### Error Message
```
{"timestamp":"2025-06-30T17:03:39.371Z","level":"ERROR","message":"Failed to subscribe to user deletion events","status":"CHANNEL_ERROR"}
```

### Root Cause
The `CHANNEL_ERROR` occurs when Supabase Realtime cannot establish a subscription to database changes. This is commonly due to:

1. **Realtime not enabled** for the target table
2. **Row Level Security (RLS)** policies blocking access
3. **Service role permissions** insufficient
4. **Quota limits** exceeded
5. **Network/firewall** issues

## Quick Diagnosis

Run the verification script to identify the specific issue:

```bash
cd backend
npm run verify-realtime
```

## Solutions

### 1. Enable Realtime for Users Table

**Steps:**
1. Go to your Supabase Dashboard
2. Navigate to **Database > Replication**
3. Find the `users` table
4. Toggle **Enable Realtime** to ON
5. Save changes

### 2. Check Row Level Security (RLS)

**Option A: Disable RLS (for testing)**
```sql
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
```

**Option B: Create policy for service role**
```sql
-- Allow service role to read all users for deletion detection
CREATE POLICY "Service role can read users" ON users
FOR SELECT USING (auth.role() = 'service_role');

-- Allow service role to detect deletions
CREATE POLICY "Service role can detect deletions" ON users
FOR DELETE USING (auth.role() = 'service_role');
```

### 3. Verify Service Role Key

Ensure your `.env` file has the correct service role key:

```env
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

**To find your service role key:**
1. Go to Supabase Dashboard
2. Navigate to **Settings > API**
3. Copy the `service_role` key (not the `anon` key)

### 4. Check Realtime Quotas

1. Go to **Settings > Usage** in Supabase Dashboard
2. Check **Realtime** section for:
   - Concurrent connections
   - Messages per second
   - Channel joins per second

If limits are exceeded, consider upgrading your plan or optimizing usage.

### 5. Network/Firewall Issues

**For development:**
- Ensure WebSocket connections are allowed
- Check if corporate firewall blocks realtime
- Try from a different network

**For production:**
- Whitelist Supabase realtime endpoints
- Ensure load balancer supports WebSockets

## Fallback Mechanism

The system automatically falls back to polling when realtime fails:

- **Polling interval:** 30 seconds
- **Method:** Checks for user deletions periodically
- **Performance:** Less real-time but more reliable

## Testing the Fix

After implementing solutions:

1. **Restart the server:**
   ```bash
   npm start
   ```

2. **Run verification:**
   ```bash
   npm run verify-realtime
   ```

3. **Check logs** for successful subscription:
   ```
   Successfully subscribed to user deletion events
   ```

## Advanced Debugging

### Enable Supabase Debug Logs

Add to your initialization:

```javascript
const supabase = createClient(url, key, {
  realtime: {
    log_level: 'debug'
  }
});
```

### Monitor WebSocket Connection

In browser developer tools:
1. Go to **Network** tab
2. Filter by **WS** (WebSocket)
3. Look for realtime connections
4. Check connection status and messages

### Check Supabase Logs

1. Go to Supabase Dashboard
2. Navigate to **Logs > Realtime**
3. Look for error messages around the timestamp

## Production Recommendations

1. **Monitor realtime health** with status endpoints
2. **Set up alerts** for CHANNEL_ERROR events
3. **Implement circuit breaker** pattern
4. **Use polling as primary** if realtime is unreliable
5. **Regular quota monitoring** to prevent limits

## Support

If issues persist:

1. **Check Supabase Status:** https://status.supabase.com/
2. **Community Support:** https://github.com/supabase/supabase/discussions
3. **Documentation:** https://supabase.com/docs/guides/realtime

## Related Files

- `backend/services/realtimeService.js` - Main realtime service
- `backend/scripts/verifyRealtimeSetup.js` - Verification script
- `backend/config/database.js` - Database configuration