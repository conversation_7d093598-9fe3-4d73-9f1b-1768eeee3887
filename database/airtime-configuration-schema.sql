-- =====================================================
-- AIRTIME CONFIGURATION SCHEMA
-- =====================================================
-- Database schema for airtime service configuration management
-- Allows admin to manage all airtime settings through dashboard
-- 
-- Author: PayVendy Development Team
-- Version: 1.0.0
-- Date: 2025-07-11

-- =====================================================
-- AIRTIME SETTINGS TABLE
-- =====================================================

-- Table to store global airtime configuration settings
CREATE TABLE IF NOT EXISTS airtime_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value JSONB NOT NULL,
    setting_type VARCHAR(50) NOT NULL, -- 'limits', 'pricing', 'network', 'security', 'system'
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Metadata
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- NETWORK CONFIGURATIONS TABLE
-- =====================================================

-- Table to store network-specific configurations
CREATE TABLE IF NOT EXISTS network_configurations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    network_id VARCHAR(20) NOT NULL, -- 'mtn', 'glo', 'airtel', 'etisalat'
    network_name VARCHAR(100) NOT NULL,
    provider_id VARCHAR(50) NOT NULL, -- 'vtpass', 'pluginng'
    
    -- Network settings
    is_enabled BOOLEAN DEFAULT TRUE,
    min_amount DECIMAL(10,2) DEFAULT 50.00,
    max_amount DECIMAL(10,2) DEFAULT 50000.00,
    service_id VARCHAR(50), -- Provider-specific service ID
    
    -- Pricing and commission
    commission_rate DECIMAL(5,4) DEFAULT 0.0000, -- e.g., 0.0250 = 2.5%
    processing_fee DECIMAL(10,2) DEFAULT 0.00,
    
    -- Performance settings
    timeout_seconds INTEGER DEFAULT 30,
    max_retries INTEGER DEFAULT 3,
    retry_delay_ms INTEGER DEFAULT 1000,
    
    -- Network prefixes (JSON array)
    supported_prefixes JSONB DEFAULT '[]'::jsonb,
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    last_tested TIMESTAMP WITH TIME ZONE,
    test_result JSONB,
    
    -- Audit fields
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(network_id, provider_id)
);

-- =====================================================
-- AIRTIME ADMIN ACTIONS TABLE
-- =====================================================

-- Table to log all admin configuration changes
CREATE TABLE IF NOT EXISTS airtime_admin_actions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    admin_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    admin_email VARCHAR(255),
    action_type VARCHAR(50) NOT NULL, -- 'update_limits', 'update_network', 'update_pricing', 'toggle_network'
    target_type VARCHAR(50) NOT NULL, -- 'global_setting', 'network_config', 'provider_setting'
    target_id VARCHAR(100), -- ID of the affected setting/network
    
    -- Action details
    action_description TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    
    -- Request context
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES
-- =====================================================

-- Airtime settings indexes
CREATE INDEX IF NOT EXISTS idx_airtime_settings_key ON airtime_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_airtime_settings_type ON airtime_settings(setting_type);
CREATE INDEX IF NOT EXISTS idx_airtime_settings_active ON airtime_settings(is_active);

-- Network configurations indexes
CREATE INDEX IF NOT EXISTS idx_network_config_network_id ON network_configurations(network_id);
CREATE INDEX IF NOT EXISTS idx_network_config_provider_id ON network_configurations(provider_id);
CREATE INDEX IF NOT EXISTS idx_network_config_enabled ON network_configurations(is_enabled);
CREATE INDEX IF NOT EXISTS idx_network_config_status ON network_configurations(status);

-- Admin actions indexes
CREATE INDEX IF NOT EXISTS idx_airtime_admin_actions_admin_user_id ON airtime_admin_actions(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_airtime_admin_actions_action_type ON airtime_admin_actions(action_type);
CREATE INDEX IF NOT EXISTS idx_airtime_admin_actions_target_type ON airtime_admin_actions(target_type);
CREATE INDEX IF NOT EXISTS idx_airtime_admin_actions_created_at ON airtime_admin_actions(created_at DESC);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Trigger for updated_at timestamps
CREATE TRIGGER update_airtime_settings_updated_at BEFORE UPDATE ON airtime_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_network_configurations_updated_at BEFORE UPDATE ON network_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get airtime setting by key
CREATE OR REPLACE FUNCTION get_airtime_setting(setting_key_param VARCHAR(100))
RETURNS JSONB AS $$
DECLARE
    setting_value JSONB;
BEGIN
    SELECT setting_value INTO setting_value
    FROM airtime_settings
    WHERE setting_key = setting_key_param AND is_active = TRUE;
    
    RETURN COALESCE(setting_value, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Function to update airtime setting
CREATE OR REPLACE FUNCTION update_airtime_setting(
    setting_key_param VARCHAR(100),
    setting_value_param JSONB,
    setting_type_param VARCHAR(50),
    description_param TEXT,
    admin_user_id UUID,
    admin_email VARCHAR(255),
    ip_address INET DEFAULT NULL,
    user_agent TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    old_value JSONB;
    setting_exists BOOLEAN;
BEGIN
    -- Check if setting exists
    SELECT setting_value, TRUE INTO old_value, setting_exists
    FROM airtime_settings
    WHERE setting_key = setting_key_param;
    
    IF setting_exists THEN
        -- Update existing setting
        UPDATE airtime_settings
        SET 
            setting_value = setting_value_param,
            description = description_param,
            updated_by = admin_user_id,
            updated_at = NOW()
        WHERE setting_key = setting_key_param;
    ELSE
        -- Insert new setting
        INSERT INTO airtime_settings (
            setting_key, setting_value, setting_type, description, 
            created_by, updated_by
        ) VALUES (
            setting_key_param, setting_value_param, setting_type_param, 
            description_param, admin_user_id, admin_user_id
        );
        old_value := '{}'::jsonb;
    END IF;
    
    -- Log the action
    INSERT INTO airtime_admin_actions (
        admin_user_id, admin_email, action_type, target_type, target_id,
        action_description, old_values, new_values, ip_address, user_agent
    ) VALUES (
        admin_user_id, admin_email, 'update_setting', 'global_setting', setting_key_param,
        'Updated airtime setting: ' || setting_key_param,
        old_value, setting_value_param, ip_address, user_agent
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to get network configuration
CREATE OR REPLACE FUNCTION get_network_config(
    network_id_param VARCHAR(20),
    provider_id_param VARCHAR(50)
)
RETURNS TABLE (
    id UUID,
    network_id VARCHAR(20),
    network_name VARCHAR(100),
    provider_id VARCHAR(50),
    is_enabled BOOLEAN,
    min_amount DECIMAL(10,2),
    max_amount DECIMAL(10,2),
    service_id VARCHAR(50),
    commission_rate DECIMAL(5,4),
    processing_fee DECIMAL(10,2),
    timeout_seconds INTEGER,
    max_retries INTEGER,
    retry_delay_ms INTEGER,
    supported_prefixes JSONB,
    status VARCHAR(20)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        nc.id, nc.network_id, nc.network_name, nc.provider_id,
        nc.is_enabled, nc.min_amount, nc.max_amount, nc.service_id,
        nc.commission_rate, nc.processing_fee, nc.timeout_seconds,
        nc.max_retries, nc.retry_delay_ms, nc.supported_prefixes, nc.status
    FROM network_configurations nc
    WHERE nc.network_id = network_id_param 
    AND nc.provider_id = provider_id_param;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INITIAL DATA
-- =====================================================

-- Insert default global airtime settings
INSERT INTO airtime_settings (setting_key, setting_value, setting_type, description) VALUES
    ('global_limits', '{"min_amount": 50, "max_amount": 50000, "daily_limit": 500000}', 'limits', 'Global transaction limits for airtime purchases'),
    ('security_settings', '{"max_retries": 3, "timeout_seconds": 30, "rate_limit_per_minute": 60}', 'security', 'Security and performance settings'),
    ('commission_settings', '{"default_rate": 0.0000, "admin_fee": 0.00}', 'pricing', 'Default commission and fee settings'),
    ('system_settings', '{"auto_retry": true, "log_level": "info", "maintenance_mode": false}', 'system', 'System-wide airtime service settings')
ON CONFLICT (setting_key) DO NOTHING;

-- Insert default network configurations for VTpass
INSERT INTO network_configurations (
    network_id, network_name, provider_id, service_id, 
    supported_prefixes, min_amount, max_amount
) VALUES
    ('mtn', 'MTN Nigeria', 'vtpass', 'mtn', 
     '["0803", "0806", "0703", "0706", "0813", "0816", "0810", "0814", "0903", "0906", "0913", "0916"]'::jsonb,
     50.00, 50000.00),
    ('glo', 'Glo Nigeria', 'vtpass', 'glo',
     '["0805", "0807", "0705", "0815", "0811", "0905", "0915"]'::jsonb,
     50.00, 50000.00),
    ('airtel', 'Airtel Nigeria', 'vtpass', 'airtel',
     '["0802", "0808", "0708", "0812", "0701", "0902", "0907", "0901", "0904", "0912"]'::jsonb,
     50.00, 50000.00),
    ('etisalat', '9mobile Nigeria', 'vtpass', 'etisalat',
     '["0809", "0818", "0817", "0909", "0908"]'::jsonb,
     50.00, 50000.00)
ON CONFLICT (network_id, provider_id) DO NOTHING;

-- Insert default network configurations for PluginNG
INSERT INTO network_configurations (
    network_id, network_name, provider_id, service_id,
    supported_prefixes, min_amount, max_amount
) VALUES
    ('mtn', 'MTN Nigeria', 'pluginng', '10',
     '["0803", "0806", "0703", "0706", "0813", "0816", "0810", "0814", "0903", "0906", "0913", "0916"]'::jsonb,
     50.00, 50000.00),
    ('glo', 'Glo Nigeria', 'pluginng', '11',
     '["0805", "0807", "0705", "0815", "0811", "0905", "0915"]'::jsonb,
     50.00, 50000.00),
    ('airtel', 'Airtel Nigeria', 'pluginng', '12',
     '["0802", "0808", "0708", "0812", "0701", "0902", "0907", "0901", "0904", "0912"]'::jsonb,
     50.00, 50000.00),
    ('etisalat', '9mobile Nigeria', 'pluginng', '13',
     '["0809", "0818", "0817", "0909", "0908"]'::jsonb,
     50.00, 50000.00)
ON CONFLICT (network_id, provider_id) DO NOTHING;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE airtime_settings IS 'Stores global airtime service configuration settings';
COMMENT ON TABLE network_configurations IS 'Stores network-specific configurations for each provider';
COMMENT ON TABLE airtime_admin_actions IS 'Audit log of all admin configuration changes';

COMMENT ON FUNCTION get_airtime_setting IS 'Retrieves an airtime setting value by key';
COMMENT ON FUNCTION update_airtime_setting IS 'Updates an airtime setting and logs the action';
COMMENT ON FUNCTION get_network_config IS 'Retrieves network configuration for a specific network and provider';
