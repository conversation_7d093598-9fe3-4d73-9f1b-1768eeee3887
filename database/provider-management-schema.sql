-- =====================================================
-- PROVIDER MANAGEMENT SCHEMA
-- =====================================================
-- Database schema for VTU provider management and admin control
-- Supports manual provider switching, configuration, and monitoring
-- 
-- Author: PayVendy Development Team
-- Version: 1.0.0
-- Date: 2025-07-11

-- =====================================================
-- PROVIDER STATUS TABLE
-- =====================================================

-- Table to store provider status and configuration
CREATE TABLE IF NOT EXISTS provider_status (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    provider_id VARCHAR(50) NOT NULL UNIQUE, -- 'vtpass', 'pluginng'
    provider_name VARCHAR(100) NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    is_primary BOOLEAN DEFAULT FALSE,
    priority INTEGER DEFAULT 1, -- 1 = highest priority
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance', 'error')),
    
    -- Configuration settings
    config JSONB DEFAULT '{}'::jsonb,
    
    -- Performance metrics
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    average_response_time INTEGER DEFAULT 0, -- milliseconds
    total_transactions BIGINT DEFAULT 0,
    successful_transactions BIGINT DEFAULT 0,
    failed_transactions BIGINT DEFAULT 0,
    last_transaction_at TIMESTAMP WITH TIME ZONE,
    
    -- Health check
    last_health_check TIMESTAMP WITH TIME ZONE,
    health_status VARCHAR(20) DEFAULT 'unknown' CHECK (health_status IN ('healthy', 'unhealthy', 'unknown')),
    health_message TEXT,
    
    -- Maintenance
    maintenance_mode BOOLEAN DEFAULT FALSE,
    maintenance_start TIMESTAMP WITH TIME ZONE,
    maintenance_end TIMESTAMP WITH TIME ZONE,
    maintenance_reason TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ADMIN ACTIONS LOG TABLE
-- =====================================================

-- Table to log all admin actions on providers
CREATE TABLE IF NOT EXISTS provider_admin_actions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    admin_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    admin_email VARCHAR(255),
    action_type VARCHAR(50) NOT NULL, -- 'enable', 'disable', 'switch_primary', 'update_config', 'maintenance'
    provider_id VARCHAR(50) NOT NULL,
    
    -- Action details
    action_description TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    
    -- Request context
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PROVIDER PERFORMANCE METRICS TABLE
-- =====================================================

-- Table to store detailed provider performance metrics
CREATE TABLE IF NOT EXISTS provider_performance_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    provider_id VARCHAR(50) NOT NULL,
    metric_date DATE NOT NULL,
    hour_of_day INTEGER CHECK (hour_of_day >= 0 AND hour_of_day <= 23),
    
    -- Transaction metrics
    total_requests INTEGER DEFAULT 0,
    successful_requests INTEGER DEFAULT 0,
    failed_requests INTEGER DEFAULT 0,
    timeout_requests INTEGER DEFAULT 0,
    
    -- Performance metrics
    avg_response_time INTEGER DEFAULT 0, -- milliseconds
    min_response_time INTEGER DEFAULT 0,
    max_response_time INTEGER DEFAULT 0,
    
    -- Error breakdown
    auth_errors INTEGER DEFAULT 0,
    validation_errors INTEGER DEFAULT 0,
    network_errors INTEGER DEFAULT 0,
    server_errors INTEGER DEFAULT 0,
    
    -- Financial metrics
    total_amount DECIMAL(15,2) DEFAULT 0.00,
    total_commission DECIMAL(15,4) DEFAULT 0.0000,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(provider_id, metric_date, hour_of_day)
);

-- =====================================================
-- PROVIDER ALERTS TABLE
-- =====================================================

-- Table to store provider alerts and notifications
CREATE TABLE IF NOT EXISTS provider_alerts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    provider_id VARCHAR(50) NOT NULL,
    alert_type VARCHAR(50) NOT NULL, -- 'high_error_rate', 'slow_response', 'service_down', 'low_balance'
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    
    -- Alert details
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    threshold_value DECIMAL(10,2),
    current_value DECIMAL(10,2),
    
    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved')),
    acknowledged_by UUID REFERENCES users(id) ON DELETE SET NULL,
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES
-- =====================================================

-- Provider status indexes
CREATE INDEX IF NOT EXISTS idx_provider_status_provider_id ON provider_status(provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_status_is_enabled ON provider_status(is_enabled);
CREATE INDEX IF NOT EXISTS idx_provider_status_is_primary ON provider_status(is_primary);
CREATE INDEX IF NOT EXISTS idx_provider_status_priority ON provider_status(priority);

-- Admin actions indexes
CREATE INDEX IF NOT EXISTS idx_provider_admin_actions_admin_user_id ON provider_admin_actions(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_provider_admin_actions_provider_id ON provider_admin_actions(provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_admin_actions_action_type ON provider_admin_actions(action_type);
CREATE INDEX IF NOT EXISTS idx_provider_admin_actions_created_at ON provider_admin_actions(created_at DESC);

-- Performance metrics indexes
CREATE INDEX IF NOT EXISTS idx_provider_performance_provider_date ON provider_performance_metrics(provider_id, metric_date DESC);
CREATE INDEX IF NOT EXISTS idx_provider_performance_date_hour ON provider_performance_metrics(metric_date, hour_of_day);

-- Alerts indexes
CREATE INDEX IF NOT EXISTS idx_provider_alerts_provider_id ON provider_alerts(provider_id);
CREATE INDEX IF NOT EXISTS idx_provider_alerts_status ON provider_alerts(status);
CREATE INDEX IF NOT EXISTS idx_provider_alerts_severity ON provider_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_provider_alerts_created_at ON provider_alerts(created_at DESC);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Trigger for updated_at timestamps
CREATE TRIGGER update_provider_status_updated_at BEFORE UPDATE ON provider_status
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_provider_performance_metrics_updated_at BEFORE UPDATE ON provider_performance_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_provider_alerts_updated_at BEFORE UPDATE ON provider_alerts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get active provider
CREATE OR REPLACE FUNCTION get_active_provider()
RETURNS TABLE (
    provider_id VARCHAR(50),
    provider_name VARCHAR(100),
    is_primary BOOLEAN,
    priority INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ps.provider_id,
        ps.provider_name,
        ps.is_primary,
        ps.priority
    FROM provider_status ps
    WHERE ps.is_enabled = TRUE 
    AND ps.status = 'active'
    AND ps.maintenance_mode = FALSE
    ORDER BY ps.is_primary DESC, ps.priority ASC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to switch primary provider
CREATE OR REPLACE FUNCTION switch_primary_provider(
    new_primary_provider_id VARCHAR(50),
    admin_user_id UUID,
    admin_email VARCHAR(255),
    ip_address INET DEFAULT NULL,
    user_agent TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    old_primary_provider VARCHAR(50);
    provider_exists BOOLEAN;
BEGIN
    -- Check if new provider exists and is enabled
    SELECT EXISTS(
        SELECT 1 FROM provider_status 
        WHERE provider_id = new_primary_provider_id 
        AND is_enabled = TRUE 
        AND status = 'active'
    ) INTO provider_exists;
    
    IF NOT provider_exists THEN
        RAISE EXCEPTION 'Provider % does not exist or is not available', new_primary_provider_id;
    END IF;
    
    -- Get current primary provider
    SELECT provider_id INTO old_primary_provider 
    FROM provider_status 
    WHERE is_primary = TRUE;
    
    -- Update all providers to not primary
    UPDATE provider_status SET is_primary = FALSE;
    
    -- Set new primary provider
    UPDATE provider_status 
    SET is_primary = TRUE, updated_at = NOW()
    WHERE provider_id = new_primary_provider_id;
    
    -- Log the action
    INSERT INTO provider_admin_actions (
        admin_user_id, admin_email, action_type, provider_id,
        action_description, old_values, new_values, ip_address, user_agent
    ) VALUES (
        admin_user_id, admin_email, 'switch_primary', new_primary_provider_id,
        'Switched primary provider from ' || COALESCE(old_primary_provider, 'none') || ' to ' || new_primary_provider_id,
        jsonb_build_object('old_primary', old_primary_provider),
        jsonb_build_object('new_primary', new_primary_provider_id),
        ip_address, user_agent
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to enable/disable provider
CREATE OR REPLACE FUNCTION toggle_provider_status(
    target_provider_id VARCHAR(50),
    enable_provider BOOLEAN,
    admin_user_id UUID,
    admin_email VARCHAR(255),
    reason TEXT DEFAULT NULL,
    ip_address INET DEFAULT NULL,
    user_agent TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    old_status BOOLEAN;
    action_desc TEXT;
BEGIN
    -- Get current status
    SELECT is_enabled INTO old_status 
    FROM provider_status 
    WHERE provider_id = target_provider_id;
    
    IF old_status IS NULL THEN
        RAISE EXCEPTION 'Provider % does not exist', target_provider_id;
    END IF;
    
    -- Update provider status
    UPDATE provider_status 
    SET 
        is_enabled = enable_provider,
        status = CASE WHEN enable_provider THEN 'active' ELSE 'inactive' END,
        updated_at = NOW()
    WHERE provider_id = target_provider_id;
    
    -- Prepare action description
    action_desc := CASE 
        WHEN enable_provider THEN 'Enabled provider ' || target_provider_id
        ELSE 'Disabled provider ' || target_provider_id
    END;
    
    IF reason IS NOT NULL THEN
        action_desc := action_desc || '. Reason: ' || reason;
    END IF;
    
    -- Log the action
    INSERT INTO provider_admin_actions (
        admin_user_id, admin_email, action_type, provider_id,
        action_description, old_values, new_values, ip_address, user_agent
    ) VALUES (
        admin_user_id, admin_email, 
        CASE WHEN enable_provider THEN 'enable' ELSE 'disable' END,
        target_provider_id, action_desc,
        jsonb_build_object('enabled', old_status),
        jsonb_build_object('enabled', enable_provider, 'reason', reason),
        ip_address, user_agent
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to update provider performance metrics
CREATE OR REPLACE FUNCTION update_provider_metrics(
    target_provider_id VARCHAR(50),
    response_time INTEGER,
    was_successful BOOLEAN,
    error_type VARCHAR(50) DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    current_date DATE := CURRENT_DATE;
    current_hour INTEGER := EXTRACT(HOUR FROM NOW());
BEGIN
    -- Insert or update hourly metrics
    INSERT INTO provider_performance_metrics (
        provider_id, metric_date, hour_of_day,
        total_requests, successful_requests, failed_requests,
        avg_response_time, min_response_time, max_response_time,
        auth_errors, validation_errors, network_errors, server_errors
    ) VALUES (
        target_provider_id, current_date, current_hour,
        1, 
        CASE WHEN was_successful THEN 1 ELSE 0 END,
        CASE WHEN NOT was_successful THEN 1 ELSE 0 END,
        response_time, response_time, response_time,
        CASE WHEN error_type = 'auth_error' THEN 1 ELSE 0 END,
        CASE WHEN error_type = 'validation_error' THEN 1 ELSE 0 END,
        CASE WHEN error_type = 'network_error' THEN 1 ELSE 0 END,
        CASE WHEN error_type = 'server_error' THEN 1 ELSE 0 END
    )
    ON CONFLICT (provider_id, metric_date, hour_of_day)
    DO UPDATE SET
        total_requests = provider_performance_metrics.total_requests + 1,
        successful_requests = provider_performance_metrics.successful_requests + 
            CASE WHEN was_successful THEN 1 ELSE 0 END,
        failed_requests = provider_performance_metrics.failed_requests + 
            CASE WHEN NOT was_successful THEN 1 ELSE 0 END,
        avg_response_time = (provider_performance_metrics.avg_response_time * provider_performance_metrics.total_requests + response_time) / 
            (provider_performance_metrics.total_requests + 1),
        min_response_time = LEAST(provider_performance_metrics.min_response_time, response_time),
        max_response_time = GREATEST(provider_performance_metrics.max_response_time, response_time),
        auth_errors = provider_performance_metrics.auth_errors + 
            CASE WHEN error_type = 'auth_error' THEN 1 ELSE 0 END,
        validation_errors = provider_performance_metrics.validation_errors + 
            CASE WHEN error_type = 'validation_error' THEN 1 ELSE 0 END,
        network_errors = provider_performance_metrics.network_errors + 
            CASE WHEN error_type = 'network_error' THEN 1 ELSE 0 END,
        server_errors = provider_performance_metrics.server_errors + 
            CASE WHEN error_type = 'server_error' THEN 1 ELSE 0 END,
        updated_at = NOW();
        
    -- Update provider status summary
    UPDATE provider_status 
    SET 
        last_transaction_at = NOW(),
        total_transactions = total_transactions + 1,
        successful_transactions = successful_transactions + 
            CASE WHEN was_successful THEN 1 ELSE 0 END,
        failed_transactions = failed_transactions + 
            CASE WHEN NOT was_successful THEN 1 ELSE 0 END,
        success_rate = CASE 
            WHEN total_transactions + 1 > 0 THEN 
                ROUND(((successful_transactions + CASE WHEN was_successful THEN 1 ELSE 0 END) * 100.0) / (total_transactions + 1), 2)
            ELSE 0 
        END,
        updated_at = NOW()
    WHERE provider_id = target_provider_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INITIAL DATA
-- =====================================================

-- Insert initial provider status records
INSERT INTO provider_status (provider_id, provider_name, is_enabled, is_primary, priority, config) 
VALUES 
    ('vtpass', 'VTpass', TRUE, TRUE, 1, '{"timeout": 30000, "max_retries": 3, "min_amount": 50, "max_amount": 50000}'::jsonb),
    ('pluginng', 'PluginNG', TRUE, FALSE, 2, '{"timeout": 30000, "max_retries": 3, "min_amount": 50, "max_amount": 50000}'::jsonb)
ON CONFLICT (provider_id) DO NOTHING;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE provider_status IS 'Stores VTU provider status, configuration, and performance metrics';
COMMENT ON TABLE provider_admin_actions IS 'Audit log of all admin actions on VTU providers';
COMMENT ON TABLE provider_performance_metrics IS 'Detailed hourly performance metrics for each provider';
COMMENT ON TABLE provider_alerts IS 'Provider alerts and notifications for admin monitoring';

COMMENT ON FUNCTION get_active_provider IS 'Returns the currently active provider based on priority and status';
COMMENT ON FUNCTION switch_primary_provider IS 'Switches the primary provider and logs the action';
COMMENT ON FUNCTION toggle_provider_status IS 'Enables or disables a provider and logs the action';
COMMENT ON FUNCTION update_provider_metrics IS 'Updates provider performance metrics in real-time';
