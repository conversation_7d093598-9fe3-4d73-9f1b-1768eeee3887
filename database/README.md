# Vendy Database Setup Guide

This directory contains all the necessary SQL scripts and documentation for setting up the Vendy VTU application database with Supabase.

## Quick Setup

### Option 1: Complete Setup (Recommended)
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the entire contents of `supabase-setup.sql`
4. Click **Run** to execute the script
5. Restart your Vendy backend server

### Option 2: Using the Setup Script
```bash
cd backend
node scripts/setup-database.js
```

This script will check your database status and provide detailed setup instructions.

## Files Overview

### `supabase-setup.sql`
Complete database setup script that includes:
- All tables (users, sessions, transactions, wallet_transactions, sms_logs)
- Indexes for performance optimization
- Triggers for automatic timestamp updates
- Helper functions for common operations
- Row Level Security (RLS) policies
- Sample data (commented out)

### `schema.sql`
Alternative schema file with the same structure as `supabase-setup.sql`.

### `setup-functions.sql`
Optional RPC functions for programmatic table creation:
- `create_users_table()`
- `create_sessions_table()`
- `create_transactions_table()`
- `setup_vendy_database()` (creates all tables at once)

## Database Tables

### Core Tables

#### `users`
Stores user account information including:
- Personal details (phone, name, email)
- Security data (encrypted PIN, login attempts)
- Account status and balance
- Verification tokens

#### `sessions`
Tracks user authentication sessions:
- JWT token hashes
- Device and IP information
- Session expiration and status

#### `transactions`
Records all VTU transactions:
- Transaction type (airtime, data, electricity, etc.)
- Amount, recipient, and provider
- Status tracking and references
- Metadata for additional information

#### `wallet_transactions`
Audit trail for wallet balance changes:
- Balance before/after each transaction
- Transaction types (credit, debit)
- References to related transactions

#### `sms_logs`
SMS delivery tracking:
- Message content and recipient
- Provider response and status
- Cost tracking and error logging

## Security Features

### Row Level Security (RLS)
All tables have RLS enabled with policies that allow full access to the service role while restricting user access based on authentication.

### Data Encryption
- User PINs are encrypted using bcrypt
- Sensitive data is stored securely
- JWT tokens are hashed before storage

### Audit Trail
- All transactions are logged
- Balance changes are tracked
- User activities are monitored

## Helper Functions

### `get_user_balance(user_uuid)`
Returns the current balance for a specific user.

### `update_user_balance(user_uuid, amount, type, description, reference)`
Safely updates user balance with automatic audit trail creation.

### `cleanup_expired_sessions()`
Removes expired authentication sessions.

### `cleanup_old_sms_logs()`
Removes SMS logs older than 30 days.

## Troubleshooting

### Common Issues

#### "Table not found" errors
- Run the complete setup script in Supabase SQL Editor
- Ensure all tables are created successfully
- Check that RLS policies are properly configured

#### "Function not found" errors
- The application tries to call RPC functions that don't exist
- This is normal if you haven't run `setup-functions.sql`
- The application will fall back to direct table access

#### Connection errors
- Verify your `SUPABASE_URL` and `SUPABASE_SERVICE_ROLE_KEY` environment variables
- Ensure your Supabase project is active
- Check your internet connection

### Verification Steps

1. **Check table creation:**
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
   ```

2. **Verify functions:**
   ```sql
   SELECT routine_name FROM information_schema.routines 
   WHERE routine_schema = 'public' AND routine_type = 'FUNCTION';
   ```

3. **Test connection:**
   ```bash
   node scripts/setup-database.js
   ```

## Environment Variables

Ensure these are set in your `.env` file:

```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Support

If you encounter issues:
1. Check the server logs for specific error messages
2. Run the database setup script for detailed status
3. Verify your Supabase project configuration
4. Ensure all environment variables are correctly set
