/**
 * Airtime Configuration Validation Middleware
 * 
 * Comprehensive validation for airtime configuration management
 * Ensures data integrity and security for all configuration operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { body, param, query } = require('express-validator');

/**
 * Validation for updating airtime settings
 */
const updateAirtimeSettingValidation = [
  param('setting_key')
    .isLength({ min: 1, max: 100 })
    .withMessage('Setting key must be between 1 and 100 characters')
    .matches(/^[a-z_]+$/)
    .withMessage('Setting key must contain only lowercase letters and underscores'),

  body('setting_value')
    .isObject()
    .withMessage('Setting value must be a valid JSON object')
    .custom((value) => {
      // Validate specific setting types
      if (typeof value !== 'object' || value === null) {
        throw new Error('Setting value must be a valid object');
      }
      return true;
    }),

  body('setting_type')
    .isIn(['limits', 'pricing', 'network', 'security', 'system'])
    .withMessage('Setting type must be one of: limits, pricing, network, security, system'),

  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters')
    .trim()
];

/**
 * Validation for updating network configuration
 */
const updateNetworkConfigValidation = [
  param('network_id')
    .isIn(['mtn', 'glo', 'airtel', 'etisalat'])
    .withMessage('Network ID must be one of: mtn, glo, airtel, etisalat'),

  param('provider_id')
    .isIn(['vtpass', 'pluginng'])
    .withMessage('Provider ID must be one of: vtpass, pluginng'),

  body('network_name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Network name must be between 1 and 100 characters')
    .trim(),

  body('is_enabled')
    .optional()
    .isBoolean()
    .withMessage('is_enabled must be a boolean value'),

  body('min_amount')
    .optional()
    .isFloat({ min: 1, max: 100000 })
    .withMessage('Minimum amount must be between 1 and 100,000')
    .toFloat(),

  body('max_amount')
    .optional()
    .isFloat({ min: 50, max: 1000000 })
    .withMessage('Maximum amount must be between 50 and 1,000,000')
    .toFloat()
    .custom((value, { req }) => {
      if (req.body.min_amount && value <= req.body.min_amount) {
        throw new Error('Maximum amount must be greater than minimum amount');
      }
      return true;
    }),

  body('service_id')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Service ID must be between 1 and 50 characters')
    .trim(),

  body('commission_rate')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Commission rate must be between 0 and 1 (0% to 100%)')
    .toFloat(),

  body('processing_fee')
    .optional()
    .isFloat({ min: 0, max: 10000 })
    .withMessage('Processing fee must be between 0 and 10,000')
    .toFloat(),

  body('timeout_seconds')
    .optional()
    .isInt({ min: 5, max: 300 })
    .withMessage('Timeout must be between 5 and 300 seconds')
    .toInt(),

  body('max_retries')
    .optional()
    .isInt({ min: 0, max: 10 })
    .withMessage('Max retries must be between 0 and 10')
    .toInt(),

  body('retry_delay_ms')
    .optional()
    .isInt({ min: 100, max: 30000 })
    .withMessage('Retry delay must be between 100 and 30,000 milliseconds')
    .toInt(),

  body('supported_prefixes')
    .optional()
    .isArray()
    .withMessage('Supported prefixes must be an array')
    .custom((prefixes) => {
      if (!Array.isArray(prefixes)) {
        throw new Error('Supported prefixes must be an array');
      }
      
      for (const prefix of prefixes) {
        if (typeof prefix !== 'string' || !/^\d{4}$/.test(prefix)) {
          throw new Error('Each prefix must be a 4-digit string');
        }
      }
      
      return true;
    }),

  body('status')
    .optional()
    .isIn(['active', 'inactive', 'maintenance'])
    .withMessage('Status must be one of: active, inactive, maintenance')
];

/**
 * Validation for toggling network status
 */
const toggleNetworkStatusValidation = [
  param('network_id')
    .isIn(['mtn', 'glo', 'airtel', 'etisalat'])
    .withMessage('Network ID must be one of: mtn, glo, airtel, etisalat'),

  param('provider_id')
    .isIn(['vtpass', 'pluginng'])
    .withMessage('Provider ID must be one of: vtpass, pluginng'),

  body('enabled')
    .isBoolean()
    .withMessage('Enabled must be a boolean value'),

  body('reason')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Reason must not exceed 500 characters')
    .trim()
];

/**
 * Validation for testing network configuration
 */
const testNetworkConfigValidation = [
  param('network_id')
    .isIn(['mtn', 'glo', 'airtel', 'etisalat'])
    .withMessage('Network ID must be one of: mtn, glo, airtel, etisalat'),

  param('provider_id')
    .isIn(['vtpass', 'pluginng'])
    .withMessage('Provider ID must be one of: vtpass, pluginng'),

  body('test_phone')
    .optional()
    .isMobilePhone('en-NG')
    .withMessage('Test phone must be a valid Nigerian phone number'),

  body('test_amount')
    .optional()
    .isFloat({ min: 50, max: 1000 })
    .withMessage('Test amount must be between 50 and 1,000')
    .toFloat()
];

/**
 * Validation for admin actions log query
 */
const adminActionsLogValidation = [
  query('action_type')
    .optional()
    .isIn(['update_setting', 'update_network', 'toggle_network', 'test_network'])
    .withMessage('Action type must be one of: update_setting, update_network, toggle_network, test_network'),

  query('target_type')
    .optional()
    .isIn(['global_setting', 'network_config'])
    .withMessage('Target type must be one of: global_setting, network_config'),

  query('admin_user_id')
    .optional()
    .isUUID()
    .withMessage('Admin user ID must be a valid UUID'),

  query('page')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Page must be between 1 and 1000')
    .toInt(),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
    .toInt(),

  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),

  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date')
    .custom((value, { req }) => {
      if (req.query.start_date && new Date(value) <= new Date(req.query.start_date)) {
        throw new Error('End date must be after start date');
      }
      return true;
    })
];

/**
 * Validation for getting airtime settings
 */
const getAirtimeSettingsValidation = [
  query('type')
    .optional()
    .isIn(['limits', 'pricing', 'network', 'security', 'system'])
    .withMessage('Type must be one of: limits, pricing, network, security, system'),

  query('active_only')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('Active only must be true or false')
];

/**
 * Validation for getting network configurations
 */
const getNetworkConfigurationsValidation = [
  query('provider_id')
    .optional()
    .isIn(['vtpass', 'pluginng'])
    .withMessage('Provider ID must be one of: vtpass, pluginng'),

  query('network_id')
    .optional()
    .isIn(['mtn', 'glo', 'airtel', 'etisalat'])
    .withMessage('Network ID must be one of: mtn, glo, airtel, etisalat'),

  query('enabled_only')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('Enabled only must be true or false')
];

/**
 * Custom validation for specific setting types
 */
const validateSettingValue = (settingType, settingValue) => {
  switch (settingType) {
    case 'limits':
      if (!settingValue.min_amount || !settingValue.max_amount) {
        throw new Error('Limits setting must include min_amount and max_amount');
      }
      if (settingValue.min_amount >= settingValue.max_amount) {
        throw new Error('min_amount must be less than max_amount');
      }
      break;

    case 'pricing':
      if (settingValue.default_rate !== undefined && (settingValue.default_rate < 0 || settingValue.default_rate > 1)) {
        throw new Error('Default rate must be between 0 and 1');
      }
      break;

    case 'security':
      if (settingValue.max_retries !== undefined && (settingValue.max_retries < 0 || settingValue.max_retries > 10)) {
        throw new Error('Max retries must be between 0 and 10');
      }
      if (settingValue.timeout_seconds !== undefined && (settingValue.timeout_seconds < 5 || settingValue.timeout_seconds > 300)) {
        throw new Error('Timeout must be between 5 and 300 seconds');
      }
      break;

    case 'system':
      if (settingValue.maintenance_mode !== undefined && typeof settingValue.maintenance_mode !== 'boolean') {
        throw new Error('Maintenance mode must be a boolean');
      }
      break;
  }
  return true;
};

module.exports = {
  updateAirtimeSettingValidation,
  updateNetworkConfigValidation,
  toggleNetworkStatusValidation,
  testNetworkConfigValidation,
  adminActionsLogValidation,
  getAirtimeSettingsValidation,
  getNetworkConfigurationsValidation,
  validateSettingValue
};
