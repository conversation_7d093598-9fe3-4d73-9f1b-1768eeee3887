/**
 * International Transaction Security Middleware
 * 
 * Ultra-secure middleware for international airtime/data transactions with enhanced
 * security measures beyond domestic transactions. Includes geo-validation, enhanced
 * fraud detection, stricter rate limiting, and comprehensive compliance checks.
 * 
 * Enhanced Security Features:
 * - Geographic validation and compliance
 * - Enhanced fraud detection with international patterns
 * - Stricter rate limiting for international transactions
 * - Currency and amount validation
 * - Enhanced device fingerprinting
 * - Real-time risk assessment
 * - Compliance with international regulations
 * - Enhanced audit logging
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const crypto = require('crypto');
const rateLimit = require('express-rate-limit');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

/**
 * International Security Class
 * Handles enhanced security for international transactions
 */
class InternationalSecurity {
  constructor() {
    this.supabase = getSupabase();
    this.suspiciousPatterns = new Map();
    this.countryRiskScores = new Map();
    this.deviceFingerprints = new Map();
    
    // Enhanced configuration for international transactions
    this.config = {
      // Stricter limits for international transactions
      maxDailyInternationalAmount: parseFloat(process.env.MAX_DAILY_INTERNATIONAL_AMOUNT) || 500000, // ₦500K
      maxInternationalTransactionAmount: parseFloat(process.env.MAX_INTERNATIONAL_TRANSACTION_AMOUNT) || 50000, // ₦50K
      maxInternationalTransactionsPerHour: parseInt(process.env.MAX_INTERNATIONAL_TRANSACTIONS_PER_HOUR) || 5,
      maxInternationalTransactionsPerDay: parseInt(process.env.MAX_INTERNATIONAL_TRANSACTIONS_PER_DAY) || 20,
      
      // Enhanced fraud detection thresholds
      internationalSuspiciousAmountThreshold: parseFloat(process.env.INTERNATIONAL_SUSPICIOUS_AMOUNT_THRESHOLD) || 20000, // ₦20K
      
      // Geographic restrictions
      allowedCountries: (process.env.ALLOWED_INTERNATIONAL_COUNTRIES || 'GH,CM,RW,SZ,YE').split(','),
      blockedCountries: (process.env.BLOCKED_INTERNATIONAL_COUNTRIES || '').split(',').filter(Boolean),
      
      // Enhanced verification requirements
      requireEnhancedVerification: process.env.REQUIRE_ENHANCED_VERIFICATION_INTERNATIONAL === 'true',
      requireBVNVerification: process.env.REQUIRE_BVN_VERIFICATION_INTERNATIONAL === 'true',
      
      // Risk scoring
      highRiskCountries: (process.env.HIGH_RISK_COUNTRIES || 'YE').split(','),
      mediumRiskCountries: (process.env.MEDIUM_RISK_COUNTRIES || 'SZ').split(','),
    };

    this.initializeCountryRiskScores();
    
    logger.info('✅ [INTERNATIONAL_SECURITY] Enhanced security initialized:', {
      maxDailyAmount: this.config.maxDailyInternationalAmount,
      maxTransactionAmount: this.config.maxInternationalTransactionAmount,
      allowedCountries: this.config.allowedCountries,
      enhancedVerificationRequired: this.config.requireEnhancedVerification
    });
  }

  /**
   * Initialize country risk scores
   */
  initializeCountryRiskScores() {
    // Low risk countries (score: 10-20)
    ['GH', 'RW'].forEach(country => {
      this.countryRiskScores.set(country, 15);
    });

    // Medium risk countries (score: 30-40)
    this.config.mediumRiskCountries.forEach(country => {
      this.countryRiskScores.set(country, 35);
    });

    // High risk countries (score: 50-60)
    this.config.highRiskCountries.forEach(country => {
      this.countryRiskScores.set(country, 55);
    });

    // Default risk score for unlisted countries
    this.defaultRiskScore = 25;
  }

  /**
   * Enhanced rate limiting for international transactions
   */
  createInternationalRateLimit() {
    return rateLimit({
      windowMs: 60 * 60 * 1000, // 1 hour
      max: (req) => {
        // Much stricter limits for international transactions
        const baseLimit = this.config.maxInternationalTransactionsPerHour;
        
        // Reduce limit for unverified users
        if (!req.user?.is_email_verified || !req.user?.is_phone_verified) {
          return Math.floor(baseLimit / 2);
        }
        
        // Further reduce for users without enhanced verification
        if (this.config.requireEnhancedVerification && !req.user?.is_bvn_verified) {
          return Math.floor(baseLimit / 3);
        }
        
        return baseLimit;
      },
      keyGenerator: (req) => {
        return `international_payment:${req.user?.id || 'anonymous'}:${req.ip}`;
      },
      message: {
        success: false,
        message: 'Too many international transaction attempts. Please try again later.',
        code: 'INTERNATIONAL_RATE_LIMIT_EXCEEDED',
        retryAfter: 3600
      },
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        logger.warn('🚫 [INTERNATIONAL_SECURITY] Rate limit exceeded:', {
          userId: req.user?.id,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          url: req.originalUrl
        });

        // Flag for enhanced monitoring
        this.flagSuspiciousActivity(req.user?.id, req.ip, 'international_rate_limit_exceeded');

        res.status(429).json({
          success: false,
          message: 'Too many international transaction attempts. Please try again later.',
          code: 'INTERNATIONAL_RATE_LIMIT_EXCEEDED',
          retryAfter: 3600
        });
      }
    });
  }

  /**
   * Validate international transaction limits
   */
  validateInternationalLimits = async (req, res, next) => {
    try {
      const { amount, country_code } = req.body;
      const userId = req.user?.id;

      if (!amount || isNaN(amount) || amount <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Invalid transaction amount',
          code: 'INVALID_AMOUNT'
        });
      }

      const numAmount = parseFloat(amount);

      // Check single transaction limit (stricter for international)
      if (numAmount > this.config.maxInternationalTransactionAmount) {
        logger.warn('🚫 [INTERNATIONAL_SECURITY] Transaction amount exceeds international limit:', {
          userId,
          amount: numAmount,
          limit: this.config.maxInternationalTransactionAmount,
          country: country_code,
          ip: req.ip
        });

        return res.status(400).json({
          success: false,
          message: `International transaction amount exceeds maximum limit of ₦${this.config.maxInternationalTransactionAmount.toLocaleString()}`,
          code: 'INTERNATIONAL_AMOUNT_LIMIT_EXCEEDED'
        });
      }

      // Check daily international spending limit
      const dailyInternationalSpent = await this.getDailyInternationalSpending(userId);
      if (dailyInternationalSpent + numAmount > this.config.maxDailyInternationalAmount) {
        logger.warn('🚫 [INTERNATIONAL_SECURITY] Daily international spending limit exceeded:', {
          userId,
          currentSpent: dailyInternationalSpent,
          attemptedAmount: numAmount,
          dailyLimit: this.config.maxDailyInternationalAmount,
          country: country_code,
          ip: req.ip
        });

        return res.status(400).json({
          success: false,
          message: `Daily international spending limit of ₦${this.config.maxDailyInternationalAmount.toLocaleString()} would be exceeded`,
          code: 'DAILY_INTERNATIONAL_LIMIT_EXCEEDED',
          currentSpent: dailyInternationalSpent,
          remainingLimit: this.config.maxDailyInternationalAmount - dailyInternationalSpent
        });
      }

      // Flag suspicious amounts (lower threshold for international)
      if (numAmount >= this.config.internationalSuspiciousAmountThreshold) {
        logger.warn('⚠️ [INTERNATIONAL_SECURITY] Suspicious international transaction amount:', {
          userId,
          amount: numAmount,
          threshold: this.config.internationalSuspiciousAmountThreshold,
          country: country_code,
          ip: req.ip
        });

        this.flagSuspiciousActivity(userId, req.ip, 'high_international_amount', {
          amount: numAmount,
          threshold: this.config.internationalSuspiciousAmountThreshold,
          country: country_code
        });
      }

      next();
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_SECURITY] Limit validation error:', error);
      return res.status(500).json({
        success: false,
        message: 'Security validation failed',
        code: 'SECURITY_ERROR'
      });
    }
  };

  /**
   * Enhanced fraud detection for international transactions
   */
  detectInternationalFraud = async (req, res, next) => {
    try {
      const userId = req.user?.id;
      const ip = req.ip;
      const userAgent = req.get('User-Agent');
      const { amount, country_code, operator_id, billersCode } = req.body;

      let riskScore = 0;
      const riskFactors = [];

      // Country-based risk scoring
      const countryRisk = this.countryRiskScores.get(country_code) || this.defaultRiskScore;
      riskScore += countryRisk;
      if (countryRisk > 30) {
        riskFactors.push(`high_risk_country_${country_code}`);
      }

      // Check for blocked countries
      if (this.config.blockedCountries.includes(country_code)) {
        logger.warn('🚫 [INTERNATIONAL_SECURITY] Blocked country transaction attempt:', {
          userId,
          country: country_code,
          ip
        });

        return res.status(403).json({
          success: false,
          message: 'Transactions to this country are currently not allowed',
          code: 'COUNTRY_BLOCKED',
          country: country_code
        });
      }

      // Check for allowed countries
      if (!this.config.allowedCountries.includes(country_code)) {
        logger.warn('🚫 [INTERNATIONAL_SECURITY] Unauthorized country transaction attempt:', {
          userId,
          country: country_code,
          ip
        });

        return res.status(403).json({
          success: false,
          message: 'Transactions to this country are not supported',
          code: 'COUNTRY_NOT_SUPPORTED',
          country: country_code
        });
      }

      // Enhanced verification check
      if (this.config.requireEnhancedVerification) {
        if (!req.user?.is_bvn_verified) {
          riskScore += 25;
          riskFactors.push('no_bvn_verification');
        }
        
        if (!req.user?.is_email_verified || !req.user?.is_phone_verified) {
          riskScore += 20;
          riskFactors.push('incomplete_verification');
        }
      }

      // Check for rapid international transactions
      const recentInternationalTransactions = await this.getRecentInternationalTransactions(userId, 30); // Last 30 minutes
      if (recentInternationalTransactions.length > 2) {
        riskScore += 30;
        riskFactors.push('rapid_international_transactions');
      }

      // Check for suspicious phone number patterns (international)
      if (billersCode && this.isSuspiciousInternationalPhone(billersCode, country_code)) {
        riskScore += 25;
        riskFactors.push('suspicious_international_phone');
      }

      // Check for unusual amount patterns
      if (amount && this.isUnusualInternationalAmount(amount, country_code)) {
        riskScore += 15;
        riskFactors.push('unusual_amount_pattern');
      }

      // Enhanced device fingerprinting
      const deviceFingerprint = this.generateEnhancedDeviceFingerprint(userAgent, req.headers);
      if (!this.isKnownInternationalDevice(userId, deviceFingerprint)) {
        riskScore += 20;
        riskFactors.push('unknown_international_device');
      }

      // Log risk assessment
      logger.info('🔍 [INTERNATIONAL_SECURITY] Fraud risk assessment:', {
        userId,
        ip,
        country: country_code,
        riskScore,
        riskFactors,
        amount
      });

      // Block very high-risk transactions
      if (riskScore >= 80) {
        logger.warn('🚫 [INTERNATIONAL_SECURITY] Very high-risk international transaction blocked:', {
          userId,
          ip,
          country: country_code,
          riskScore,
          riskFactors,
          amount
        });

        return res.status(403).json({
          success: false,
          message: 'Transaction blocked due to security concerns. Please contact support for international transactions.',
          code: 'VERY_HIGH_RISK_INTERNATIONAL_TRANSACTION',
          riskScore
        });
      }

      // Flag high-risk transactions for enhanced monitoring
      if (riskScore >= 50) {
        this.flagSuspiciousActivity(userId, ip, 'high_risk_international', {
          riskScore,
          riskFactors,
          country: country_code,
          amount
        });
      }

      // Store risk score for analytics
      req.internationalRiskScore = riskScore;
      req.internationalRiskFactors = riskFactors;

      next();
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_SECURITY] Fraud detection error:', error);
      next(); // Continue on error to avoid blocking legitimate transactions
    }
  };

  /**
   * Get user's daily international spending
   */
  async getDailyInternationalSpending(userId) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const { data, error } = await this.supabase
        .from('transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('status', 'completed')
        .eq('type', 'international_airtime')
        .gte('created_at', today.toISOString());

      if (error) {
        logger.error('❌ [INTERNATIONAL_SECURITY] Daily spending query error:', error);
        return 0;
      }

      return data.reduce((total, transaction) => total + parseFloat(transaction.amount), 0);
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_SECURITY] Daily spending calculation error:', error);
      return 0;
    }
  }

  /**
   * Get recent international transactions
   */
  async getRecentInternationalTransactions(userId, minutes = 30) {
    try {
      const cutoff = new Date(Date.now() - minutes * 60 * 1000);

      const { data, error } = await this.supabase
        .from('transactions')
        .select('id, amount, created_at')
        .eq('user_id', userId)
        .eq('type', 'international_airtime')
        .gte('created_at', cutoff.toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('❌ [INTERNATIONAL_SECURITY] Recent transactions query error:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('❌ [INTERNATIONAL_SECURITY] Recent transactions error:', error);
      return [];
    }
  }

  /**
   * Check for suspicious international phone patterns
   */
  isSuspiciousInternationalPhone(phone, countryCode) {
    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '');
    
    // Check for sequential numbers
    const sequential = /(\d)\1{4,}/.test(digits); // 5 or more repeated digits
    const ascending = /01234|12345|23456|34567|45678|56789/.test(digits);
    const descending = /98765|87654|76543|65432|54321|43210/.test(digits);
    
    // Check for obviously fake patterns
    const fakePatterns = /1111111|2222222|3333333|4444444|5555555|6666666|7777777|8888888|9999999|0000000/.test(digits);
    
    return sequential || ascending || descending || fakePatterns;
  }

  /**
   * Check for unusual international amount patterns
   */
  isUnusualInternationalAmount(amount, countryCode) {
    const numAmount = parseFloat(amount);
    
    // Very round amounts might be suspicious
    if (numAmount % 10000 === 0 && numAmount >= 20000) {
      return true;
    }
    
    // Amounts ending in many zeros
    if (numAmount % 5000 === 0 && numAmount >= 15000) {
      return true;
    }
    
    return false;
  }

  /**
   * Generate enhanced device fingerprint
   */
  generateEnhancedDeviceFingerprint(userAgent, headers) {
    const fingerprintData = {
      userAgent,
      acceptLanguage: headers['accept-language'],
      acceptEncoding: headers['accept-encoding'],
      connection: headers.connection,
      xForwardedFor: headers['x-forwarded-for'],
      xRealIp: headers['x-real-ip']
    };

    return crypto
      .createHash('sha256')
      .update(JSON.stringify(fingerprintData))
      .digest('hex');
  }

  /**
   * Check if device is known for international transactions
   */
  isKnownInternationalDevice(userId, fingerprint) {
    const userDevices = this.deviceFingerprints.get(`international:${userId}`) || new Set();
    return userDevices.has(fingerprint);
  }

  /**
   * Flag suspicious activity
   */
  flagSuspiciousActivity(userId, ip, type, metadata = {}) {
    const key = `international:${userId}:${ip}:${type}`;
    const existing = this.suspiciousPatterns.get(key) || { count: 0, firstSeen: Date.now() };
    
    existing.count++;
    existing.lastSeen = Date.now();
    existing.metadata = metadata;
    
    this.suspiciousPatterns.set(key, existing);

    logger.warn('⚠️ [INTERNATIONAL_SECURITY] Suspicious international activity flagged:', {
      userId,
      ip,
      type,
      count: existing.count,
      metadata
    });

    // Alert on repeated suspicious activity
    if (existing.count >= 2) {
      logger.error('🚨 [INTERNATIONAL_SECURITY] Repeated suspicious international activity:', {
        userId,
        ip,
        type,
        count: existing.count,
        timespan: Date.now() - existing.firstSeen
      });
    }
  }
}

// Create singleton instance
const internationalSecurity = new InternationalSecurity();

module.exports = {
  validateInternationalLimits: internationalSecurity.validateInternationalLimits,
  detectInternationalFraud: internationalSecurity.detectInternationalFraud,
  createInternationalRateLimit: () => internationalSecurity.createInternationalRateLimit(),
  internationalSecurity
};
