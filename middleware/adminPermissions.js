/**
 * PayVendy Admin Permissions Middleware
 * 
 * Advanced permission system for admin users with granular access control,
 * activity logging, and security features.
 */

const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Admin permission levels and their descriptions
 */
const ADMIN_PERMISSIONS = {
  // User Management
  READ_USERS: 'read_users',                    // View user profiles and data
  WRITE_USERS: 'write_users',                  // Edit user information
  DELETE_USERS: 'delete_users',                // Delete/deactivate users
  MANAGE_PERMISSIONS: 'manage_permissions',     // Manage other admin permissions
  VIEW_SENSITIVE_DATA: 'view_sensitive_data',   // View PINs, tokens, etc.
  
  // Financial Management
  MANAGE_TRANSACTIONS: 'manage_transactions',   // View/modify transactions
  MANAGE_BALANCES: 'manage_balances',          // Modify user balances
  
  // System Administration
  SYSTEM_SETTINGS: 'system_settings',          // Modify system settings
  AUDIT_LOGS: 'audit_logs',                    // View audit logs
  BULK_OPERATIONS: 'bulk_operations',          // Perform bulk operations
  EXPORT_DATA: 'export_data',                 // Export user/system data
  
  // Advanced Features
  IMPERSONATE_USERS: 'impersonate_users',     // Login as other users
  MANAGE_ADMINS: 'manage_admins',              // Create/modify admin accounts
  SYSTEM_MAINTENANCE: 'system_maintenance'     // System maintenance operations
};

/**
 * Log admin activity for security and audit purposes
 */
async function logAdminActivity(adminId, action, targetType, targetId = null, details = {}, req = null) {
  try {
    const supabase = getSupabase();
    
    const logData = {
      admin_id: adminId,
      action,
      target_type: targetType,
      target_id: targetId,
      details,
      ip_address: req ? getClientIP(req) : null,
      user_agent: req ? req.headers['user-agent'] : null,
      created_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('admin_activity_logs')
      .insert([logData]);

    if (error) {
      logger.error('Failed to log admin activity:', error);
    } else {
      logger.info(`Admin activity logged: ${action}`, {
        adminId,
        targetType,
        targetId,
        action
      });
    }
  } catch (error) {
    logger.error('Admin activity logging error:', error);
  }
}

/**
 * Get client IP address from request
 */
function getClientIP(req) {
  return req.headers['x-forwarded-for'] || 
         req.headers['x-real-ip'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null);
}

/**
 * Check if admin has specific permission
 */
async function checkAdminPermission(adminId, permission) {
  try {
    const supabase = getSupabase();
    
    const { data: admin, error } = await supabase
      .from('users')
      .select('admin_permissions, role, is_active')
      .eq('id', adminId)
      .single();

    if (error || !admin) {
      logger.warn('Admin not found for permission check:', { adminId, permission });
      return false;
    }

    if (admin.role !== 'admin' || !admin.is_active) {
      logger.warn('User is not an active admin:', { adminId, role: admin.role, isActive: admin.is_active });
      return false;
    }

    const permissions = admin.admin_permissions || {};
    const hasPermission = permissions[permission] === true;

    logger.info('Permission check result:', {
      adminId,
      permission,
      hasPermission,
      allPermissions: permissions
    });

    return hasPermission;
  } catch (error) {
    logger.error('Permission check error:', error);
    return false;
  }
}

/**
 * Middleware to require specific admin permission
 */
function requirePermission(permission, options = {}) {
  const { 
    logActivity = true, 
    activityAction = null,
    activityTarget = 'system'
  } = options;

  return async (req, res, next) => {
    try {
      if (!req.user || req.user.role !== 'admin') {
        return res.status(403).json({
          status: 'error',
          message: 'Admin access required',
          code: 'ADMIN_REQUIRED'
        });
      }

      const hasPermission = await checkAdminPermission(req.user.id, permission);

      if (!hasPermission) {
        // Log unauthorized access attempt
        await logAdminActivity(
          req.user.id,
          'UNAUTHORIZED_ACCESS_ATTEMPT',
          'permission',
          null,
          { 
            requiredPermission: permission,
            endpoint: req.originalUrl,
            method: req.method
          },
          req
        );

        return res.status(403).json({
          status: 'error',
          message: 'Insufficient permissions for this operation',
          code: 'INSUFFICIENT_PERMISSIONS',
          requiredPermission: permission
        });
      }

      // Log successful permission check if requested
      if (logActivity && activityAction) {
        await logAdminActivity(
          req.user.id,
          activityAction,
          activityTarget,
          null,
          { 
            endpoint: req.originalUrl,
            method: req.method
          },
          req
        );
      }

      // Add permission info to request for later use
      req.adminPermissions = req.adminPermissions || {};
      req.adminPermissions[permission] = true;

      next();
    } catch (error) {
      logger.error('Permission middleware error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Permission check failed',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
}

/**
 * Middleware to require multiple permissions (all must be present)
 */
function requireAllPermissions(permissions, options = {}) {
  return async (req, res, next) => {
    try {
      if (!req.user || req.user.role !== 'admin') {
        return res.status(403).json({
          status: 'error',
          message: 'Admin access required',
          code: 'ADMIN_REQUIRED'
        });
      }

      const permissionChecks = await Promise.all(
        permissions.map(permission => checkAdminPermission(req.user.id, permission))
      );

      const hasAllPermissions = permissionChecks.every(check => check === true);

      if (!hasAllPermissions) {
        await logAdminActivity(
          req.user.id,
          'UNAUTHORIZED_ACCESS_ATTEMPT',
          'permissions',
          null,
          { 
            requiredPermissions: permissions,
            endpoint: req.originalUrl,
            method: req.method
          },
          req
        );

        return res.status(403).json({
          status: 'error',
          message: 'Insufficient permissions for this operation',
          code: 'INSUFFICIENT_PERMISSIONS',
          requiredPermissions: permissions
        });
      }

      // Add all permissions to request
      req.adminPermissions = req.adminPermissions || {};
      permissions.forEach(permission => {
        req.adminPermissions[permission] = true;
      });

      next();
    } catch (error) {
      logger.error('Multiple permissions middleware error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Permission check failed',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
}

/**
 * Middleware to require any of the specified permissions (at least one must be present)
 */
function requireAnyPermission(permissions, options = {}) {
  return async (req, res, next) => {
    try {
      if (!req.user || req.user.role !== 'admin') {
        return res.status(403).json({
          status: 'error',
          message: 'Admin access required',
          code: 'ADMIN_REQUIRED'
        });
      }

      const permissionChecks = await Promise.all(
        permissions.map(permission => checkAdminPermission(req.user.id, permission))
      );

      const hasAnyPermission = permissionChecks.some(check => check === true);

      if (!hasAnyPermission) {
        await logAdminActivity(
          req.user.id,
          'UNAUTHORIZED_ACCESS_ATTEMPT',
          'permissions',
          null,
          { 
            requiredPermissions: permissions,
            endpoint: req.originalUrl,
            method: req.method
          },
          req
        );

        return res.status(403).json({
          status: 'error',
          message: 'Insufficient permissions for this operation',
          code: 'INSUFFICIENT_PERMISSIONS',
          requiredPermissions: permissions
        });
      }

      next();
    } catch (error) {
      logger.error('Any permission middleware error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Permission check failed',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
}

module.exports = {
  ADMIN_PERMISSIONS,
  logAdminActivity,
  checkAdminPermission,
  requirePermission,
  requireAllPermissions,
  requireAnyPermission,
  getClientIP
};
