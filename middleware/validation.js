/**
 * General Validation Middleware
 * 
 * Provides reusable validation functions for API endpoints
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { validationResult } = require('express-validator');
const logger = require('../utils/logger');

/**
 * Generic validation middleware that checks for validation errors
 * and returns appropriate error responses
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value
    }));

    logger.warn('Validation errors:', {
      url: req.originalUrl,
      method: req.method,
      errors: errorMessages,
      ip: req.ip
    });

    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errorMessages
    });
  }

  next();
};

/**
 * Simple validation function for basic field validation
 * @param {Array} validationRules - Array of validation rules
 * @returns {Function} Express middleware function
 */
const validateRequest = (validationRules = []) => {
  return (req, res, next) => {
    const errors = [];

    for (const rule of validationRules) {
      const { field, type, required, message } = rule;
      const value = req.body[field];

      // Check if required field is missing
      if (required && (value === undefined || value === null || value === '')) {
        errors.push({
          field,
          message: message || `${field} is required`,
          value
        });
        continue;
      }

      // Skip type checking if field is not required and not provided
      if (!required && (value === undefined || value === null)) {
        continue;
      }

      // Type validation
      switch (type) {
        case 'string':
          if (typeof value !== 'string') {
            errors.push({
              field,
              message: message || `${field} must be a string`,
              value
            });
          }
          break;
        case 'number':
          if (typeof value !== 'number' || isNaN(value)) {
            errors.push({
              field,
              message: message || `${field} must be a number`,
              value
            });
          }
          break;
        case 'boolean':
          if (typeof value !== 'boolean') {
            errors.push({
              field,
              message: message || `${field} must be a boolean`,
              value
            });
          }
          break;
        case 'object':
          if (typeof value !== 'object' || Array.isArray(value)) {
            errors.push({
              field,
              message: message || `${field} must be an object`,
              value
            });
          }
          break;
        case 'array':
          if (!Array.isArray(value)) {
            errors.push({
              field,
              message: message || `${field} must be an array`,
              value
            });
          }
          break;
        default:
          // No type validation for unknown types
          break;
      }
    }

    if (errors.length > 0) {
      logger.warn('Request validation failed:', {
        url: req.originalUrl,
        method: req.method,
        errors,
        ip: req.ip
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }

    next();
  };
};

/**
 * Validate UUID format
 * @param {string} uuid - UUID string to validate
 * @returns {boolean} True if valid UUID
 */
const isValidUUID = (uuid) => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Validate email format
 * @param {string} email - Email string to validate
 * @returns {boolean} True if valid email
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number format (Nigerian)
 * @param {string} phone - Phone number to validate
 * @returns {boolean} True if valid phone number
 */
const isValidPhone = (phone) => {
  const phoneRegex = /^(\+234|234|0)?[789][01]\d{8}$/;
  return phoneRegex.test(phone);
};

/**
 * Sanitize string input
 * @param {string} input - Input string to sanitize
 * @returns {string} Sanitized string
 */
const sanitizeString = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .substring(0, 1000); // Limit length
};

module.exports = {
  handleValidationErrors,
  validateRequest,
  isValidUUID,
  isValidEmail,
  isValidPhone,
  sanitizeString
};
