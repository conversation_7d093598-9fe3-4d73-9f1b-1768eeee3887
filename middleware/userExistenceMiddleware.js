const realtimeService = require('../services/realtimeService');
const logger = require('../utils/logger');

/**
 * Middleware to check if authenticated user still exists in database
 * This catches cases where user was deleted but still has valid tokens
 */
const checkUserExists = async (req, res, next) => {
  // Only check for authenticated requests
  if (!req.user || !req.user.id) {
    return next();
  }

  const userId = req.user.id;

  try {
    // Check if user still exists in database
    const userExists = await realtimeService.checkUserExists(userId);

    if (!userExists) {
      logger.security('DELETED_USER_DETECTED_IN_REQUEST', {
        userId,
        endpoint: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });

      // Return specific error for deleted user
      return res.status(404).json({
        status: 'error',
        message: 'User account no longer exists. Please sign up again.',
        code: 'USER_DELETED',
        data: {
          userDeleted: true,
          redirectToSignup: true,
          userId: userId
        }
      });
    }

    // User exists, continue with request
    next();

  } catch (error) {
    logger.error('Error checking user existence in middleware:', error);
    
    // On error, allow request to continue to avoid blocking legitimate users
    // The individual route handlers will catch user not found errors
    next();
  }
};

/**
 * Express middleware factory for user existence checking
 * Can be applied to specific routes or globally
 */
const userExistenceMiddleware = {
  // Check user existence for all authenticated requests
  checkUserExists,

  // Check user existence only for critical operations
  checkUserExistsStrict: async (req, res, next) => {
    // Same as checkUserExists but fails closed on errors
    if (!req.user || !req.user.id) {
      return next();
    }

    const userId = req.user.id;

    try {
      const userExists = await realtimeService.checkUserExists(userId);

      if (!userExists) {
        logger.security('DELETED_USER_DETECTED_STRICT', {
          userId,
          endpoint: req.originalUrl,
          method: req.method,
          ip: req.ip
        });

        return res.status(404).json({
          status: 'error',
          message: 'User account no longer exists. Please sign up again.',
          code: 'USER_DELETED',
          data: {
            userDeleted: true,
            redirectToSignup: true,
            userId: userId
          }
        });
      }

      next();

    } catch (error) {
      logger.error('Error in strict user existence check:', error);
      
      // In strict mode, fail closed - return error
      return res.status(500).json({
        status: 'error',
        message: 'Unable to verify user account. Please try again.',
        code: 'USER_VERIFICATION_ERROR'
      });
    }
  }
};

module.exports = userExistenceMiddleware;