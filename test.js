// const axios = require('axios');

// const url = 'https://pluginng.com/api/login';

// const headers = {
//   'Content-Type': 'application/json',
//   'Accept': 'application/json'
// };

// const body = {
//   email: '<EMAIL>',
//   password: '_p-rBS25hXf:7yY'
// };

// axios.post(url, body, { headers })
//   .then(response => {
//     console.log('✅ Login successful!');
//     console.log('Response data:', response.data);
//   })
//   .catch(error => {
//     if (error.response) {
//       console.error('❌ Login failed with status:', error.response.status);
//       console.error('Error data:', error.response.data);
//     } else {
//       console.error('❌ Error:', error.message);
//     }
//   });

const axios = require('axios');

const url = 'https://datastation.com.ng/api/user/';
const token = 'dc3034f8239fd90a846142c925bd16f110b3ec32';

axios.get(url, {
  headers: {
    'Authorization': `Token ${token}`,
    'Content-Type': 'application/json'
  }
})
.then(response => {
  // Pretty-print full data with 2-space indentation
  console.log(JSON.stringify(response.data, null, 2));
})
.catch(error => {
  console.error('Error:', error.response?.data || error.message);
});

