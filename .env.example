# =====================================================
# VENDY BACKEND ENVIRONMENT VARIABLES
# =====================================================
# Copy this file to .env and update with your actual values

# =====================================================
# APPLICATION SETTINGS
# =====================================================
NODE_ENV=development
PORT=3000
API_VERSION=v1

# =====================================================
# SUPABASE DATABASE CONFIGURATION
# =====================================================
# Get these from your Supabase project dashboard
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# =====================================================
# JWT AUTHENTICATION
# =====================================================
# Generate strong random secrets for production
JWT_SECRET=your-super-secure-jwt-secret-key-here-minimum-32-characters
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-here-minimum-32-characters
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# =====================================================
# BREVO EMAIL CONFIGURATION
# =====================================================
# Get your API key from Brevo dashboard
BREVO_API_KEY=your-brevo-api-key-here
BREVO_FROM_EMAIL=<EMAIL>
BREVO_FROM_NAME=Vendy

# Email rate limiting
MAX_EMAIL_OTP_PER_5MIN=3

# =====================================================
# TERMII SMS CONFIGURATION
# =====================================================
# Get your API key from Termii dashboard
TERMII_API_KEY=your-termii-api-key-here
TERMII_SENDER_ID=Vendy
TERMII_BASE_URL=https://v3.api.termii.com

# SMS rate limiting
MAX_SMS_OTP_PER_5MIN=3

# =====================================================
# SECURITY SETTINGS
# =====================================================
# Password hashing rounds (higher = more secure but slower)
BCRYPT_ROUNDS=12

# Account lockout settings
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# OTP expiry time in milliseconds (5 minutes = 300000)
OTP_EXPIRY_TIME=300000

# =====================================================
# RATE LIMITING
# =====================================================
# General API rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =====================================================
# LOGGING
# =====================================================
# Log level: error, warn, info, debug
LOG_LEVEL=info

# =====================================================
# CORS SETTINGS
# =====================================================
# Allowed origins for CORS (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8081

# =====================================================
# PRODUCTION SETTINGS
# =====================================================
# Set to true in production for enhanced security
TRUST_PROXY=false

# Database connection pool settings
DB_POOL_MIN=2
DB_POOL_MAX=10

# =====================================================
# OPTIONAL FEATURES
# =====================================================
# Enable/disable features
ENABLE_SWAGGER_DOCS=true
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=false

# =====================================================
# WEBHOOK SETTINGS (Optional)
# =====================================================
# SendGrid webhook endpoint verification
SENDGRID_WEBHOOK_SECRET=your-webhook-secret-here

# Termii webhook settings
TERMII_WEBHOOK_SECRET=your-termii-webhook-secret-here

# =====================================================
# MONITORING & ANALYTICS (Optional)
# =====================================================
# Application monitoring
SENTRY_DSN=your-sentry-dsn-here

# Analytics
GOOGLE_ANALYTICS_ID=your-ga-id-here

# =====================================================
# BACKUP & MAINTENANCE (Optional)
# =====================================================
# Database backup settings
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# =====================================================
# PLUGINNG API CONFIGURATION (PRIMARY PROVIDER)
# =====================================================
# PluginNG API credentials for primary airtime purchases
# Supports: MTN, GLO, Airtel, 9mobile (Etisalat)
# Login credentials for PluginNG API (tokens expire every 5-10 minutes)
# Get your account from: https://pluginng.com/register
PLUGINNG_EMAIL=<EMAIL>
PLUGINNG_PASSWORD=your-pluginng-password
PLUGINNG_BASE_URL=https://pluginng.com/api

# PluginNG security settings
PLUGINNG_TIMEOUT=30000
PLUGINNG_MAX_RETRIES=3
PLUGINNG_RATE_LIMIT_PER_MINUTE=60
PLUGINNG_MIN_AMOUNT=50
PLUGINNG_MAX_AMOUNT=50000

# =====================================================
# VTPASS API CONFIGURATION (BACKUP PROVIDER)
# =====================================================
# VTpass API credentials for backup airtime/data purchases
# Supports: MTN, GLO, Airtel, 9mobile (Etisalat)
# Used as backup when PluginNG is unavailable
VTPASS_API_KEY=your-vtpass-api-key-here
VTPASS_SECRET_KEY=your-vtpass-secret-key-here
VTPASS_PUBLIC_KEY=your-vtpass-public-key-here
VTPASS_BASE_URL=https://vtpass.com/api
VTPASS_SANDBOX_URL=https://sandbox.vtpass.com/api

# VTpass security settings
VTPASS_TIMEOUT=30000
VTPASS_MAX_RETRIES=3
VTPASS_RATE_LIMIT_PER_MINUTE=60
VTPASS_MIN_AMOUNT=50
VTPASS_MAX_AMOUNT=50000

# =====================================================
# PROVIDER MANAGEMENT CONFIGURATION
# =====================================================
# Manual provider control settings
PROVIDER_MANUAL_CONTROL=true
PROVIDER_AUTO_FAILOVER=false
PROVIDER_HEALTH_CHECK_INTERVAL=30000
PROVIDER_CIRCUIT_BREAKER_THRESHOLD=5
PROVIDER_CIRCUIT_BREAKER_TIMEOUT=300000

# Provider monitoring settings
PROVIDER_METRICS_RETENTION_DAYS=30
PROVIDER_ALERT_CHECK_INTERVAL=60000
PROVIDER_PERFORMANCE_LOG_LEVEL=info

# Admin dashboard settings
ADMIN_PROVIDER_RATE_LIMIT_SWITCH=10
ADMIN_PROVIDER_RATE_LIMIT_CONFIG=20
ADMIN_PROVIDER_RATE_LIMIT_GENERAL=100

# Payment security configuration
MAX_DAILY_PAYMENT_AMOUNT=1000000
MAX_TRANSACTION_AMOUNT=100000
MAX_TRANSACTIONS_PER_HOUR=20
MAX_TRANSACTIONS_PER_DAY=50
SUSPICIOUS_AMOUNT_THRESHOLD=50000
PAYMENT_SIGNATURE_SECRET=your-secure-signature-secret-here

# Geographic and security restrictions
ALLOWED_COUNTRIES=NG
BLOCKED_COUNTRIES=
REQUIRE_DEVICE_VERIFICATION=false
ENABLE_GEO_BLOCKING=false

# =====================================================
# BACKUP VTU PROVIDER CONFIGURATION
# =====================================================
# Secondary provider for failover (to be implemented)
BACKUP_VTU_PROVIDER=none
BACKUP_VTU_API_KEY=your-backup-provider-api-key-here
BACKUP_VTU_SECRET_KEY=your-backup-provider-secret-key-here

# =====================================================
# DEVELOPMENT ONLY
# =====================================================
# These should not be used in production
DEBUG_MODE=false
MOCK_SMS=false
MOCK_EMAIL=false
MOCK_VTPASS=false
