-- Add unique constraints to sessions table to prevent duplicate token hashes
-- This prevents the "multiple rows returned" error when checking token blacklist

-- Add unique constraint on refresh_token_hash
ALTER TABLE sessions 
ADD CONSTRAINT unique_refresh_token_hash UNIQUE (refresh_token_hash);

-- Add unique constraint on access_token_hash  
ALTER TABLE sessions 
ADD CONSTRAINT unique_access_token_hash UNIQUE (access_token_hash);

-- Add index for better performance on token lookups
CREATE INDEX IF NOT EXISTS idx_sessions_refresh_token_hash ON sessions(refresh_token_hash);
CREATE INDEX IF NOT EXISTS idx_sessions_access_token_hash ON sessions(access_token_hash);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id_active ON sessions(user_id, is_active);