-- =====================================================
-- CREATE FCM TOKENS TABLE MIGRATION
-- =====================================================
-- This migration creates the user_fcm_tokens table if it doesn't exist
-- Run this SQL script in your Supabase dashboard SQL Editor

-- Create the user_fcm_tokens table
CREATE TABLE IF NOT EXISTS user_fcm_tokens (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    fcm_token TEXT NOT NULL,
    device_info JSONB,
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (user_id, fcm_token)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_fcm_tokens_user_id ON user_fcm_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_user_fcm_tokens_fcm_token ON user_fcm_tokens(fcm_token);
CREATE INDEX IF NOT EXISTS idx_user_fcm_tokens_last_seen ON user_fcm_tokens(last_seen);

-- Enable Row Level Security
ALTER TABLE user_fcm_tokens ENABLE ROW LEVEL SECURITY;

-- Create RLS policy: Users can only manage their own FCM tokens
CREATE POLICY IF NOT EXISTS "Users can manage own FCM tokens" ON user_fcm_tokens
    FOR ALL
    USING (auth.uid()::text = user_id::text);

-- Grant necessary permissions
GRANT ALL ON user_fcm_tokens TO service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_fcm_tokens TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE user_fcm_tokens IS 'Stores Firebase Cloud Messaging tokens for push notifications';
COMMENT ON COLUMN user_fcm_tokens.user_id IS 'Reference to the user who owns this FCM token';
COMMENT ON COLUMN user_fcm_tokens.fcm_token IS 'Firebase Cloud Messaging token for push notifications';
COMMENT ON COLUMN user_fcm_tokens.device_info IS 'Optional device information (platform, version, etc.)';
COMMENT ON COLUMN user_fcm_tokens.last_seen IS 'Last time this token was used or updated';

SELECT 'FCM tokens table created successfully' AS migration_status;
