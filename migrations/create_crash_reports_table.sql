-- =====================================================
-- CREATE CRASH REPORTS TABLE
-- =====================================================
-- This migration creates the crash_reports table for storing
-- application crash reports and error logs

-- Create crash_reports table
CREATE TABLE IF NOT EXISTS crash_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    error_id VARCHAR(100) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    stack TEXT,
    component_stack TEXT,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    app_version VARCHAR(20) NOT NULL,
    platform VARCHAR(10) NOT NULL CHECK (platform IN ('ios', 'android')),
    device_info JSONB DEFAULT '{}'::jsonb,
    breadcrumbs JSONB DEFAULT '[]'::jsonb,
    severity VARCHAR(20) DEFAULT 'error' CHECK (severity IN ('info', 'warning', 'error', 'fatal')),
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by UUID REFERENCES users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_crash_reports_user_id ON crash_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_crash_reports_platform ON crash_reports(platform);
CREATE INDEX IF NOT EXISTS idx_crash_reports_app_version ON crash_reports(app_version);
CREATE INDEX IF NOT EXISTS idx_crash_reports_timestamp ON crash_reports(timestamp);
CREATE INDEX IF NOT EXISTS idx_crash_reports_created_at ON crash_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_crash_reports_error_id ON crash_reports(error_id);
CREATE INDEX IF NOT EXISTS idx_crash_reports_severity ON crash_reports(severity);
CREATE INDEX IF NOT EXISTS idx_crash_reports_resolved ON crash_reports(resolved);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_crash_reports_platform_version ON crash_reports(platform, app_version);
CREATE INDEX IF NOT EXISTS idx_crash_reports_user_timestamp ON crash_reports(user_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_crash_reports_unresolved ON crash_reports(resolved, created_at) WHERE resolved = FALSE;

-- Create GIN indexes for JSONB columns
CREATE INDEX IF NOT EXISTS idx_crash_reports_device_info_gin ON crash_reports USING GIN (device_info);
CREATE INDEX IF NOT EXISTS idx_crash_reports_breadcrumbs_gin ON crash_reports USING GIN (breadcrumbs);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_crash_reports_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS trigger_update_crash_reports_updated_at ON crash_reports;
CREATE TRIGGER trigger_update_crash_reports_updated_at
    BEFORE UPDATE ON crash_reports
    FOR EACH ROW
    EXECUTE FUNCTION update_crash_reports_updated_at();

-- Create function to automatically set severity based on message content
CREATE OR REPLACE FUNCTION set_crash_report_severity()
RETURNS TRIGGER AS $$
BEGIN
    -- Auto-detect severity based on message content
    IF NEW.message ~* '(fatal|crash|segfault|abort)' THEN
        NEW.severity = 'fatal';
    ELSIF NEW.message ~* '(error|exception|failed|failure)' THEN
        NEW.severity = 'error';
    ELSIF NEW.message ~* '(warning|warn|deprecated)' THEN
        NEW.severity = 'warning';
    ELSE
        NEW.severity = COALESCE(NEW.severity, 'error');
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically set severity
DROP TRIGGER IF EXISTS trigger_set_crash_report_severity ON crash_reports;
CREATE TRIGGER trigger_set_crash_report_severity
    BEFORE INSERT ON crash_reports
    FOR EACH ROW
    EXECUTE FUNCTION set_crash_report_severity();

-- Create view for crash report statistics
CREATE OR REPLACE VIEW crash_report_stats AS
SELECT 
    platform,
    app_version,
    severity,
    COUNT(*) as report_count,
    COUNT(DISTINCT user_id) as affected_users,
    MIN(created_at) as first_occurrence,
    MAX(created_at) as last_occurrence,
    COUNT(CASE WHEN resolved = TRUE THEN 1 END) as resolved_count,
    COUNT(CASE WHEN resolved = FALSE THEN 1 END) as unresolved_count,
    ROUND(
        COUNT(CASE WHEN resolved = TRUE THEN 1 END)::DECIMAL / 
        COUNT(*)::DECIMAL * 100, 2
    ) as resolution_rate
FROM crash_reports
GROUP BY platform, app_version, severity
ORDER BY report_count DESC;

-- Create view for recent critical crashes
CREATE OR REPLACE VIEW recent_critical_crashes AS
SELECT 
    cr.id,
    cr.error_id,
    cr.message,
    cr.platform,
    cr.app_version,
    cr.severity,
    cr.created_at,
    u.email as user_email,
    u.first_name,
    u.last_name,
    cr.device_info->>'model' as device_model,
    cr.device_info->>'systemVersion' as os_version
FROM crash_reports cr
LEFT JOIN users u ON cr.user_id = u.id
WHERE cr.severity IN ('fatal', 'error')
    AND cr.resolved = FALSE
    AND cr.created_at >= NOW() - INTERVAL '24 hours'
ORDER BY cr.created_at DESC;

-- Create function to clean up old crash reports
CREATE OR REPLACE FUNCTION cleanup_old_crash_reports(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM crash_reports 
    WHERE created_at < NOW() - (days_to_keep || ' days')::INTERVAL
        AND resolved = TRUE;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup
    INSERT INTO system_logs (level, message, data, created_at)
    VALUES (
        'info',
        'Cleaned up old crash reports',
        jsonb_build_object(
            'deleted_count', deleted_count,
            'days_to_keep', days_to_keep,
            'cleanup_date', NOW()
        ),
        NOW()
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON crash_reports TO authenticated;
GRANT SELECT ON crash_report_stats TO authenticated;
GRANT SELECT ON recent_critical_crashes TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_crash_reports(INTEGER) TO authenticated;

-- Create system_logs table if it doesn't exist (for cleanup logging)
CREATE TABLE IF NOT EXISTS system_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);

-- Insert initial system log
INSERT INTO system_logs (level, message, data)
VALUES (
    'info',
    'Crash reports table created successfully',
    jsonb_build_object(
        'migration', 'create_crash_reports_table',
        'created_at', NOW()
    )
);

-- Add comments for documentation
COMMENT ON TABLE crash_reports IS 'Stores application crash reports and error logs from mobile clients';
COMMENT ON COLUMN crash_reports.error_id IS 'Unique identifier for the error from the client';
COMMENT ON COLUMN crash_reports.device_info IS 'JSON object containing device information like model, OS version, etc.';
COMMENT ON COLUMN crash_reports.breadcrumbs IS 'JSON array of user actions leading up to the crash';
COMMENT ON COLUMN crash_reports.severity IS 'Severity level: info, warning, error, or fatal';
COMMENT ON VIEW crash_report_stats IS 'Aggregated statistics for crash reports by platform, version, and severity';
COMMENT ON VIEW recent_critical_crashes IS 'View of recent critical crashes that need immediate attention';
