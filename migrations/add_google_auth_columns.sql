-- =====================================================
-- ADD GOOGLE AUTHENTICATION COLUMNS TO USERS TABLE
-- =====================================================
-- Run this migration in your Supabase SQL Editor
-- This adds support for Google OAuth authentication

-- Add Google authentication columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS google_uid VARCHAR(255) UNIQUE,
ADD COLUMN IF NOT EXISTS provider_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS auth_provider VARCHAR(50) DEFAULT 'phone',
ADD COLUMN IF NOT EXISTS picture TEXT,
ADD COLUMN IF NOT EXISTS is_email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS email_verification_token TEXT,
ADD COLUMN IF NOT EXISTS email_verification_expires TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS profile_source VARCHAR(20) DEFAULT 'manual' CHECK (profile_source IN ('manual', 'google', 'email'));

-- Update the phone_number constraint to allow NULL for Google users
ALTER TABLE users ALTER COLUMN phone_number DROP NOT NULL;

-- Add constraint to ensure either phone_number or google_uid is present
ALTER TABLE users ADD CONSTRAINT check_user_identifier 
CHECK (
    (phone_number IS NOT NULL) OR 
    (google_uid IS NOT NULL AND email IS NOT NULL)
);

-- Add constraint for auth_provider values
ALTER TABLE users ADD CONSTRAINT check_auth_provider 
CHECK (auth_provider IN ('phone', 'google', 'email'));

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_users_google_uid ON users(google_uid);
CREATE INDEX IF NOT EXISTS idx_users_auth_provider ON users(auth_provider);
CREATE INDEX IF NOT EXISTS idx_users_is_email_verified ON users(is_email_verified);

-- Update existing users to have auth_provider = 'phone'
UPDATE users SET auth_provider = 'phone' WHERE auth_provider IS NULL;

-- Create email logs table for tracking email delivery
CREATE TABLE IF NOT EXISTS email_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    message_type VARCHAR(50) DEFAULT 'otp' CHECK (message_type IN ('otp', 'verification', 'reset', 'notification')),
    provider_message_id VARCHAR(255),
    status VARCHAR(20) DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'failed', 'bounced')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for email logs
CREATE INDEX IF NOT EXISTS idx_email_logs_user_id ON email_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_email ON email_logs(email);
CREATE INDEX IF NOT EXISTS idx_email_logs_created_at ON email_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);

-- Enable RLS for email logs
ALTER TABLE email_logs ENABLE ROW LEVEL SECURITY;

-- Email logs policies
CREATE POLICY "Users can view own email logs" ON email_logs
    FOR SELECT USING (auth.uid()::text = user_id::text);

-- Function to clean up old email logs (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_email_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM email_logs WHERE created_at < NOW() - INTERVAL '30 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the unique constraint on phone_number to be conditional
-- First drop the existing unique constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_phone_number_key;

-- Create a partial unique index that only applies to non-null phone numbers
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_phone_number_unique 
ON users(phone_number) WHERE phone_number IS NOT NULL;

-- Create a partial unique index for email when it's the primary identifier
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email_unique_google 
ON users(email) WHERE auth_provider = 'google' AND email IS NOT NULL;

COMMENT ON COLUMN users.google_uid IS 'Google Firebase UID for OAuth users';
COMMENT ON COLUMN users.provider_id IS 'OAuth provider user ID';
COMMENT ON COLUMN users.auth_provider IS 'Authentication method: phone, google, or email';
COMMENT ON COLUMN users.picture IS 'User profile picture URL from OAuth provider';
COMMENT ON COLUMN users.is_email_verified IS 'Whether email address has been verified';
COMMENT ON COLUMN users.email_verification_token IS 'Token for email verification';
COMMENT ON COLUMN users.email_verification_expires IS 'Expiration time for email verification token';

-- Migration completed successfully
SELECT 'Google authentication columns added successfully' AS migration_status;
